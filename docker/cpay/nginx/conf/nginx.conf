user  root;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

stream {

    upstream iot_cpay {
        server node1.emqx.io:1883;
    }
    
     # MQTT请求处理
    server {
        listen 18883 ssl;
        proxy_timeout 24h;
        proxy_pass iot_cpay;
        proxy_buffer_size 4k;
        ssl_handshake_timeout 15s;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers AES128-SHA:AES256-SHA:RC4-SHA:DES-CBC3-SHA:RC4-MD5;
        ssl_certificate /etc/nginx/emqx/mqtt_keys/server.crt;
        ssl_certificate_key /etc/nginx/emqx/mqtt_keys/server.key;
    } 
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;
    sendfile        on;
    keepalive_timeout  500s;

    client_max_body_size 2000m;
    client_body_timeout 6000s;

    server {
      listen  80;
      listen  443 ssl;
      server_name  localhost;
      ssl_certificate     /etc/nginx/certificate/custom.pem;
      ssl_certificate_key  /etc/nginx/certificate/custom.key;


      location /emq-dashboard/ {
            proxy_pass http://node1.emqx.io:18083/;
      }


      location /msg-admin/ {
            proxy_pass http://tomcat:8080/msg-admin/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
      }

      location /msg-gateway/ {
            proxy_pass http://tomcat:8080/msg-gateway/;
      }

      location /msg-receive/ {
	    proxy_pass http://tomcat:8080/msg-receive/;
      }

      location /file/ {
            alias /data/iot/common/files/;
      }

    }
}
