<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>msg-center</artifactId>
        <groupId>com.centerm</groupId>
        <version>2.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>msg-admin</artifactId>

    <packaging>war</packaging>
    <description>管理服务</description>

    <dependencies>

        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.centerm</groupId>
            <artifactId>msg-framework</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <!-- 定时任务-->
        <dependency>
            <groupId>com.centerm</groupId>
            <artifactId>msg-quartz</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <!-- 消息中心模块-->
        <dependency>
            <groupId>com.centerm</groupId>
            <artifactId>msg-admin-service</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.18</version>
        </dependency>

        <!--TODO 临时版本 -->
        <dependency>
            <groupId>com</groupId>
            <artifactId>PriviateIoTGWSDK</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/PrivateGateWay-SDK-1.0.jar</systemPath>
        </dependency>

    </dependencies>

    <build>
        <finalName>${artifactId}</finalName>
        <resources>
            <resource>
                <directory>${basedir}/lib</directory>
                <targetPath>BOOT-INF/lib/</targetPath>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.jar</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <!--add 2023-01-04-->
                <excludes>
                    <exclude>static/fonts/**</exclude>
                    <exclude>static/ajax/libs/layui/css/modules/laydate/default/font/**</exclude>
                    <exclude>static/ajax/libs/summernote/font/**</exclude>
                    <exclude>**/*.ico</exclude>
                    <exclude>**/*.ttf</exclude>
                    <exclude>**/*.woff</exclude>
                    <exclude>**/*.eot</exclude>
                </excludes>
                <!--add end -->
            </resource>

            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>static/fonts/**</include>
                    <include>static/ajax/libs/layui/css/modules/laydate/default/font/**</include>
                    <include>static/ajax/libs/summernote/font/**</include>
                    <include>**/*.ico</include>
                    <include>**/*.ttf</include>
                    <include>**/*.woff</include>
                    <include>**/*.eot</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven.war.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>