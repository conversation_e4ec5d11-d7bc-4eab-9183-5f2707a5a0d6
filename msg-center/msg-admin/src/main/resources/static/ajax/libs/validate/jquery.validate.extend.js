$(document).ready(function(){$.validator.setDefaults({submitHandler: function(form) {form.submit();}});//Phone number validation with ID card combined: (^\d{15}$)|(^\d{17}([0-9]|X)$)jQuery.validator.addMethod(“isPhone”,function(value,element){var length = value.length;var phone=/^1[3|4|5|6|7|8][0-9]\d{8}$/;return this.optional(element)||(length == 11 && phone.test(value));},“Please enter a valid 11-digit phone number”);//Telephone number validationjQuery.validator.addMethod(“isTel”,function(value,element){var tel = /^(0\d{2,3}-)?\d{7,8}$/g;//Area code 3 or 4 digits, number 7 or 8 digitsreturn this.optional(element) || (tel.test(value));},“Please enter a valid landline number”);//Name validationjQuery.validator.addMethod(“isName”,function(value,element){var name=/^[\u4e00-\u9fa5]{2,6}$/;return this.optional(element) || (name.test(value));},“Name can only consist of Chinese characters, with a length of 2-4 characters”);//Username validationjQuery.validator.addMethod(“isUserName”,function(value,element){var userName=/^[a-zA-Z0-9]{2,13}$/;return this.optional(element) || (userName).test(value);},‘Please enter only numbers or letters, no special characters’);

//Identity card validation
	jQuery.validator.addMethod("isIdentity",function(value,element){
		var id= /^(\d{15}$|^\d{18}$|^\d{17}(\d|X))$/;
		return this.optional(element) || (id.test(value));
	},"Please enter a correct 15 or 18-digit ID card number, with uppercase X at the end");
//Birthdate validation
	jQuery.validator.addMethod("isBirth",function(value,element){
		var birth = /^(19|20)\d{2}-(1[0-2]|0?[1-9])-(0?[1-9]|[1-2][0-9]|3[0-1])$/;
		return this.optional(element) || (birth).test(value);
	},"Enter the date of birth in the format yyyy-mm-dd");
//Validate if the new and old passwords are the same
	jQuery.validator.addMethod("isdiff",function(){
		var p1=$("#pwdOld").val();
		var p2=$("#pwdNew").val();
		if(p1==p2){
			return false;
		}else{
			return true;
		}
	});
//Validate if the new password and confirm password are the same
	jQuery.validator.addMethod("issame",function(){
		var p3=$("#confirm_password").val();
		var p4=$("#pwdNew").val();
		if(p3==p4){
			return true;
		}else{
			return false;
		}
	});
//Validate basic information form
	$("#basicInfoForm").validate({
		errorElement:'span',
		errorClass:'help-block error-mes',
		rules:{
			name:{
				required:true,
				isName:true
			},
			sex:"required",
			birth:"required",
			mobile:{
				required:true,
				isPhone:true
			},
			email:{
				required:true,
				email:true
			}
		},
		messages:{
			name:{
				required:"Please enter a Chinese name",
				isName:"Name can only consist of Chinese characters"
			},
			sex:{
				required:"Please enter gender"
			},
			birth:{
				required:"Please enter date of birth"
			},
			mobile:{
				required:"Please enter a phone number",
				isPhone:"Please enter a correct 11-digit phone number"
			},
			email:{
				required:"Please enter an email",
				email:"Please enter a valid email address"
			}
		},

		errorPlacement:function(error,element){
			element.next().remove();
			element.closest('.gg-formGroup').append(error);
		},

		highlight:function(element){
			$(element).closest('.gg-formGroup').addClass('has-error has-feedback');
		},
		success:function(label){
			var el = label.closest('.gg-formGroup').find("input");
			el.next().remove();
			label.closest('.gg-formGroup').removeClass('has-error').addClass("has-feedback has-success");
			label.remove();
		},
		submitHandler:function(form){
			alert("Successfully saved!");
		}
	});

//Validate the password modification form
	$("#modifyPwd").validate({
		onfocusout: function(element) { $(element).valid()},
		debug:false, //Indicates whether the form should be submitted directly after validation
		onkeyup:false, //Listen for validation upon release of keys
		rules:{
			pwdOld:{
				required:true,
				minlength:6
			},
			pwdNew:{
				required:true,
				minlength:6,
				isdiff:true,
			},
			confirm_password:{
				required:true,
				minlength:6,
				issame:true,
			}

		},
		messages:{
			pwdOld : {
				required:'Required',
				minlength:$.validator.format('Password length must be greater than 6')
			},
			pwdNew:{
				required:'Required',
				minlength:$.validator.format('Password length must be greater than 6'),
				isdiff:'Original password and new password cannot be the same',

			},
			confirm_password:{
				required:'Required',
				minlength:$.validator.format('Password length must be greater than 6'),
				issame:'New password must match confirmation password',
			}

		},
		errorElement:"mes",
		errorClass:"gg-star",
		errorPlacement: function(error, element)
		{
			element.closest('.gg-formGroup').append(error);

		}
	});
});