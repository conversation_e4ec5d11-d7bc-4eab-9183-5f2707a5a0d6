<head th:fragment="header">
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title></title>
	<meta name="keywords" content="">
	<meta name="description" content="">
	<link rel="shortcut icon" href="favicon.ico">
	<link th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
	<link th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
	<!-- bootstrap-table 表格插件样式 -->
	<link th:href="@{/ajax/libs/bootstrap-table/bootstrap-table.min.css}" rel="stylesheet"/>
	<link th:href="@{/ajax/libs/bootstrap-treetable/bootstrap-treetable.css}" rel="stylesheet"/>
	<link th:href="@{/css/animate.css}" rel="stylesheet"/>
	<link th:href="@{/css/style.css}" rel="stylesheet"/>
	<link th:href="@{/ajax/libs/select/select2.css}" rel="stylesheet"/>
	<link th:href="@{/app/css/app.css}" rel="stylesheet"/>
</head>
<div th:fragment="footer">
	<script th:src="@{/js/jquery.min.js}"></script>
	<script th:src="@{/js/bootstrap.min.js}"></script>

	<!-- bootstrap-table 表格插件 -->
	<script th:src="@{/ajax/libs/bootstrap-table/bootstrap-table.min.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/mobile/bootstrap-table-mobile.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/toolbar/bootstrap-table-toolbar.min.js}"></script>
	<!-- jquery-validate 表单验证插件 -->
	<script th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
	<script th:src="@{/ajax/libs/validate/jquery.validate.extend.js}"></script>
	<!-- jquery-validate 表单树插件 -->
	<script th:src="@{/ajax/libs/bootstrap-treetable/bootstrap-treetable.js}"></script>
	<!-- jquery-export 表格导出插件 -->
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/export/bootstrap-table-export.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/export/tableExport.js}"></script>
	<!-- 遮罩层 -->
	<script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
	<script th:src="@{/ajax/libs/iCheck/icheck.min.js}"></script>
	<script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
	<script th:src="@{/ajax/libs/layui/layui.js}"></script>
	<script th:src="@{/app/js/common.js?v=3.2.0}"></script>
	<script th:src="@{/app/js/app.js?v=3.2.0}"></script>

	<script src="https://kit.fontawesome.com/23b0592455.js" crossorigin="anonymous"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.40/moment-timezone-with-data.min.js"></script>


	<script th:inline="javascript"> var ctx = [[@{/}]]; </script>
</div>