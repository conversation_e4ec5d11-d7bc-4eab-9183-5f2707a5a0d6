<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org"
	xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">

    <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="online-form">
					<div class="select-list">
						<ul>
							<li>
								Address：<input type="text" name="ipaddr"/>
							</li>
							<li>
								Operator：<input type="text" name="loginName"/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;Search</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;Reset</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
	        <div class="btn-group-sm hidden-xs" id="toolbar" role="group">
	            <a class="btn btn-danger btn-del disabled" onclick="javascript:batchForceLogout()" shiro:hasPermission="monitor:online:batchForceLogout">
	                <i class="fa fa-sign-out"></i> Force Logout
	            </a>
	        </div>
	        
	        <div class="col-sm-12 select-table table-striped">
			    <table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>
	</div>
	<div th:include="include :: footer"></div>
	<script th:inline="javascript">
		var forceFlag = [[${@permission.hasPermi('monitor:online:forceLogout')}]];
		var prefix = ctx + "monitor/online";

		$(function() {
		    var options = {
		        url: prefix + "/list",
		        exportUrl: prefix + "/export",
		        sortName: "lastAccessTime",
		        sortOrder: "desc",
		        search: false,
		        escape: true,
		        columns: [{
		            checkbox: true
		        },
		        {
                    title: "ID",
                    formatter: function (value, row, index) {
                 	    return $.table.serialNumber(index);
                    }
                },
		        {
		            field: 'sessionId',
		            title: 'Session ID',
		        },
		        {
		            field: 'loginName',
		            title: 'Login Name',
		            sortable: true
		        },
		        {
		            field: 'deptName',
		            title: 'Institution'
		        },
		        {
		            field: 'ipaddr',
		            title: 'Address'
		        },
		        {
		            field: 'longinLocation',
		            title: 'Location'
		        },
		        {
		            field: 'browser',
		            title: 'Browser'
		        },
		        {
		            field: 'os',
		            title: 'Operation System'
		        },
		        {
		            field: 'status',
		            title: 'Status',
		            align: 'center',
		            formatter: function(value, row, index) {
		                if (value == 'on_line') {
		                    return '<span class="badge badge-primary">Online</span>';
		                } else if (value == 'off_line') {
		                    return '<span class="badge badge-danger">Offline</span>';
		                }
		            }
		        },
		        {
		            field: 'startTimestamp',
		            title: 'Login Date',
		            sortable: true
		        },
		        {
		            field: 'lastAccessTime',
		            title: 'Last Access',
		            sortable: true
		        },
		        {
		            title: 'Operation',
		            align: 'center',
		            formatter: function(value, row, index) {
		                var msg = '<a class="btn btn-danger btn-xs ' + forceFlag + '" href="#" onclick="forceLogout(\'' + row.sessionId + '\')"><i class="fa fa-sign-out"></i>Force Logout</a> ';
		                return msg;
		            }
		        }]
		    };
		    $.table.init(options);
		});

		// 单条强退
		function forceLogout(sessionId) {
		    $.modal.confirm("Are you sure you want to force the selected users to log out?", function() {
		    	var data = { "sessionId": sessionId };
		        $.operate.post(prefix + "/forceLogout", data);
		    })
		}

		// 批量强退
		function batchForceLogout() {
		    var rows = $.table.selectColumns("sessionId");
		    if (rows.length == 0) {
		        $.modal.alertWarning("Please select the user you want to force logout");
		        return;
		    }
		    $.modal.confirm("Are you sure you want to force logout the selected " + rows.length + " entries?", function() {
		        var url = prefix + "/batchForceLogout";
		        var data = { "ids": rows };
		        $.operate.post(url, data);
		    });
		}
	</script>
</body>
</html>