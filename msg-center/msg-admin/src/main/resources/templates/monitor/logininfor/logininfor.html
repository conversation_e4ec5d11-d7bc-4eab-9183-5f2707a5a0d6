<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org"
	xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">

	<div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="logininfor-form">
					<div class="select-list">
						<ul>
							<li>
								<label>Address：</label><input type="text" name="ipaddr"/>
							</li>
							<li>
								<label>Login Name：</label><input type="text" name="loginName"/>
							</li>
							<li>
								<label>Status：</label><select name="status" th:with="type=${@dict.getType('sys_common_status')}">
									<option value="">All</option>
									<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
								</select>
							</li>
							<li class="select-time">
								<label>Login Time： </label>
								<input type="text" class="time-input" id="startTime" placeholder="Start Time" name="params[beginTime]"/>
								<span>-</span>
								<input type="text" class="time-input" id="endTime" placeholder="End Time" name="params[endTime]"/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;Search</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;Reset</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
			<div class="btn-group-sm hidden-xs" id="toolbar" role="group">
				<a class="btn btn-danger btn-del disabled" onclick="$.operate.removeAll()" shiro:hasPermission="monitor:logininfor:remove">
		            <i class="fa fa-remove"></i> Delete
		        </a>
		        <a class="btn btn-danger" onclick="$.operate.clean()" shiro:hasPermission="monitor:logininfor:remove">
	                <i class="fa fa-trash"></i> Clean
	            </a>
	            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="monitor:logininfor:export">
		            <i class="fa fa-download"></i> Export
		        </a>
	        </div>
        
	        <div class="col-sm-12 select-table table-striped">
			    <table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>
	</div>
	
	<div th:include="include :: footer"></div>
	<script th:inline="javascript">
	    var datas = [[${@dict.getType('sys_common_status')}]];
		var prefix = ctx + "monitor/logininfor";
	
		$(function() {
		    var options = {
		        url: prefix + "/list",
		        cleanUrl: prefix + "/clean",
		        removeUrl: prefix + "/remove",
		        exportUrl: prefix + "/export",
		        sortName: "loginTime",
		        sortOrder: "desc",
		        modalName: "Login Information",
		        search: false,
		        showExport: false,
		        escape: true,
		        columns: [{
		            checkbox: true
		        },
		        {
		            field: 'infoId',
		            title: 'ID'
		        },
		        {
		            field: 'loginName',
		            title: 'Login Name',
		            sortable: true
		        },
		        {
		            field: 'ipaddr',
		            title: 'Address'
		        },
		        {
		            field: 'loginLocation',
		            title: 'Location'
		        },
		        {
		            field: 'browser',
		            title: 'Browser'
		        },
		        {
		            field: 'os',
		            title: 'Operation System'
		        },
		        {
		            field: 'status',
		            title: 'Status',
		            align: 'center',
		            formatter: function(value, row, index) {
		            	return $.table.selectDictLabel(datas, value);
		            }
		        },
		        {
		            field: 'msg',
		            title: 'Message'
		        },
		        {
		            field: 'loginTime',
		            title: 'Login Time',
		            sortable: true
		        }]
		    };
		    $.table.init(options);
		});
	</script>
</body>
</html>