<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org"
	xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">
	
	<div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="operlog-form">
					<div class="select-list">
						<ul>
							<li>
								<label>Module： </label><input type="text" name="title"/>
							</li>
							<li>
								<label>Operator： </label><input type="text" name="operName"/>
							</li>
							<li>
								<label>Type： </label><select name="businessType" th:with="type=${@dict.getType('sys_oper_type')}">
									<option value="">All</option>
									<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
								</select>
							</li>
							<li class="select-time">
								<label>Operation Time： </label>
								<input type="text" class="time-input" id="startTime" placeholder="Start Time" name="params[beginTime]"/>
								<span>-</span>
								<input type="text" class="time-input" id="endTime" placeholder="End Time" name="params[endTime]"/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;Search</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;Reset</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
			<div class="btn-group-sm hidden-xs" id="toolbar" role="group">
				<a class="btn btn-danger btn-del disabled" onclick="$.operate.removeAll()" shiro:hasPermission="monitor:logininfor:remove">
		            <i class="fa fa-remove"></i> Delete
		        </a>
		        <a class="btn btn-danger" onclick="$.operate.clean()" shiro:hasPermission="monitor:logininfor:remove">
	                <i class="fa fa-trash"></i> Clean
	            </a>
	            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="monitor:logininfor:export">
		            <i class="fa fa-download"></i> Export
		        </a>
	        </div>
	        
			<div class="col-sm-12 select-table table-striped">
				<table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>
	</div>
	
	<div th:include="include :: footer"></div>
	<script th:inline="javascript">
		var detailFlag = [[${@permission.hasPermi('monitor:operlog:detail')}]];
		var datas = [[${@dict.getType('sys_oper_type')}]];
		var prefix = ctx + "monitor/operlog";

		$(function() {
		    var options = {
		        url: prefix + "/list",
		        cleanUrl: prefix + "/clean",
		        detailUrl: prefix + "/detail/{id}",
		        removeUrl: prefix + "/remove",
		        exportUrl: prefix + "/export",
		        sortName: "operTime",
		        sortOrder: "desc",
		        modalName: "Operation Log",
		        search: false,
		        showExport: false,
		        escape: true,
		        columns: [{
		            checkbox: true
		        },
		        {
		            field: 'operId',
		            title: 'Operation ID'
		        },
		        {
		            field: 'title',
		            title: 'Module'
		        },
		        {
		            field: 'businessType',
		            title: 'Type',
		            align: 'center',
		            formatter: function(value, row, index) {
		            	return $.table.selectDictLabel(datas, value);
		            }
		        },
		        {
		            field: 'operName',
		            title: 'Operator',
		            sortable: true
		        },
		        {
		            field: 'deptName',
		            title: 'Institution'
		        },
		        {
		            field: 'operIp',
		            title: 'Address'
		        },
		        {
		            field: 'operLocation',
		            title: 'Location'
		        },
		        {
		            field: 'status',
		            title: 'Status',
		            align: 'center',
		            formatter: function(value, row, index) {
		                if (value == 0) {
		                    return '<span class="badge badge-primary">Success</span>';
		                } else if (value == 1) {
		                    return '<span class="badge badge-danger">Failure</span>';
		                }
		            }
		        },
		        {
		            field: 'operTime',
		            title: 'Operation Time',
		            sortable: true
		        },
		        {
		            title: 'Operation',
		            align: 'center',
		            formatter: function(value, row, index) {
		                var actions = [];
		                actions.push('<a class="btn btn-warning btn-xs ' + detailFlag + '" href="#" onclick="$.operate.detail(\'' + row.operId + '\')"><i class="fa fa-search"></i>Detail</a>');
		                return actions.join('');
		            }
		        }]
		    };
		    $.table.init(options);
		});
	</script>
</body>
</html>