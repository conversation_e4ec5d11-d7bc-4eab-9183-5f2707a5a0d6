<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<style>
	.qrcodes-info-piece input.form-control {
		width: calc(50% - 40px);
		display: inline-block;
	}
	#qrcodes-info-cont {
		padding-top: 10px;
	}
	.qrcodes-info-piece {
		margin-bottom: 10px;
	}
	.close {
		line-height: 34px;
	}
</style>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-merTerminalBind-edit" th:object="${merTerminalUnBind}">
			<div class="form-group">
				<label class="col-sm-3 control-label">商户编号：</label>
				<div class="col-sm-8">
					<input id="merNo" name="merNo" th:field="*{merNo}" class="form-control" type="text" readonly>
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">门店编号：</label>
				<div class="col-sm-8">
					<input id="storesNo" name="storesNo" th:field="*{storesNo}" class="form-control" type="text" readonly>
				</div>
			</div>
<!--			<div class="form-group">
				<label class="col-sm-3 control-label">设备序列号：</label>
				<div class="col-sm-8">
					<input id="sn" name="sn" th:field="*{sn}" class="form-control" type="text" readonly>
				</div>
			</div>-->
			<div class="form-group">	
				<label class="col-sm-3 control-label">请输入正确序列号并确认绑定：</label>
				<div class="col-sm-8">
					<input id="sn" name="sn"  class="form-control" type="text">
				</div>
			</div>

		</form>
    </div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var prefix = ctx + "msgcenter/merTerminalUnBind";

		$("#form-merTerminalBind-edit").validate({
			rules:{
				sn:{
					required:true
				},
			},
			messages: {
				sn: {
					required: "请输入要绑定的设备序列号！"
				}
			}
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
				//var url = "http://paywxuat.jsrcu.com/yz/gateway/manage/esbManage/bindHorn"; //测试环境
				//var url = " https://zhsd.js96008.com:2443/gateway/manage/esbManage/bindHorn"; //生产环境
				var url = jsnx_url + "gateway/manage/esbManage/bindHorn";
				var bind_data = {
					hornSn:$('#sn').val(),
					mchntCd:$('#merNo').val(),
					shopId:$('#storesNo').val(),
					hornType:"ST"
				};
				bindBank(url,bind_data);
	        }
	    }

		//发送到农信绑定
		var bindBank = function (url, data) {
			var config = {
				url: url,
				type: "post",
				dataType: "json",
				data: JSON.stringify(data),
				beforeSend: function () {
					$.modal.loading("正在处理中，请稍后...");
					$.modal.disable();
				},
				contentType:"application/json",
				success: function (result) {
					if("00"==result.dataCode ){
						//var _url = "http://localhost:18082/msgcenter/merTerminalUnBind/bind";
						//var _url = "http://*************:7073/msgcenter/merTerminalUnBind/bind";//测试环境
						//var _url = "http://st.jsnx.net:18082/msgcenter/merTerminalUnBind/bind";//生产环境
						var _url = ctx + "msgcenter/merTerminalUnBind/bind";
						var bind_data = {
							sn:$('#sn').val(),
							merNo:$('#merNo').val(),
							storesNo:$('#storesNo').val()
						};
						bind(_url,bind_data);
					}else{
						$.modal.msgError("行内绑定失败:"+result.dataMessage);
					}
					$.modal.closeLoading();

				}, error: function (error) {
					$.modal.msgError("行内绑定失败");
					$.modal.closeLoading();
				}
			};
			$.ajax(config);
		};

		//发送到管理系统绑定
		var bind = function (url, data) {
			var config = {
				url: url,
				type: "post",
				dataType: "json",
				data: JSON.stringify(data),
				contentType:"application/json",
				success: function (result) {
					if("response.success"==result.code ){
						$.modal.msgSuccess("绑定成功");
						parent.$("#bootstrap-table").bootstrapTable('refresh', {
							silent: true
						});
						var index = parent.layer.getFrameIndex(window.name);
						parent.layer.close(index);
					}

				}, error: function (error) {
					$.modal.msgError("管理系统绑定失败");
				}
			};
			$.ajax(config);
		};




	</script>
</body>
</html>
