<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-paramTask-add">
			<div class="form-group">	
				<label class="col-sm-3 control-label">作业编号：</label>
				<div class="col-sm-8">
					<input id="jobId" name="jobId" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">终端序列号：</label>
				<div class="col-sm-8">
					<input id="termSeq" name="termSeq" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">下载标识：</label>
				<div class="col-sm-8">
					<input id="dlFlag" name="dlFlag" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">下载开始时间：</label>
				<div class="col-sm-8">
					<input id="dlBeginDate" name="dlBeginDate" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">下载结束时间：</label>
				<div class="col-sm-8">
					<input id="dlEndDate" name="dlEndDate" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">发布时间：</label>
				<div class="col-sm-8">
					<input id="releaseTime" name="releaseTime" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">有效期：</label>
				<div class="col-sm-8">
					<input id="validDate" name="validDate" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">记录创建时间：</label>
				<div class="col-sm-8">
					<input id="recordCreateTime" name="recordCreateTime" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">结果说明：</label>
				<div class="col-sm-8">
					<input id="resultMessage" name="resultMessage" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">文件大小：</label>
				<div class="col-sm-8">
					<input id="size" name="size" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">任务最后一次下发的时间：</label>
				<div class="col-sm-8">
					<input id="updateTime" name="updateTime" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">重发次数：</label>
				<div class="col-sm-8">
					<input id="retryCount" name="retryCount" class="form-control" type="text">
				</div>
			</div>
		</form>
	</div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var prefix = ctx + "msgcenter/paramTask"
		$("#form-paramTask-add").validate({
			rules:{
				xxxx:{
					required:true,
				},
			}
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/add", $('#form-paramTask-add').serialize());
	        }
	    }
	</script>
</body>
</html>
