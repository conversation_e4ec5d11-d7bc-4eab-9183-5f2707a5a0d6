<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-job-add">
        <div class="form-group">
            <label class="col-sm-3 control-label"><span >Task Name</span><span style="color: red; ">*</span>:</label>
            <div class="col-sm-8">
                <input name="jobName" class="form-control" required type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">Target Institution<span style="color: red; ">*</span>：</label>
            <div class="col-sm-8">
                <input id="treeId"  name="deptId" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">Target Group：</label>
            <div class="col-sm-8">
<!--                <select id="groupId" name="groupId" class="form-control m-b" th:with="groups=${@groupService.selectGroupList()}">-->
<!--                    <option value="">None</option>-->
<!--                    <option th:each="group : ${groups}" th:text="${group.groupName}" th:value="${group.id}"></option>-->
<!--                </select>-->
                <select id="groupId" name="groupId" class="form-control m-b">
                </select>
            </div>
        </div>
        <div class="form-group" id="condition-select-div">
            <label class="col-sm-3 control-label"><span>Terminal Set</span><span
                    style="color: red; ">*</span>:</label>
            <div class="col-sm-8">
                <div class="tusn-btn">
                    <button type="button" class="btn btn-info" onclick="selectTerminalList()"><i
                            class="fa fa-plus"></i><span
                    >Select Terminal</span>
                    </button>
                    <button type="button" class="btn btn-primary" onclick="$.table.importExcel('', importTerminal)"><i
                            class="fa fa-upload"></i><span
                    >Import Excel</span>
                    </button>
                    <button type="button" class="btn btn-warning" onclick="deleteTerminal()"><i
                    ></i><span >Delete Selected</span>
                    </button>
                    <button type="button" class="btn btn-danger" onclick="clearTerminal()"><i
                            class="fa fa-remove"></i><span >Clean</span>
                    </button>
                </div>
                <select id="tusnListSelect" multiple readonly class="col-sm-12"
                        style="height: 200px; border-color: #e5e6e7;margin-top: 5px;"></select>
                <input name="tusns" id="tusnsInput" type="hidden">
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>


<script type="text/javascript">
    var prefix = ctx + "msgcenter/changInsJob";

    $("#form-job-add").validate({
        focusCleanup: true,
        rules:{
            jobName:{
                maxlength: 40
            }
        }
    });

    function submitHandler() {
        if ($.validate.form()) {
            if(!$("#tusnsInput").val()) {
                $.modal.msgError(" No terminal selected, please select again");
                return;
            }
            $.operate.save(prefix + "/add", $('#form-job-add').serialize());
        }
    }

    function importTemplate() {
        $.get(ctx + 'msgcenter/device/importTemplate', function (result) {
            if (result.code == web_status.SUCCESS) {
                window.location.href = ctx + "common/download?fileName=" + encodeURI(result.data) + "&delete=" + true;
            } else if (result.code == web_status.WARNING) {
                $.modal.alertWarning(result.msg)
            } else {
                $.modal.alertError(result.msg);
            }
        });
    };

    $.table._option.modalName = " ChangeIns ";
    $.table._option.importUrl = ctx + "msgcenter/device/importDataSns";

    function importTerminal(res) {
        if (res && res.length) {
            var options = '';
            res.forEach(function (item) {
                var hasOption = $("#tusnListSelect option[value=" + item.sn + "]").val();
                if (!hasOption) {
                    options += '<option value="' + item.sn + '">' + item.sn + '</option>';
                }
            });
            $("#tusnListSelect").append(options);
            setSelectAllValue();
        }
    }

    function selectTerminalList() {
        $.modal.open("Select Terminal", ctx + "/msgcenter/device/select");
    }

    function clearTerminal() {
        $("#tusnListSelect").html('');
        $("#tusnsInput").val('');
    }

    function setSelectAllValue() {
        var options = $("#tusnListSelect option").map(function (item) {
            return $(this).val();
        }).get().join(";");
        $("#tusnsInput").val(options);
    }

    function selectedTerminal(rows) {
        var options = '';
        rows.forEach(function (item) {
            var hasOption = $("#tusnListSelect option[value=" + item + "]").val();
            if (!hasOption) {
                options += '<option value="' + item + '">' + item + '</option>';
            }
        });
        $("#tusnListSelect").append(options);
        setSelectAllValue();
    }

    function deleteTerminal() {
        $("#tusnListSelect option:selected").remove();
        setSelectAllValue();
    }

    function releaseTypeChange() {
        var releaseType = $("#releaseType").val();
        var conditionDiv = $("#condition-select-div");
        var deptSelectDiv = $("#dept-select-div");
        if (releaseType == 2) {//条件
            conditionDiv.show();
            deptSelectDiv.hide();
        } else if (releaseType == 1) {//机构
            conditionDiv.hide();
            deptSelectDiv.show();
        } else {
            conditionDiv.hide();
            deptSelectDiv.hide();
        }
    }
    $.tree.selectDeptAndGroup($('#treeId'));
    // $.tree.getTreeName($('#treeId').val());

</script>
</body>
<form id="importForm" enctype="multipart/form-data" class="mt20 mb10" style="display: none">
    <div class="col-xs-offset-1">
        <form id="form_en">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                &nbsp; <a onclick="importTemplate()" class="btn btn-default btn-xs"><i
                    class="fa fa-file-excel-o"></i> <span >Template</span></a>
            </div>
            <font color="red" class="pull-left mt10">
                <span >Note: Only XLS or XLSX file is allowed.</span>
            </font>
        </form>
    </div>
</form>

</html>