<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-appVersion-edit" th:object="${appVersion}">
            <input id="id" name="id" th:field="*{id}"  type="hidden">
			<div class="form-group">	
				<label class="col-sm-3 control-label">软件编号：</label>
				<div class="col-sm-8">
					<input id="appId" name="appId" th:field="*{appId}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">软件名称：</label>
				<div class="col-sm-8">
					<input id="appName" name="appName" th:field="*{appName}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">厂商编号id：</label>
				<div class="col-sm-8">
					<input id="manufacturerId" name="manufacturerId" th:field="*{manufacturerId}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">生产厂商：</label>
				<div class="col-sm-8">
					<input id="producer" name="producer" th:field="*{producer}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">终端类型编号：</label>
				<div class="col-sm-8">
					<input id="termTypeId" name="termTypeId" th:field="*{termTypeId}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">终端型号：</label>
				<div class="col-sm-8">
					<input id="model" name="model" th:field="*{model}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">应用版本号：</label>
				<div class="col-sm-8">
					<input id="appVersion" name="appVersion" th:field="*{appVersion}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">原文件名称：</label>
				<div class="col-sm-8">
					<input id="fileName" name="fileName" th:field="*{fileName}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">版本状态：</label>
				<div class="col-sm-8">
					<input id="versionStatus" name="versionStatus" th:field="*{versionStatus}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">md5：</label>
				<div class="col-sm-8">
					<input id="md5" name="md5" th:field="*{md5}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">app保存路径：</label>
				<div class="col-sm-8">
					<input id="appPath" name="appPath" th:field="*{appPath}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">备注：</label>
				<div class="col-sm-8">
					<input id="remark" name="remark" th:field="*{remark}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">操作标志：</label>
				<div class="col-sm-8">
					<input id="operFlag" name="operFlag" th:field="*{operFlag}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">：</label>
				<div class="col-sm-8">
					<input id="size" name="size" th:field="*{size}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">：</label>
				<div class="col-sm-8">
					<input id="insId" name="insId" th:field="*{insId}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">：</label>
				<div class="col-sm-8">
					<input id="iconPath" name="iconPath" th:field="*{iconPath}" class="form-control" type="text">
				</div>
			</div>
		</form>
    </div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var prefix = ctx + "msgcenter/appVersion"
		$("#form-appVersion-edit").validate({
			rules:{
				xxxx:{
					required:true,
				},
			}
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/edit", $('#form-appVersion-edit').serialize());
	        }
	    }
	</script>
</body>
</html>
