<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">
<div style="padding: 20px">
    <div class="select-table table-striped" style="margin-top: 0">
        <form class="form-horizontal m" id="form-firmware">
            <div class="form-group">
                <label class="col-sm-3 control-label">固件镜像下载地址：</label>
                <div class="col-sm-9">
                    <div class="col-sm-7 no-padding">
                        <input id="downloadUrl" name="downloadUrl" class="form-control" type="text"
                               value="http://admin.91team.tech/images2/AppImg.zip">
                    </div>
                </div>

            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">升级镜像文件Md5值：</label>
                <div class="col-sm-9">
                    <div class="col-sm-7 no-padding">
                        <input id="md5Chk" name="md5Chk" class="form-control" type="text"
                        value="33A81F96A13C9DCE810B7083FD7D5E99">
                    </div>
                </div>

            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">版本码：</label>
                <div class="col-sm-9">
                    <div class="col-sm-7 no-padding">
                        <input id="version" name="version" class="form-control" type="text"
                               value="3">
                    </div>
                </div>

            </div>
            <div class="form-group">
                <div class="col-sm-9 col-sm-offset-3">
                    <button type="button" class="btn btn-primary" onclick="submitHandler()">固件升级</button>
                </div>
            </div>

        </form>
    </div>

</div>
<div th:include="include::footer"></div>
<script th:src="@{/app/js/demo.js}"></script>
<script type="text/javascript">
    var prefix = ctx + "v1/firmware/upgrade"
    $("#form-firmware").validate({
        rules:{
            downloadUrl:{
                required:true,
            },
            md5Chk: {
                required:true,
            },
            version: {
                required:true,
            }
        }
    });

    function submitHandler() {
        if ($.validate.form()) {
            var value = $.common.paramString2obj($('#form-firmware').serialize());
            value = Object.assign({
                nonce: Math.random()
            },value);
            $.operate.saveHeaderJson(prefix, value);
            window.location.href="/msgcenter/demo/firmware";
        }
    }
</script>
</body>
</html>
