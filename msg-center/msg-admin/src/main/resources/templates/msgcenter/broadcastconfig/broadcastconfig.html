<!DOCTYPE HTML>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">

<div class="container-div">
	<div class="row">
		<div class="col-sm-12 search-collapse">
			<form id="formId">
				<div class="select-list">
					<ul>
						<li>
							设备序列号：<input type="text" name="sn"/>
						</li>
						<!--
                                                    <li>
                                                        广告开关：<input type="text" name="advertSwitch"/>
                                                    </li>

                                                    <li>
                                                        播报开关：<input type="text" name="broadcastSwitch"/>
                                                    </li>

                                                    <li>
                                                        播报策略：<input type="text" name="broadcastStrategy"/>
                                                    </li>

                                                    <li>
                                                        防逃单提醒：<input type="text" name="broadcastAlarm"/>
                                                    </li>

                                                    <li>
                                                        个性语音：<input type="text" name="broadcastIndividuality"/>
                                                    </li>-->

						<li>
							播报门店：<input type="text" name="broadcastStore"/>
						</li>

						<li>
							<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
							<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
						</li>
					</ul>
				</div>
			</form>
		</div>

		<!--<div class="btn-group-sm hidden-xs" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="msgcenter:broadcastconfig:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary btn-edit disabled" onclick="$.operate.edit()" shiro:hasPermission="msgcenter:broadcastconfig:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger btn-del btn-del disabled" onclick="$.operate.removeAll()" shiro:hasPermission="msgcenter:broadcastconfig:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="msgcenter:broadcastconfig:export">
                    <i class="fa fa-download"></i> 导出
             </a>
        </div>-->
		<div class="col-sm-12 select-table table-striped">
			<table id="bootstrap-table" data-mobile-responsive="true"></table>
		</div>
	</div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
	var editFlag = [[${@permission.hasPermi('msgcenter:broadcastconfig:edit')}]];
	var removeFlag = [[${@permission.hasPermi('msgcenter:broadcastconfig:remove')}]];
	var commonSwitch = [[${@dict.getType('common_switch')}]];
	var broadcastStrategy = [[${@dict.getType('broadcast_strategy')}]];
	var broadcastIndividuality = [[${@dict.getType('broadcast_individuality')}]];
	var prefix = ctx + "msgcenter/broadcastconfig";

	$(function() {
		var options = {
			url: prefix + "/list",
			createUrl: prefix + "/add",
			updateUrl: prefix + "/edit/{id}",
			removeUrl: prefix + "/remove",
			exportUrl: prefix + "/export",
			modalName: "设备播报配置",
			search: false,
			showExport: true,
			columns: [{
				checkbox: true
			},
				{
					field : 'id',
					title : '编号',
					visible: false
				},
				{
					field : 'sn',
					title : '设备序列号',
					sortable: true
				},
				{
					field : 'advertSwitch',
					title : '广告开关',
					sortable: true,
					formatter: function(value, row, index) {
						return statusTools($.table.selectDictText(commonSwitch, value));
					}
				},
				{
					field : 'broadcastSwitch',
					title : '播报开关',
					sortable: true,
					formatter: function(value, row, index) {
						return statusTools($.table.selectDictText(commonSwitch, value));
					}
				},
				{
					field : 'broadcastStrategy',
					title : '播报策略',
					sortable: true,
					formatter: function(value, row, index) {
						return $.table.selectDictText(broadcastStrategy, value);
					}
				},
				{
					field : 'broadcastAlarm',
					title : '防逃单提醒',
					sortable: true,
					formatter: function(value, row, index) {
						return statusTools($.table.selectDictText(commonSwitch, value));
					}
				},
				{
					field : 'broadcastIndividuality',
					title : '个性语音',
					sortable: true,
					formatter: function(value, row, index) {
						return $.table.selectDictText(broadcastIndividuality, value);
					}
				},
				{
					field : 'broadcastStore',
					title : '播报门店',
					sortable: true
				},
				{
					title: '操作',
					align: 'center',
					formatter: function(value, row, index) {
						var actions = [];
						actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="#" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
//                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="#" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
						return actions.join('');
					}
				}]
		};
		$.table.init(options);
	});

	/* 用户状态显示 */
	function statusTools(value) {
		if (value === '<span>关</span>') {
			return '<i class=\"fa fa-toggle-off text-info fa-2x\"></i> ';
		} else {
			return '<i class=\"fa fa-toggle-on text-info fa-2x\"></i> ';
		}
	}
</script>
</body>
</html>