<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
	<form class="form-horizontal m" id="form-broadcastconfig-edit" th:object="${broadcastconfig}">
		<input id="id" name="id" th:field="*{id}"  type="hidden">
		<div class="form-group">
			<label class="col-sm-3 control-label">Device SN：</label>
			<div class="col-sm-8">
				<input id="sn" name="sn" th:field="*{sn}" class="form-control" type="text">
			</div>
		</div>
<!--		<div class="form-group">-->
<!--			<label class="col-sm-3 control-label">广告开关：</label>-->
<!--			<div class="col-sm-8">-->
<!--				<input id="advertSwitch" name="advertSwitch" th:field="*{advertSwitch}" class="form-control" type="hidden">-->
<!--				<span id="advertSwitch-toggle" class="toggle" data-id="advertSwitch"></span>-->
<!--			</div>-->
<!--		</div>-->
		<!--<div class="form-group">
			<label class="col-sm-3 control-label">播报开关：</label>
			<div class="col-sm-8">
				<input id="broadcastSwitch" name="broadcastSwitch" th:field="*{broadcastSwitch}" type="hidden">
				<span id="broadcastSwitch-toggle" class="toggle" data-id="broadcastSwitch"></span>
			</div>
		</div>-->
		<div class="form-group">
			<label class="col-sm-3 control-label">Broadcast Strategy：</label>
			<div class="col-sm-8">
				<select id="broadcastStrategy" name="broadcastStrategy" th:field="*{broadcastStrategy}" class="form-control" th:with="type=${@dict.getType('broadcast_strategy')}">
					<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
				</select>
			</div>
		</div>
		<!--<div class="form-group">
			<label class="col-sm-3 control-label">防逃单提醒：</label>
			<div class="col-sm-8">
				<input id="broadcastAlarm" name="broadcastAlarm" th:field="*{broadcastAlarm}" class="form-control" type="hidden">
				<span id="broadcastAlarm-toggle" class="toggle" data-id="broadcastAlarm"></span>
			</div>
		</div>-->
		<!--<div class="form-group">
			<label class="col-sm-3 control-label">个性语音：</label>
			<div class="col-sm-8">
				<select id="broadcastIndividuality" name="broadcastIndividuality" th:field="*{broadcastIndividuality}" class="form-control" th:with="type=${@dict.getType('broadcast_individuality')}">
					<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
				</select>
			</div>
		</div>-->
		<!--<div class="form-group">
			<label class="col-sm-3 control-label">播报门店：</label>
			<div class="col-sm-8">
				<input id="broadcastStore" name="broadcastStore" th:field="*{broadcastStore}" class="form-control" type="text">
			</div>
		</div>-->
	</form>
</div>
<div th:include="include::footer"></div>
<script type="text/javascript">
	var prefix = ctx + "msgcenter/broadcastconfig";
	$(function () {
		$('.toggle').each(function () {
			statusTools($(this).attr('data-id'));
		});
	})
	$("#form-broadcastconfig-edit").validate({
		rules:{
			xxxx:{
				required:true,
			},
		}
	});

	function submitHandler() {
		if ($.validate.form()) {
			$.operate.save(prefix + "/edit", $('#form-broadcastconfig-edit').serialize());
		}
	}

	function statusTools(id) {
		var str = '';
		if ($('#'+id).val() == 0) {
			str = '<i class=\"fa fa-toggle-off text-info fa-2x\" onclick="setToggleValue(\'' + id + '\', 1 )"></i> ';
		} else {
			str = '<i class=\"fa fa-toggle-on text-info fa-2x\" onclick="setToggleValue(\'' + id + '\', 0)"></i> ';
		}
		$('#'+id).next('.toggle').html(str);
	}

	function setToggleValue(id, value) {
		$('#'+id).val(value);
		statusTools(id)
	}
</script>
</body>
</html>