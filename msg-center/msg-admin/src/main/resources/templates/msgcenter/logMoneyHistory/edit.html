<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-logMoneyHistory-edit" th:object="${logMoneyHistory}">
            <input id="id" name="id" th:field="*{id}"  type="hidden">
			<div class="form-group">	
				<label class="col-sm-3 control-label">设备序列号：</label>
				<div class="col-sm-8">
					<input id="sn" name="sn" th:field="*{sn}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">流水号：</label>
				<div class="col-sm-8">
					<input id="nonce" name="nonce" th:field="*{nonce}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">付款时间：</label>
				<div class="col-sm-8">
					<input id="paidTime" name="paidTime" th:field="*{paidTime}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">下发时间：</label>
				<div class="col-sm-8">
					<input id="createTime" name="createTime" th:field="*{createTime}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">付款渠道：</label>
				<div class="col-sm-8">
					<input id="channel" name="channel" th:field="*{channel}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">金额：</label>
				<div class="col-sm-8">
					<input id="money" name="money" th:field="*{money}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">播报状态：</label>
				<div class="col-sm-8">
					<input id="status" name="status" th:field="*{status}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">备注信息：</label>
				<div class="col-sm-8">
					<input id="remark" name="remark" th:field="*{remark}" class="form-control" type="text">
				</div>
			</div>
		</form>
    </div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var prefix = ctx + "msgcenter/logMoneyHistory"
		$("#form-logMoneyHistory-edit").validate({
			rules:{
				xxxx:{
					required:true,
				},
			}
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/edit", $('#form-logMoneyHistory-edit').serialize());
	        }
	    }
	</script>
</body>
</html>
