<!DOCTYPE HTML>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<style>
	.inp-label {
		min-width: 65px;
		display: inline-block;
	}
</style>
<body class="gray-bg">
    
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="formId">
					<div class="select-list">
						<ul>
							<li>
								<span class="inp-label">设备序列号</span>：<input type="text" name="sn"/>
							</li>

							<li>
								<span class="inp-label">生产厂商</span>：<select name="producer" th:with="type=${@dict.getType('producer_list')}">
								<option value="">所有</option>
								<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
							</select>
							</li>

							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			<div class="btn-group-sm hidden-xs" id="toolbar" role="group">
				<a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="msgcenter:downloadTask:export">
					<i class="fa fa-download"></i> 导出
				</a>
				<!--<a class="btn btn-success" th:href="@{'/msgcenter/merchant'}">
					<i class="fa fa-undo"></i> 返回列表
				</a>-->
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>
	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">
        var prefix = ctx + "msgcenter/device";

		function queryParams(params) {
			return {
				merchantId:       /*<![CDATA[*/[[${merchantId}]]/*]]>*/,
				pageSize:       params.limit,
				pageNum:        params.offset / params.limit + 1,
				searchValue:    params.search,
				orderByColumn:  params.sort,
				isAsc:          params.order
			};
		}

		var deviceStatus = [[${@dict.getType('t_device_status')}]];
		var deviceType = [[${@dict.getType('t_device_type')}]];
		var producerList = [[${@dict.getType('producer_list')}]];
        $(function() {
            var options = {
                url: prefix + "/list",
                modalName: "设备",
				search: false,
		        showExport: false,
				showImport: false,
				queryParams: queryParams,
                columns: [{
		            checkbox: true
		        },
				{
					field : 'id', 
					title : '编号',
					visible: false
				},
				{
					field : 'sn', 
					title : '设备序列号',
					sortable: true
				},
				{
					field : 'type', 
					title : '类型',
					sortable: true,
					formatter: function(value, row, index) {
						return $.table.selectDictText(deviceType, value);
					}
				},
				{
					field : 'status', 
					title : '设备状态',
					sortable: true,
                    formatter: function(value, row, index) {
						return $.table.selectDictLabel(deviceStatus, value);
                    }
				},
				{
					field : 'producer', 
					title : '生产厂商',
					sortable: true,
					formatter: function(value, row, index) {
						return $.table.selectDictText(producerList, value);
					}
				},
				{
					field : 'model',
					title : '设备型号',
					sortable: true
				},
				{
					field : 'deptName',
					title : '机构',
					sortable: true,
					formatter:function (value, row, index) {
						var actions = [];
						if(value == "" || value == null){
							value = "-";
						}
						actions.push("<span>" + value + "</span>");
						return actions.join('');
					}
				},
				{
					field : 'groupName',
					title : '组别',
					sortable: true,
					formatter:function (value, row, index) {
						var actions = [];
						if(value == "" || value == null){
							value = "-";
						}
						actions.push("<span>" + value + "</span>");
						return actions.join('');
					}
				},
				{
					field : 'os', 
					title : '操作系统',
					sortable: true,
					visible: false
				},
				{
					field : 'version', 
					title : '软件版本',
					sortable: true,
					visible: false
				},
				{
					field : 'createTime',
					title : '创建时间',
					sortable: true
				}]
            };
            $.table.init(options);
        });

		function submitHandler() {
			var rows = $.table.selectColumns("sn");
			$.modal.close();
			window.parent.addDeviceList(rows);
			$.modal.enable();
		}

    </script>
</body>
</html>