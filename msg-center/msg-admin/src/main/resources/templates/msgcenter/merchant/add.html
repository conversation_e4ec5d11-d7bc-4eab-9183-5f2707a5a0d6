<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-merchant-add">
			<div class="form-group">
				<label class="col-sm-3 control-label">商户名称：</label>
				<div class="col-sm-8">
					<input id="name" name="name" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">商户地址：</label>
				<div class="col-sm-8">
					<input id="address" name="address" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">商户联系人：</label>
				<div class="col-sm-8">
					<input id="applyerName" name="applyerName" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">商户手机号：</label>
				<div class="col-sm-8">
					<input id="applyerPhone" name="applyerPhone" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">二维码信息：</label>
				<input id="qrcodes" name="qrcodes" type="hidden">
				<div class="col-sm-8" id="qrcodes-info">
					<button class="btn btn-primary btn-sm" id="addQrcodes" type="button">添加二维码</button>
					<div id="qrcodes-info-cont">


					</div>
				</div>

			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">行业：</label>
				<div class="col-sm-8">
					<select id="bussType" name="bussType"  class="form-control" th:with="type=${@dict.getType('buss_type')}">
						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
					</select>
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">备注：</label>
				<div class="col-sm-8">
					<input id="remark" name="remark" class="form-control" type="text">
				</div>
			</div>
		</form>
	</div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var prefix = ctx + "msgcenter/merchant"

        var resetIndex = function () {
            $('#qrcodes-info-cont .qrcodes-info-piece').each(function () {
                $(this).find('span').html(($(this).index() + 1) + '、');
            })
        }

        $('#addQrcodes').on('click', function () {
            var item = $('<div class="qrcodes-info-piece"><span>'+($('.qrcodes-info-piece').length + 1)+'、</span>' +
                '<input type="text" class="form-control desc" placeholder="码名称" style="margin-right: 5px"/>' +
                '<input type="text" class="form-control type"  placeholder="码编号"/>' +
                '<div class="close"><i class="fa fa-remove"></i> </div></div>').appendTo($('#qrcodes-info-cont'));
            item.find('.close').on('click', function () {
                item.remove();
                resetIndex();
            });
        });

		$("#form-merchant-add").validate({
			rules:{
				applyerPhone:{
					required:true,
				},
			}
		});
		
		function submitHandler() {
            var list = [];
            $('#qrcodes-info-cont .qrcodes-info-piece').each(function () {
                list.push({
                    desc: $(this).find('.desc').val(),
                    type: $(this).find('.type').val()
                });
            });
            $('#qrcodes').val(JSON.stringify(list));
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/add", $('#form-merchant-add').serialize());
	        }
	    }
	</script>
</body>
</html>
