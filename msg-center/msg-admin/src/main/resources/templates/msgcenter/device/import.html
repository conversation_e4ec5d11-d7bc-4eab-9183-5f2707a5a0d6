<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="importForm">

        <div class="form-group">
            <label class="col-sm-3 control-label">Type<span style="color: red; ">*</span>：</label>
            <div class="col-sm-8">
                <select id="type" name="type"  class="form-control m-b" th:with="type=${@dict.getType('t_device_type')}">
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <!--			<div class="form-group">-->
        <!--				<label class="col-sm-3 control-label">Status：</label>-->
        <!--				<div class="col-sm-8">-->
        <!--					<select id="status" name="status"  class="form-control m-b" th:with="type=${@dict.getType('t_device_status')}">-->
        <!--						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>-->
        <!--					</select>-->
        <!--				</div>-->
        <!--			</div>-->
        <div class="form-group">
            <label class="col-sm-3 control-label">Manufacturer<span style="color: red; ">*</span>：</label>
            <div class="col-sm-8">
                <select id="producer" name="producer"  class="form-control m-b" th:with="type=${@dict.getType('producer_list')}">
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">Model<span style="color: red; ">*</span>：</label>
            <div class="col-sm-8">
                <select id="model" name="model"  class="form-control m-b" th:with="type=${@dict.getType('model_list')}">
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">IoT Services<span style="color: red; ">*</span>：</label>
            <div class="col-sm-8">
                <select id="iotType" name="iotType"  class="form-control m-b" th:with="type=${@dict.getType('t_iot_type')}">
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
<!--        <div class="form-group">-->
<!--            <label class="col-sm-3 control-label">IoT Product Key<span style="color: red; ">*</span>：</label>-->
<!--            <div class="col-sm-8">-->
<!--                <select id="productKey" name="productKey"  class="form-control m-b" th:with="type=${@dict.getType('t_iot_key')}">-->
<!--                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>-->
<!--                </select>-->
<!--            </div>-->
<!--        </div>-->

        <div class="form-group">
            <label class="col-sm-3 control-label">Institution<span style="color: red; ">*</span>：</label>
            <div class="col-sm-8">
                <input id="treeId"  name="deptId" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">Group：</label>
            <div class="col-sm-8">
                <select id="groupId" name="groupId" class="form-control m-b" th:with="groups=${@groupService.selectGroupList()}">
                    <option value="">None</option>
                    <option th:each="group : ${groups}" th:text="${group.groupName}" th:value="${group.id}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">Remark：</label>
            <div class="col-sm-8">
                <input id="remark" name="remark" class="form-control" type="text">
            </div>
        </div>

        <div class="form-group">
            <div class="col-sm-3"></div>
            <div class="col-sm-8">
                <input type="file" id="file" name="file"/>
                <div class="mt10 pt5">
                    &nbsp; <a onclick="importTemplate()" class="btn btn-default btn-xs"><i
                        class="fa fa-file-excel-o"></i> <span >Template</span></a>
                </div>
                <font color="red" class="pull-left mt10">
                    <span >Note: Only XLS or XLSX file is allowed.</span>
                </font>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script type="text/javascript">
    var prefix = ctx + "msgcenter/device";

   function importTemplate() {
       $.get(prefix + '/importTemplate', function (result) {
           if (result.code == web_status.SUCCESS) {
               window.location.href = ctx + "common/download?fileName=" + encodeURI(result.data) + "&delete=" + true;
           } else if (result.code == web_status.WARNING) {
               $.modal.alertWarning(result.msg)
           } else {
               $.modal.alertError(result.msg);
           }
       });
  };

   $.tree.selectDeptAndGroup($('#treeId'));

  $("#importForm").validate({
      rules:{
          model:{
              required:true,
          },
          type:{
              required:true,
          },
          treeId:{
              required:true,
          }
      }
  });
  function submitHandler() {
      if ($.validate.form()) {
          var formData = new FormData();
          formData.append("file", $('#file')[0].files[0]);
          if (!$('#file')[0].files[0]) {
              $.modal.alertError("Select XLS or xlsx File");
              return;
          }
          var file1Data = $('#file1')[0];
          if (file1Data) {
              formData.append("file1", file1Data.files[0]);
          }

          if ($('#groupId').val()) {
              formData.append("groupId", $('#groupId').val());
          }
          formData.append("type", $('#type').val());
          formData.append("deptId", $("input[name='deptId']").val());
          formData.append("model", $('#model').val());
          formData.append("producer", $('#producer').val());
          formData.append("remark", $('#remark').val());
          $.ajax({
              url: prefix + '/importData',
              data: formData,
              cache: false,
              contentType: false,
              processData: false,
              timeout: 120000,
              type: 'POST',
              success: function (result) {
                  if (result.data) {
                      if (!result.code == 'response.success') {
                          $.modal.msgSuccess(data);
                          $.modal.close();
                          $.table.refresh();
                      } else {
                          $.modal.alertError(result.msg);
                          $.modal.close();
                          $.table.refresh();
                      }
                  } else {
                      $.modal.alertError(result.msg);
                  }
              },
          });
      }
  }


</script>
</body>
</html>