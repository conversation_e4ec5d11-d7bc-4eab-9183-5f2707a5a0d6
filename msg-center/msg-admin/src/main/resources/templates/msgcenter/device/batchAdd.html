<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-device-add">
			<div class="form-group">
				<label class="col-sm-3 control-label">Device SN Header<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<input id="snHead" name="snHead" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Device SN Range<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<input id="snStart" name="snStart" class="form-control short" placeholder="Prefix" type="text">
					<span>----</span>
					<input id="snEnd" name="snEnd" class="form-control short" placeholder="Suffix" type="text">
				</div>
                <label class="col-sm-3"></label>
                <div class="col-sm-8">
                    <span style="color:blue">If importing terminals D1V01600000-D1V01600005,<br/> then the serial header is D1V01600, start position 000, end position 005.</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">SN Length<span style="color: red; ">*</span>：</label>
                <div class="col-sm-8">
                    <input id="snLength" name="snLength" class="form-control" type="text">
                </div>
            </div>
<!--            <div class="form-group">-->
<!--                <label class="col-sm-3 control-label">Product Key：</label>-->
<!--                <div class="col-sm-8">-->
<!--                    <input id="productKey" name="productKey" class="form-control" type="text">-->
<!--                </div>-->
<!--            </div>-->
<!--			<div class="form-group">-->
<!--				<label class="col-sm-3 control-label">Update existing device ：</label>-->
<!--				<div class="col-sm-8">-->
<!--					<input type="checkbox" id="updateSupport" name="updateSupport" title="如果设备已经存在，更新这条数据。">-->
<!--				</div>-->
<!--			</div>-->
<!--			<div class="form-group">-->
<!--				<label class="col-sm-3 control-label">Device Name：</label>-->
<!--				<div class="col-sm-8">-->
<!--					<input id="name" name="name" class="form-control" type="text">-->
<!--				</div>-->
<!--			</div>-->
			<div class="form-group">
				<label class="col-sm-3 control-label">IoT Services<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<select id="iotType" name="iotType"  class="form-control m-b" th:with="type=${@dict.getType('t_iot_type')}">
						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
					</select>
				</div>
			</div>
<!--			<div class="form-group">-->
<!--				<label class="col-sm-3 control-label">IoT Product Key<span style="color: red; ">*</span>：</label>-->
<!--				<div class="col-sm-8">-->
<!--					<select id="productKey" name="productKey"  class="form-control m-b" th:with="type=${@dict.getType('t_iot_key')}">-->
<!--						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>-->
<!--					</select>-->
<!--				</div>-->
<!--			</div>-->

			<div class="form-group">
				<label class="col-sm-3 control-label">Type<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<select id="type" name="type"  class="form-control m-b" th:with="type=${@dict.getType('t_device_type')}">
						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
					</select>
				</div>
			</div>
<!--			<div class="form-group">-->
<!--				<label class="col-sm-3 control-label">Status：</label>-->
<!--				<div class="col-sm-8">-->
<!--					<select id="status" name="status"  class="form-control m-b" th:with="type=${@dict.getType('t_device_status')}">-->
<!--						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>-->
<!--					</select>-->
<!--				</div>-->
<!--			</div>-->
			<div class="form-group">
				<label class="col-sm-3 control-label">Manufacturer<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<select id="producer" name="producer"  class="form-control m-b" th:with="type=${@dict.getType('producer_list')}">
						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Model<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<select id="model" name="model"  class="form-control m-b" th:with="type=${@dict.getType('model_list')}">
						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Institution<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<input id="treeId"  name="deptId" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Group：</label>
				<div class="col-sm-8">
					<select id="groupId" name="groupId" class="form-control m-b" th:with="groups=${@groupService.selectGroupList()}">
						<option value="">None</option>
						<option th:each="group : ${groups}" th:text="${group.groupName}" th:value="${group.id}"></option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Remark：</label>
				<div class="col-sm-8">
					<input id="remark" name="remark" class="form-control" type="text">
				</div>
			</div>

		</form>
	</div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var prefix = ctx + "msgcenter/device";

        $.validator.addMethod("snLengthLimit", function(value, element) {
                var returnVal = false;
                var snHead = $("#snHead").val();
                var snLength = $("#snLength").val();
                var snStart = $("#snStart").val();
                var snEnd = $("#snEnd").val();
                if(snHead.length + snStart.length == snLength && snHead.length + snEnd.length == snLength){
                    returnVal = true;
                }
                return returnVal;
            },
			$.validator.format("Ensure serial number length equals the sum of the header and segment lengths.")
		);

        $.validator.addMethod("snSegment", function(value, element) {
                var returnVal = false;
                var snStart = $("#snStart").val();
                var snEnd = $("#snEnd").val();
                if(snStart.length == snEnd.length && parseInt(snStart) <= parseInt(snEnd)){
                    returnVal = true;
                }
                return returnVal;
            },
            $.validator.format("Ensure start is less than end and in the same format.")
		);

		$("#form-device-add").validate({
			rules:{
				snHead:{
					required:true,
				},
                snLength:{
                    required:true,
                    digits:true,
                    snLengthLimit:true,
                },
				snStart:{
					required:true,
				},
				snEnd:{
					required:true,
                    snSegment:true,
				},
				model:{
					required:true,
				},
				producer:{
					required:true,
				},
				type:{
					required:true,
				},
				treeId:{
					required:true,
				},
				deptId:{
					required:true,
				}
			}
		});

		$.tree.selectDeptAndGroup($('#treeId'));

		function submitHandler() {
	        if ($.validate.form()) {
				if(!$('#treeId').val()){
					$.modal.msgError("Please select a institution");
					return;
				};
                $.operate.save(prefix + "/batchAdd", $('#form-device-add').serialize());
	        }
	    }
	</script>
</body>
</html>