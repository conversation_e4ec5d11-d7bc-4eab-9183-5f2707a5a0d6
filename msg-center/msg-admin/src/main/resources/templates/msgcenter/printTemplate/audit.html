<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-template-edit" th:object="${printTemplate}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">模板名称：</label>
                <div class="col-sm-8">
                    <input name="templateName" th:field="*{templateName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">模板：</label>
                <div class="col-sm-8">
                    <textarea id="templateValue" name="templateValue" class="form-control" style="height:500px;" readonly>[[*{templateValue}]]</textarea>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "msgcenter/printTemplate";
        $("#form-template-edit").validate({
            focusCleanup: true
        });
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/audit", $('#form-template-edit').serialize());
            }
        }
    </script>
</body>
</html>