<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                模板名称：
                                <input type="text" name="templateName"/>
                            </li>
                            <!--li>
                                模板：
                                <input type="text" name="templateValue"/>
                            </li-->
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="msgcenter:printTemplate:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-danger btn-del btn-edit disabled" onclick="$.operate.edit()" shiro:hasPermission="msgcenter:printTemplate:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger btn-del btn-del disabled" onclick="$.operate.removeAll()" shiro:hasPermission="msgcenter:printTemplate:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="msgcenter:template:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('msgcenter:printTemplate:edit')}]];
        var removeFlag = [[${@permission.hasPermi('msgcenter:printTemplate:remove')}]];
        var prefix = ctx + "msgcenter/printTemplate";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "打印模板",
                columns: [{
                    checkbox: true
                },{
                    field: 'id',
                    title: '编号',
                    visible: false
                },
                {
                    field : 'templateName',
                    title : '模板名称'
                },
                {
                    field : 'templateValue', 
                    title : '模板'
                },
                {
                    field : 'status',
                    title : '状态'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="audit(\'' + row.id + '\')"><i class="fa fa-edit"></i>审核</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
        $.tree.selectDept($('#treeId'));
        $.tree.getTreeName($('#treeId').val());



        /* 机构管理-参数配置 */
        function audit(deptId) {
            var url = prefix + '/audit/' + deptId;
            $.modal.open('审核', url);
        }
    </script>
</body>
</html>