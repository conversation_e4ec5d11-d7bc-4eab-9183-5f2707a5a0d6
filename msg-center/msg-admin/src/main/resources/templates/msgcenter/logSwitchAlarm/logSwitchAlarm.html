<!DOCTYPE HTML>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">
    
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="formId">
					<div class="select-list">
						<ul>
							<li>
								<span class="inp-label">设备序列号</span>：<input type="text" name="sn" id="sn"/>
							</li>

                            <li>
                                <span class="inp-label">处理状态</span>：
                                <select id="status" name="status" th:with="type=${@dict.getType('switch_alarm_log_status')}" >
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>

							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
										class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
										class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>



						</ul>
					</div>
				</form>
			</div>

			<div class="col-sm-12 select-table table-striped">
				<table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>
	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">

        var editFlag = [[${@permission.hasPermi('msgcenter:logSwitchAlarm:edit')}]];

        var prefix = ctx + "msgcenter/logSwitchAlarm";
        var dealStatus = [[${@dict.getType('switch_alarm_log_status')}]];
        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
				exportUrl: prefix + "/export",
                modalName: "终端切换记录",
				search: false,
		        showExport: true,
                columns: [{
		            checkbox: true
		        },
				{
					field : 'id', 
					title : '',
					visible: false
				},
				{
					field : 'sn', 
					title : '设备序列号',
					sortable: true
				},
				{
					field : 'version',
					title : '版本',
					sortable: true
				},
                {
                     field: 'deptName',
                     title: '机构',
                     sortable: false
                },
                {
                    field: 'status',
                    title: '状态',
                    sortable: true,
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(dealStatus, value);
                    }
                },
				{
					field : 'createTime',
					title : '创建时间',
					sortable: true
				},
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if(row.status == '0'){
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="#" onclick="dealLog(\'' + row.id + '\')"><i class="fa fa-play"></i>处理</a> ');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
			$.table.search();
        });

        function dealLog(id) {
            $.modal.confirm("确认已处理？", function() {
                $.operate.get(prefix + "/dealLog/"+id);
            })
        }

    </script>
</body>
</html>