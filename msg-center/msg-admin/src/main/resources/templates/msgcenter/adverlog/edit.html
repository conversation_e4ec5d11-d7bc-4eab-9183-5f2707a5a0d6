<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-adverlog-edit" th:object="${adverlog}">
            <input id="id" name="id" th:field="*{id}"  type="hidden">
			<div class="form-group">	
				<label class="col-sm-3 control-label">设备序列号：</label>
				<div class="col-sm-8">
					<input id="sn" name="sn" th:field="*{sn}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">广告内容：</label>
				<div class="col-sm-8">
					<input id="content" name="content" th:field="*{content}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">广告类型：</label>
				<div class="col-sm-8">
					<input id="type" name="type" th:field="*{type}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">广告播报方式：</label>
				<div class="col-sm-8">
					<input id="broadcastType" name="broadcastType" th:field="*{broadcastType}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">广告状态：</label>
				<div class="col-sm-8">
					<input id="status" name="status" th:field="*{status}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">下发时间：</label>
				<div class="col-sm-8">
					<input id="createTime" name="createTime" th:field="*{createTime}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">备注信息：</label>
				<div class="col-sm-8">
					<input id="remark" name="remark" th:field="*{remark}" class="form-control" type="text">
				</div>
			</div>
		</form>
    </div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var prefix = ctx + "msgcenter/adverlog"
		$("#form-adverlog-edit").validate({
			rules:{
				xxxx:{
					required:true,
				},
			}
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/edit", $('#form-adverlog-edit').serialize());
	        }
	    }
	</script>
</body>
</html>
