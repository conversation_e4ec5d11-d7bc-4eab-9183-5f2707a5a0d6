<!DOCTYPE HTML>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">
    
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="formId">
					<div class="select-list">
						<ul>
							<li>
								设备序列号：<input type="text" name="sn"/>
							</li>

							<li>
								广告状态：
								<select name="status" th:with="type=${@dict.getType('common_status')}">
									<option value="">所有</option>
									<option th:each="dict : ${type}" th:text="${dict.dictLabel}"
											th:value="${dict.dictValue}"></option>
								</select>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>
	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('msgcenter:adverlog:edit')}]];
        var removeFlag = [[${@permission.hasPermi('msgcenter:adverlog:remove')}]];
		var advertCommonType = [[${@dict.getType('advert_common_type')}]]
		var commonStatus = [[${@dict.getType('common_status')}]]
		var advertBroadcastType = [[${@dict.getType('advert_broadcast_type')}]]
		var prefix = ctx + "msgcenter/adverlog";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
				exportUrl: prefix + "/export",
                modalName: "广告消息记录",
				search: false,
		        showExport: true,
                columns: [{
		            checkbox: true
		        },
				{
					field : 'id', 
					title : '编号',
					visible: false
				},
				{
					field : 'sn', 
					title : '设备序列号',
					sortable: true
				},
				{
					field : 'content', 
					title : '广告内容',
					sortable: true
				},
				{
					field : 'type', 
					title : '广告类型',
					sortable: true,
					formatter: function(value, row, index) {
						return $.table.selectDictText(advertCommonType, value);
					}
				},
				/*{
					field : 'broadcastType', 
					title : '广告播报方式',
					sortable: true,
					formatter: function(value, row, index) {
						return $.table.selectDictText(advertBroadcastType, value);
					}
				},*/
				{
					field : 'status', 
					title : '广告状态',
					sortable: true,
					formatter: function(value, row, index) {
						return $.table.selectDictText(commonStatus, value);
					}
				},
				{
					field : 'createTime', 
					title : '下发时间',
					sortable: true
				},
				/*{
					field : 'remark', 
					title : '广告时长',
					sortable: true
				},*/
		        /*{
		            title: '操作',
		            align: 'center',
		            formatter: function(value, row, index) {
		            	var actions = [];
		            	actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="#" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="#" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
						return actions.join('');
		            }
		        }*/]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>