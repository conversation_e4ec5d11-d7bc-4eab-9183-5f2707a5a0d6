<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">
<form class="form-horizontal scroll" id="form-downloadJob-edit" th:object="${downloadJob}">
	<div class="container-div">
		<div class="row">
			<div class="col-sm-12 select-table">
				<input id="id" name="id" th:field="*{id}"  type="hidden">
				<div class="card-title">Job</div>
				<div class="col-lg-6">
					<div class="form-group">
						<label class="col-sm-3 control-label">Job Name：</label>
						<div class="col-sm-8">
							<input id="jobName" name="jobName" th:field="*{jobName}" class="form-control" type="text">
						</div>
					</div>
				</div>
			</div>
			<div class="col-sm-12 select-table">
				<div class="card-title">Software</div>
				<div class="col-lg-6">
					<div class="form-group">
						<label class="col-sm-3 control-label">Software Name：</label>
						<div class="col-sm-8">
							<select id="appId" name="appId"  class="form-control" >
								<option value="">Please select a software</option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="form-group">
						<label class="col-sm-3 control-label">Software Name：</label>
						<div class="col-sm-8">
							<input id="code" name="code" class="form-control" type="text" readonly>
						</div>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="form-group">
						<label class="col-sm-3 control-label">Software Version：</label>
						<div class="col-sm-8">
							<select id="appVersionId" name="appVersionId" class="form-control">
								<option value="">Please select a software version</option>
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="col-sm-12 select-table">
				<div class="card-title">Validity</div>
				<div class="col-lg-6">
					<div class="form-group">
						<label class="col-sm-3 control-label">TimeZone<span style="color: red; ">*</span>:</label>
						<div class="col-sm-8">
							<select id="timeZone" name="timeZone" class="form-control" th:with="type=${@dict.getType('time_zone')}"  required="required">
								<!--                    <option value="" ><span th:text="#{'common.time.zone'}">时区</span></option>-->
								<option th:each="dict : ${type}" th:text="${dict.dictLabel}"
										th:value="${dict.dictValue}" th:selected="${dict.dictValue == 'Asia/Shanghai'}"></option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="form-group">
						<label class="col-sm-3 control-label">Release Date：</label>
						<div class="col-sm-8 select-time">
						<input type="text" th:value="${@timeZoneUtils.printDateWithTimeZone(downloadJob.releaseTime, downloadJob.timeZone)}"  class="datetimepicker form-control"
							   id="startTime" placeholder="Release Date" name="releaseTime">
<!--						<input id="startTime" name="validStartTime" class="form-control time-input" type="text"-->
<!--							   th:value="${@timeZoneUtils.printDateWithTimeZone(downloadJob.releaseTime, downloadJob.timeZone)}"-->
<!--							   required data-format="yyyy-MM-dd HH:mm">-->
					</div>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="form-group">
						<label class="col-sm-3 control-label">Expired Date：</label>
						<div class="col-sm-8 select-time">
							<input id="endTime"
								   th:value="${@timeZoneUtils.printDateWithTimeZone(downloadJob.validDate, downloadJob.timeZone)}"
								   name="validDate" type="text" class="datetimepicker form-control" placeholder="Expired Date"/>
						</div>
					</div>
				</div>
			</div>
			<div class="col-sm-12 select-table">
				<div class="card-title"></div>
				<div class="col-lg-6">
					<div class="form-group">
						<label class="col-sm-3 control-label">Release Type：</label>
						<div class="col-sm-8">
							<select id="releaseType" th:field="*{releaseType}"  name="releaseType"  class="form-control" th:with="type=${@dict.getType('release_type')}">
								<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="form-group">
						<label class="col-sm-3 control-label">Institution：</label>
						<div class="col-sm-8">
							<input id="treeId" th:field="*{deptId}"  name="deptId" class="form-control" type="text">
						</div>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="form-group">
						<label class="col-sm-3 control-label">Group：</label>
						<div class="col-sm-8">
							<select id="groupId" th:field="*{groupId}"  name="groupId" class="form-control m-b" th:with="groups=${@groupService.selectGroupList()}">
								<option value="">None</option>
								<option th:each="group : ${groups}" th:text="${group.groupName}" th:value="${group.id}"></option>
							</select>
						</div>
					</div>
				</div>
                <div class="col-lg-6">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">Software Verison：</label>
                        <div class="col-sm-8">
                            <input id="firmwareVersion" name="firmwareVersion" th:field="*{firmwareVersion}" class="form-control" maxlength="20" type="text">
                        </div>
                    </div>
                </div>
				<div class="col-sm-12 no-padding">
					<div class="col-lg-6">
						<div class="form-group">
							<label class="col-sm-3 control-label">Device List：</label>
							<div class="col-sm-8">
								<div class="m-b">
									<button type="button" class="btn btn-primary" onclick="deviceList()">Add by Searching</button>
									<button type="button" class="btn btn-warning" onclick="deleteList()">Reset</button>
								</div>
							</div>
                            <div class="m-b col-sm-12 col-sm-offset-3">
                                <button type="button" class="btn btn-primary" onclick="deviceBySectionList()">Add by SN Range</button>
                                <label class="control-label">SN Prefix：</label>
                                <input id="snHead" class="form-control short" type="text">
                                <label class="control-label">SN Range：</label>
                                <input id="snStart" class="form-control short" placeholder="Prefix" type="text">
                                <span>----</span>
                                <input id="snEnd" class="form-control short" placeholder="Suffix" type="text">
                            </div>
                            <div class="m-b col-sm-12 col-sm-offset-3">
                                <div>
                                    <div class="label-cont" id="device_list" th:with="collections = ${#strings.arraySplit(downloadJob.collection,',')}">
                                        <div class="label-piece" th:inline="text" th:each="item:${collections}" th:data-value="${item}">
											<span class="label-close"><i class="fa fa-remove"></i>
											</span>[[${item}]]</div>
                                    </div>
                                    <input id="collection" th:field="*{collection}"  name="collection" class="form-control" type="hidden" />
                                </div>
                            </div>
						</div>
					</div>
				</div>

			</div>

		</div>
	</div>
	<div class="btn-bar">
		<button type="button" class="btn btn-primary " onclick="submitHandler()">Save</button>
		<!--<button type="button" class="btn btn-default" onclick="back()">返回列表</button>-->
	</div>
</form>

<div th:include="include::footer"></div>
<script th:src="@{/ajax/libs/select/select2.js}"></script>
<script type="text/javascript">
	var prefix = ctx + "msgcenter/downloadJob"
	var devicePrefix = ctx + "msgcenter/device"
	var appData = [];

	layui.use(['laydate'], function(){
		var laydate = layui.laydate;

		// Initialize the date picker with English settings
		laydate.render({
			elem: '#startTime',       // Specify the target element
			lang: 'en',          // Set the language to English
			type: 'datetime',    // 选择日期 + 时间
			format: 'yyyy-MM-dd HH:mm',  // 格式化显示
		});

		laydate.render({
			elem: '#endTime',       // Specify the target element
			lang: 'en',          // Set the language to English
			type: 'datetime',    // 选择日期 + 时间
			format: 'yyyy-MM-dd HH:mm',  // 格式化显示
		});
	});

	$("#form-downloadJob-edit").validate({
		rules:{
			jobName:{
				required:true,
			},
			appId:{
				required:true,
			},
			appVersionId:{
				required:true,
			},
			releaseTime:{
				required:true,
			},
			validDate:{
				required:true,
			},
		}
	});
	$.tree.selectDept($('#treeId'));
	$.tree.getTreeName($('#treeId').val());

	function submitHandler() {
		var array = new Array();
		$("#device_list .label-piece").each(function(){
			array.push($(this).attr('data-value'));
		})

		//【判断】类型参数非空
		const releaseType = $("#releaseType").val();
		const organization = $("#treeId").val();
		const collection = $("#device_list").val();
		if (releaseType === '1' && organization === '') {
			$.modal.msgError("Please select a organization");
			return;
		} else if (releaseType === '2' && collection === '') {
			$.modal.msgError("Please select at least one device");
			return;
		}
		$('#collection').val(array.join());
		if ($.validate.form()) {
			$.operate.save(prefix + "/edit", $('#form-downloadJob-edit').serialize(), function (res) {
				$.modal.closeLoading();
				if(res.code === web_status.SUCCESS){
					//window.location = prefix;
                    var menuName = "Remote Upgrade";
					refreshOrCreateMenuItem(prefix, menuName);
                    $.modal.alertSuccess("Edit successfully");
					$.modal.close();
					back();
                }else {
					$.modal.alertError(res.msg)
				}
			});
		}
	}

	function back() {
		window.location = prefix;
	}
	function deviceList() {
		$.modal.open("Device List", devicePrefix+"/devicePopout", 1200);
	}
    function deleteList() {
        $('#device_list').html('');
    }

    function formatInt(number,len) {
        var mask = "";
        var returnVal = "";
        for(var i=0;i<len;i++) mask+="0";
        returnVal = mask + number;
        returnVal = returnVal.substr(returnVal.length-len,len);
        return returnVal;
    }
    function deviceBySectionList() {
        var returnVal = false;
        var snHead = $("#snHead").val();
        var snStart = $("#snStart").val();
        var snEnd = $("#snEnd").val();
        var start = parseInt(snStart);
        var end = parseInt(snEnd);
        var snlength = snStart.length;
        var rows = [];
        if(snHead == ""){
            $.modal.msgError("Please add SN prefix.");
        }
        if(snlength == snEnd.length && start < end){
            returnVal = true;
            for(var i=start;i<=end;i++){
                rows.push(snHead + formatInt(i,snlength));
            }
        }
        if (returnVal) {
            addDeviceList(rows);
        }else{
            $.modal.msgError("Please ensure the start is before the end and the format is consistent");
        }
    }
	function addDeviceList(rows) {
        var arr = [];
        $('#device_list .label-piece').each(function () {
            arr.push($(this).attr('data-value'));
        });
        rows = arr.concat(rows);
        rows = Array.from(new Set(rows));
        $('#device_list').html('');
		for(var i = 0, length = rows.length; i < length; i++){
			$('#device_list').append("<div class='label-piece' data-value='"+rows[i]+"'><span class='label-close'><i class='fa fa-remove'></i></span>"+rows[i]+"</div>");
		}
		$('#device_list .label-close').on('click',function () {
			$(this).closest('.label-piece').remove();
		})
		$('#device_list').val(rows);

	}
	function deleteDevice() {
		$("#device_list option:selected").remove();
	}
	function setNameOption(appId) {
		$('#appId').find('option').not('option:first').remove();
		$.service.post(ctx + 'msgcenter/appInf/list', {/*type: $('#releaseType').val()*/}, function (res) {
			if(res && res.data && Array.isArray(res.data.rows)){
				appData = res.data.rows;
				res.data.rows.forEach(function (t) {
					if(appId == t.id){
						$('#appId').append('<option value="'+ t.id +' " selected>'+t.name+'</option>');
						$('#code').val(t.code);
					}else {
						$('#appId').append('<option value="'+ t.id +'">'+t.name+'</option>');
					}

				})
			}
		} )
	}
	function setVersionOption(appVersionId) {
		$('#appVersionId').find('option').not('option:first').remove();
		$.service.post(ctx + 'msgcenter/appVersion/list', {appId: $('#appId').val()}, function (res) {
			if(res && res.data && Array.isArray(res.data.rows)){
				res.data.rows.forEach(function (t) {
					if(appVersionId == t.id){
						$('#appVersionId').append('<option value="'+ t.id +'" selected>'+t.appVersion+'</option>');
					}else {
						$('#appVersionId').append('<option value="'+ t.id +'">'+t.appVersion+'</option>');
					}


				})
			}
		} )
	}

	$(function () {
        $('#device_list .label-close').on('click',function () {
            $(this).closest('.label-piece').remove();
        })
		setNameOption(/*<![CDATA[*/[[${downloadJob.appId}]]/*]]>*/);
		if($('#releaseType').val() == 1){
			$('#collection').closest('.form-group').hide();
			$('#devices').closest('.form-group').hide();
		}else{
			$('#treeId').closest('.form-group').hide();
			$('#groupId').closest('.form-group').hide();
		}
		setVersionOption(/*<![CDATA[*/[[${downloadJob.appVersionId}]]/*]]>*/);
		$('#appId').on('change', function () {
			setVersionOption(/*<![CDATA[*/[[${downloadJob.appVersionId}]]/*]]>*/);
		});
		$('#releaseType').on('change', function () {
			if($(this).val() == 1){
				$('#treeId').closest('.form-group').show();
				$('#groupId').closest('.form-group').show();
				$('#collection').closest('.form-group').hide();
				$('#devices').closest('.form-group').hide();
			}else{
				$('#treeId').closest('.form-group').hide();
				$('#groupId').closest('.form-group').hide();
				$('#devices').closest('.form-group').show();
				$('#collection').closest('.form-group').show();
			}
		});
	});
</script>
</body>
</html>