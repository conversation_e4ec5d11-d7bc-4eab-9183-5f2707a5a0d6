<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-merchantBind-edit" th:object="${merchantBind}">
            <input id="id" name="id" th:field="*{id}"  type="hidden">
			<div class="form-group">	
				<label class="col-sm-3 control-label">商户名称：</label>
				<div class="col-sm-8">
					<input id="name" name="name" th:field="*{name}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">商户地址：</label>
				<div class="col-sm-8">
					<input id="address" name="address" th:field="*{address}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">商户联系人：</label>
				<div class="col-sm-8">
					<input id="applyerName" name="applyerName" th:field="*{applyerName}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">商户手机号：</label>
				<div class="col-sm-8">
					<input id="applyerPhone" name="applyerPhone" th:field="*{applyerPhone}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">账号：</label>
				<div class="col-sm-8">
					<input id="account" name="account" th:field="*{account}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">密码：</label>
				<div class="col-sm-8">
					<input id="password" name="password" th:field="*{password}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">盐加密：</label>
				<div class="col-sm-8">
					<input id="salt" name="salt" th:field="*{salt}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">二维码信息：</label>
				<div class="col-sm-8">
					<input id="qrcodes" name="qrcodes" th:field="*{qrcodes}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">行业：</label>
				<div class="col-sm-8">
					<input id="bussType" name="bussType" th:field="*{bussType}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">备注：</label>
				<div class="col-sm-8">
					<input id="remark" name="remark" th:field="*{remark}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">机构id：</label>
				<div class="col-sm-8">
					<input id="deptId" name="deptId" th:field="*{deptId}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">签名方式：</label>
				<div class="col-sm-8">
					<input id="signType" name="signType" th:field="*{signType}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">设备序列号：</label>
				<div class="col-sm-8">
					<input id="sn" name="sn" th:field="*{sn}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">商户号：</label>
				<div class="col-sm-8">
					<input id="merchantCode" name="merchantCode" th:field="*{merchantCode}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">银行终端号：</label>
				<div class="col-sm-8">
					<input id="terminalNumber" name="terminalNumber" th:field="*{terminalNumber}" class="form-control" type="text">
				</div>
			</div>
		</form>
    </div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var prefix = ctx + "msgcenter/merchantBind"
		$("#form-merchantBind-edit").validate({
			rules:{
				xxxx:{
					required:true,
				},
			}
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/edit", $('#form-merchantBind-edit').serialize());
	        }
	    }
	</script>
</body>
</html>
