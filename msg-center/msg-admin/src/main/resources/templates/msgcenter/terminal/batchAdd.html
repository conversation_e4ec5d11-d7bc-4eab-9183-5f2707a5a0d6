<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-device-add">
			<div class="form-group">
				<label class="col-sm-3 control-label">设备序列号头：</label>
				<div class="col-sm-8">
					<input id="snHead" name="snHead" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">设备序列号区间：</label>
				<div class="col-sm-8">
					<input id="snStart" name="snStart" class="form-control short" placeholder="起始位" type="text">
					<span>----</span>
					<input id="snEnd" name="snEnd" class="form-control short" placeholder="结束位" type="text">
				</div>
                <label class="col-sm-3"></label>
                <div class="col-sm-8">
                    <span style="color:blue">如导入D1V01600000-D1V01600005的终端，<br/>则序列头为D1V01600，起始位000，结束位005；一次批量处理最多50000条！！！ </span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">SN长度：</label>
                <div class="col-sm-8">
                    <input id="snLength" name="snLength" class="form-control" type="text">
                </div>
            </div>
			<div class="form-group">
				<label class="col-sm-3 control-label">更新存在的设备数据 ：</label>
				<div class="col-sm-8">
					<input type="checkbox" id="updateSupport" name="updateSupport" title="如果设备已经存在，更新这条数据。">
				</div>
			</div>

			<div class="form-group">
				<label class="col-sm-3 control-label">设备型号：</label>
				<div class="col-sm-8">
					<select id="model" name="model"  class="form-control m-b" th:with="type=${@dict.getType('model_list')}">
						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">所属机构：</label>
				<div class="col-sm-8">
					<input id="treeId"  name="deptId" class="form-control" type="text">
				</div>
			</div>

		</form>
	</div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var prefix = ctx + "msgcenter/terminal";
        $.validator.addMethod("snLengthLimit",
			function(value, element) {
            var returnVal = false;
            var snHead = $("#snHead").val();
            var snLength = $("#snLength").val();
            var snStart = $("#snStart").val();
            var snEnd = $("#snEnd").val();
            if(snHead.length + snStart.length == snLength && snHead.length + snEnd.length == snLength){
                returnVal = true;
            }
            return returnVal;
        },
			$.validator.format("请确保序列号长度等于序列头长度加段号长度")
		);
        $.validator.addMethod("snSegment",
			function(value, element) {
            var returnVal = false;
            var snStart = $("#snStart").val();
            var snEnd = $("#snEnd").val();
            if(snStart.length == snEnd.length && parseInt(snStart) < parseInt(snEnd)){
                returnVal = true;
            }
            return returnVal;
        },
			$.validator.format("请确保初始小于结束且格式一致")
		);
		$("#form-device-add").validate({
			rules:{
				snHead:{
					required:true,
				},
                snLength:{
                    required:true,
                    digits:true,
                    snLengthLimit:true,
                },
				snStart:{
					required:true,
				},
				snEnd:{
					required:true,
                    snSegment:true,
				},
				treeId:{
					required:true,
				},
				deptId:{
					required:true,
				}
			}
		});
		$.tree.selectDept($('#treeId'));
		$.tree.getTreeName($('#treeId').val());

		function submitHandler() {
	        if ($.validate.form()) {
				if(!$('#treeId').val()){
					$.modal.msgError("请选择机构");
					return;
				};
                $.operate.save(prefix + "/batchAdd", $('#form-device-add').serialize());
	        }
	    }
	</script>
</body>
</html>
