<!DOCTYPE HTML>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<link th:href="@{/js/datetime/daterangepicker.css}" rel="stylesheet"/>
<link>
<style>
    .table-part {
        display: none;
    }
</style>
<body class="gray-bg">

<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <span class="inp-label">机构</span>：<input type="text" name="deptId" id="treeId"/>
                        </li>

                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" id="search-btn"><i
                                    class="fa fa-search"></i>&nbsp;Search</a>
                            <a class="btn btn-warning btn-rounded btn-sm" id="reset-btn"><i
                                    class="fa fa-refresh"></i>&nbsp;Reset</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div id="main-cont" style="display: none">
            <div class="col-sm-12 select-table">
                <div class="card-title log-card-title">
                    <h3>Summary</h3>
                    <div class="card-title-extra" id="timeRange">
                        <a class="active" href="javascript:;" data-value="yesterday">Last Day</a>
                        <a href="javascript:;" data-value="lastWeek">Last Week</a>
                        <a href="javascript:;" data-value="lastMonth">Last Month</a>
                        <div class="input-group">

                            <input type="text" id="date-range-picker" />
                            <span class="input-group-addon">
                            <i class="fa fa-calendar"></i>
                        </span>

                        </div>
                    </div>
                </div>
                <div class="report-card-group">
                    <div class="report-card">
                        <div>
                            <div class="meta">Number of Device</div>
                            <div class="total" id="snCount">0</div>
                        </div>

                    </div>
                    <div class="report-card">
                        <div>
                            <div class="meta">Number of Activated Device</div>
                            <div class="total" id="activateCount">0</div>
                        </div>
                    </div>
                    <div class="report-card">
                        <div>
                            <div class="meta">Activated Rate (%)</div>
                            <div class="total" id="success">0</div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <div class="card-title log-card-title" style="margin-bottom: 24px">
                    <h3>Device Number Trend</h3>
                    <div class="card-title-extra" id="changeListType">
                        <a class="active" href="javascript:;" data-value="chart">Graphic Chart</a>
                        <a href="javascript:;" data-value="table">Data Chart</a>
                    </div>
                </div>
                <div id="chart1" class="chart-part" style="height: 300px;"></div>
                <div class="table-part">
                    <table id="table1" data-mobile-responsive="true"></table>
                </div>

            </div>
<!--            <div class="col-sm-12 select-table table-part table-striped">-->
<!--                <div class="card-title log-card-title">-->
<!--                    <h3>收款方式对比</h3>-->
<!--                </div>-->
<!--                <table id="table2" class="table-part" data-mobile-responsive="true"></table>-->
<!--            </div>-->
            <div class="col-sm-12 chart-part" style="padding-left: 0;padding-right: 0">
                <div class="select-table" >
                    <div class="card-title log-card-title" style="padding-left: 40px;">
                        <h3>Device Activation Trend</h3>
                    </div>
                    <div id="chart2" style="height: 300px;"></div>
                </div>

            </div>
<!--            <div class="col-sm-12 col-lg-4 chart-part" style="padding-right: 0">-->
<!--                <div class="select-table" style="padding: 15px">-->
<!--                    <div class="card-title log-card-title">-->
<!--                        <h3>收款方式对比</h3>-->
<!--                        <div class="card-title-extra" id="changePie">-->
<!--                            <a class="active" href="javascript:;" data-value="3">金额</a>-->
<!--                            <a href="javascript:;" data-value="4">笔数</a>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div id="chart3" style="height: 300px;"></div>-->

<!--                </div>-->
<!--            </div>-->
        </div>
        <div id="tip-cont" class="col-sm-12 select-table table-striped">
            <div class="none-info search-tip"></div>
        </div>

    </div>
</div>
<div th:include="include :: footer"></div>
<script th:src="@{/js/echarts.js}"></script>
<script th:src="@{/js/datetime/moment.min.js}"></script>
<script th:src="@{/js/datetime/daterangepicker.js}"></script>
<script th:inline="javascript">
    $.tree.selectDept($('#treeId'));
    $.tree.getTreeName($('#treeId').val());

    var payChannel = [[${@dict.getType('voice_channel')}]];

    var list = [];//获取数据进行存储
    var chart1List = [new Array(),new Array(),new Array()];
    var chart2List = [new Array(),new Array(),new Array()];

    // var chart3List = [];
    // var chart4List = [];
    var timeX = [];
    var timeShowX = [];

    $('#date-range-picker').daterangepicker({
        opens: 'right',
        locale: {
            format: 'YYYY/MM/DD',
            applyLabel: 'Confirm',
            cancelLabel: 'Cancel',
            fromLabel: 'Start Time',
            toLabel: 'End Time',
            customRangeLabel: 'Manual Select',
            daysOfWeek: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
            monthNames: ['January', 'February', 'March', 'April', 'May',
                'June','July', 'August', 'September', 'October', 'November', 'December'],
            firstDay: 1,
        }
    }).next().on('click', function(){
        $(this).prev().focus();
    });
    $('#date-range-picker').val('');

    //获取汇总数据
    var initSta = function(data){
        var snCount = data.reduce(function (num, item, index) {
            return num + (parseInt(item.snCount) || 0);
        },0);
        var activateCount = data.reduce(function (num, item, index) {
            return num + (parseInt(item.activateCount) || 0);
        },0);
        // var money = data.reduce(function (num, item, index) {
        //     return num + (Number(item.money) || 0);
        // },0);
        $('#snCount').html(snCount);
        $('#activateCount').html(activateCount);
        $('#success').html(snCount === 0 ? 0 : (activateCount / snCount * 100).toFixed(2));
    }

    //初始化pie图标
    // var initPieChart = function (type) {
    //     var chart3 = echarts.init($('#chart3').get(0));
    //     var data = (type == 3?chart3List:chart4List).sort(function (a, b) { return b.value - a.value; });
    //     var option3 = {
    //         tooltip : {
    //             trigger: 'item',
    //             formatter: "{a} <br/>{b} : {c} ({d}%)"
    //         },
    //         series : [
    //             {
    //                 name: '支付方式',
    //                 type: 'pie',
    //                 radius : '50%',
    //                 center: ['50%', '50%'],
    //                 data: data,
    //                 itemStyle: {
    //                     emphasis: {
    //                         shadowBlur: 10,
    //                         shadowOffsetX: 0,
    //                         shadowColor: 'rgba(0, 0, 0, 0.5)'
    //                     },
    //                     normal:{
    //                         color:function(params) {
    //                             //自定义颜色
    //                             var colorList = [
    //                                 '#488af0', '#1ab394', '#f8ac59', '#ed5565', '#23c6c8',
    //                             ];
    //                             return colorList[params.dataIndex]
    //                         },
    //                         opacity: 0.8
    //                     }
    //                 },
    //             }
    //         ]
    //     };
    //     chart3.setOption(option3);
    // }

    //初始化图标
    var initChartData = function(res) {
        var list = res.deviceReportList;
        var data = {};
        getTimeRange();
        [...list].forEach(function (val) {
            var name = val.countTime?val.countTime:'';
            data[name] = {
                activateCount: val.activateCount,
                snCount: val.snCount
            }
        })
        chart1List = [new Array(),new Array(),["终端总数", "平均终端总数"]];
        chart2List = [new Array(),new Array(),["终端激活数", "平均终端激活数"]];
        timeX.forEach(function (t) {
            if(data[t]){
                chart1List[0].push(data[t].snCount);
                chart1List[1].push(data[t].snCount == 0 ? 0 : (data[t].snCount / data[t].snCount).toFixed(2));

                chart2List[0].push(data[t].activateCount);
                chart2List[1].push(data[t].snCount == 0 ? 0 : (data[t].activateCount / data[t].snCount).toFixed(2));
            }else{
                chart1List[0].push(0);
                chart1List[1].push(0);

                chart2List[0].push(0);
                chart2List[1].push(0);
            }
        })
        // var pieData = res.listOnlyChannel;
        // chart3List = [];
        // chart4List = [];
        // pieData.forEach(function (t) {
        //     var channel = $.table.selectDictLabel(payChannel, t.channel).replace(/\<span\>|\<\/span\>/g, '');
        //     chart3List.push({
        //         name: channel + '\n（'+t.money+'元）',
        //         value: t.money
        //     });
        //     chart4List.push({
        //         name: channel + '\n（'+t.paidCount+'笔）',
        //         value: t.paidCount
        //     });
        // })

    }
    var initChart = function () {
        var chart1 = echarts.init($('#chart1').get(0));
        var chart2 = echarts.init($('#chart2').get(0));
        var option1 = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: chart1List[2]
            },
            xAxis: {
                name: 'Date',
                type: 'category',
                boundaryGap: false,
                axisLine: {
                    lineStyle: {
                        color: '#e5e5e5'
                    }
                },
                axisLabel: {
                    color: '#999',
                    interval: Math.floor(timeShowX.length/6) || 1
                },
                data: timeShowX
            },
            calculable: true,
            yAxis: {
                name: '终端总数',
                type: 'value',
                splitLine: {
                    show: false
                },
                minInterval: 100,
                axisLine: {
                    lineStyle: {
                        color: '#e5e5e5'
                    }
                },
                axisLabel: {
                    color: '#999'
                },
            },
            series: [{
                name: chart1List[2][0],
                data: chart1List[0],
                type: 'line',
                smooth: true,
                color: '#488af0',
                areaStyle: {
                    opacity: 0.2
                }
            }]
        };
        chart1.setOption(option1);
        var option2 = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: chart2List[2]
            },
            xAxis: {
                name: 'Date',
                type: 'category',
                boundaryGap: false,
                axisLine: {
                    lineStyle: {
                        color: '#e5e5e5'
                    }
                },
                axisLabel: {
                    color: '#999',
                    interval: Math.floor(timeShowX.length/6) || 1
                },
                data: timeShowX
            },
            calculable: true,
            yAxis: {
                name: 'Number of Activated Device',
                type: 'value',
                splitLine: {
                    show: false
                },
                minInterval: 100,
                axisLine: {
                    lineStyle: {
                        color: '#e5e5e5'
                    }
                },
                axisLabel: {
                    color: '#999'
                },
            },
            series: [{
                name: chart2List[2][0],
                data: chart2List[0],
                type: 'line',
                smooth: true,
                color: '#488af0',
                areaStyle: {
                    opacity: 0.2
                }
            },{
                name: chart2List[2][1],
                data: chart2List[1],
                type: 'line',
                smooth: true,
                color: '#1ab394',
                areaStyle: {
                    opacity: 0.2
                }}]
        };
        chart2.setOption(option2);
        option2.yAxis.minInterval = 100;
        //initPieChart($('#changePie a.active').attr('data-value'));

    }

    //初始化表格
    var initTable = function () {
        var option = {
            pagination: true,
            pageNumber: 1,                                      // 初始化加载第一页，默认第一页
            pageSize: 5,                                       // 每页的记录行数（*）
            pageList: [5, 10, 15],
        }
        $('#table1').bootstrapTable({
            columns:[{
                field: 'countTime',
                title: '统计时间'
            },{
                field: 'snCount',
                title: '终端总数'
            },{
                field: 'activateCount',
                title: '终端激活数'
            }],
            data: [],
            ...option
        });
        // $('#table2').bootstrapTable({
        //     columns:[{
        //         field: 'channel',
        //         title: '交易渠道',
        //         formatter: function (value, row, index) {
        //             return $.table.selectDictLabel(payChannel, value);
        //         }
        //     },{
        //         field: 'money',
        //         title: '交易金额（元）'
        //     },{
        //         field: 'paidCount',
        //         title: '交易笔数（笔）'
        //     }],
        //     data: [],
        //     ...option
        // });


    }

    var setTable = function () {
        $('#table1').bootstrapTable('load',{
            data: list.deviceReportList
        });
        // $('#table2').bootstrapTable('load',{
        //     data: list.listOnlyChannel
        // });
    }
    //获取时间范围
    var getTimeRange = function () {
        if($('#timeRange a.active').length){
            var type = $('#timeRange a.active').attr('data-value');
            switch (type) {
                case 'yesterday':
                    var day = moment().subtract(1, 'days').startOf('day');
                    var dayList = [];
                    for(var i=0;i<24;i++){
                        dayList.push(moment(day));
                        day.add(1,'h');
                    }
                    timeShowX = [].concat(dayList).map(function (val, index) {
                        return index === 0?val.format('MM-DD HH:mm'):val.format('HH:mm');
                    });
                    timeX = [].concat(dayList).map(function (val, index) {
                        return val.format('YYYY-MM-DD HH:mm:ss');
                    });
                    break;
                case 'lastWeek':
                    timeX = [];
                    timeShowX = [];
                    var start = moment().subtract(1, 'weeks').startOf('week');

                    for(var i=0;i<7;i++){
                        start = start.add(1, 'days');
                        timeX.push(start.format('YYYY-MM-DD'+' 00:00:00'));
                        timeShowX.push(start.format('YYYY-MM-DD'));
                    }
                    break;
                case 'lastMonth':
                    timeX = [];
                    timeShowX = [];
                    var sm = moment().subtract(1, 'months').startOf('months');
                    var se = moment().subtract(1, 'months').endOf('months');
                    var days = se.diff(sm, 'days') + 1;
                    for(var i=0;i<days;i++){
                        timeX.push(sm.format('YYYY-MM-DD'+' 00:00:00'));
                        timeShowX.push(sm.format('YYYY-MM-DD'));
                        sm = sm.add(1, 'days')
                    }
                    break;

            }
            return { dateType: type }
        }
        var dataValue = $('#date-range-picker').val().split('-');
        if(!dataValue[0] || !dataValue[1]){
            $('#timeRange > a:first-child').addClass('active');
            $('#date-range-picker').val('');
            return getTimeRange();
        }
        timeX = [];
        timeShowX = [];
        var rm = moment(dataValue[0].trim(), 'YYYY/MM/DD');
        var re =moment(dataValue[1].trim(), 'YYYY/MM/DD');
        var rdays = re.diff(rm, 'days') + 1;
        for(var i=0;i<rdays;i++){
            if(rdays > 2){
                timeX.push(rm.format('YYYY-MM-DD HH:mm:ss'));
                timeShowX.push(rm.format('YYYY-MM-DD'));
                rm = rm.add(1, 'days');
            }else{
                var harr = [];
                var now = moment(rm);
                for(var j=0;j<24;j++){
                    harr.push(moment(now));
                    now.add(1,'h');
                }
                timeShowX = timeShowX.concat([].concat(harr).map(function (val, index) {
                    return val.format('MM-DD HH:mm');
                }));
                timeX = timeX.concat([].concat(harr).map(function (val, index) {
                    return val.format('YYYY-MM-DD HH:mm:ss');
                }));
                rm = rm.add(1, 'days');
            }

        }
        return {
            startDate: dataValue[0].trim().replace(/\//g,''),
            endDate: dataValue[1].trim().replace(/\//g,''),
            dateType: rdays > 2?'day':'hour'
        }

    }



    var initData = function() {
        initSta(list.deviceReportList);
        initChartData(list);
        initChart();
        setTable();
    }

    var handleSearch = function () {
        // if(!$('#treeId').val()){
        //     $('#main-cont').hide();
        //     $('#tip-cont').show();
        //     list = [];
        // }else{
        //     $('#tip-cont').hide();
        //     $('#main-cont').show();
        //     $.service.post(ctx + 'msgcenter/logMoneyReport/list',$.extend({
        //         deptId: $('#treeId').val()
        //     },getTimeRange()),function (res) {
        //         list = res.data;
        //         initData();
        //     });
        // }
        $('#tip-cont').hide();
        $('#main-cont').show();
        $.service.post(ctx + 'log/device/reportList',$.extend({
            deptId: $('#treeId').val()
        },getTimeRange()),function (res) {
            list = res.data;
            initData();
        });
    }

    handleSearch();

    initTable();
    $('#search-btn').on('click',handleSearch);

    $('#timeRange a').on('click', function () {
        $('#date-range-picker').val('');
        $('#timeRange a.active').removeClass('active');
        $(this).addClass('active');
        handleSearch();
    })

    //选择时间
    $('#date-range-picker').on('apply.daterangepicker',function(ev, picker) {
        if(!picker.startDate || !picker.endDate) return;
        $('#timeRange a.active').removeClass('active');
        handleSearch();

    });

    $('#reset-btn').on('click',function () {
        $('#sn').val('');
        $('#main-cont').hide();
        $('#tip-cont').show();
        list = [];
    });

    $('#changePie a').on('click', function () {
        $('#changePie a.active').removeClass('active');
        $(this).addClass('active');
        //initPieChart($(this).attr('data-value'))
    });

    $('#changeListType a').on('click', function () {
        $('#changeListType a.active').removeClass('active');
        $(this).addClass('active');
        $('.table-part, .chart-part').hide();
        $('.'+$(this).attr('data-value')+'-part').show();
        initChart();
        setTable();
    });


</script>
</body>
</html>