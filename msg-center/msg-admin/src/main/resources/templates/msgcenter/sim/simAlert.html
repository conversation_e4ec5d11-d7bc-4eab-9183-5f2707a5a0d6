<!DOCTYPE HTML>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">

<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            ICCID：<input type="text" name="iccid"/>
                        </li>


                        <li class="select-time">
                            <label>有效期： </label>
                            <input type="text" class="datetimepicker" id="startTime" placeholder="开始时间" name="startTime"/>
                            <span>-</span>
                            <input type="text" class="datetimepicker" id="endTime" placeholder="结束时间" name="endTime"/>
                        </li>

                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm hidden-xs" id="toolbar" role="group"></div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>

    </div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var prefix = ctx + "msgcenter/sim";

    $(function () {
        var options = {
            url: prefix + "/listSimAlerts",
            modalName: "Sim提醒信息",
            search: false,
            showExport: true,
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: '编号',
                    visible: false
                },
                {
                    field: 'iccid',
                    title: 'ICCID',
                    sortable: true
                },
                {
                    field: 'message',
                    title: '播报内容',
                    sortable: true,
                    visible: true
                },
                {
                    field: 'expires',
                    title: '有效期',
                    sortable: true
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>