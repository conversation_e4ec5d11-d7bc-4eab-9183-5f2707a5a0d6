<!DOCTYPE HTML>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<style>
    .inp-label {
        min-width: 65px;
        display: inline-block;
    }
</style>
<body class="gray-bg">

<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId" onkeydown="if(event.keyCode==13){return false;}">
                <div class="select-list">
                    <ul>
                        <li>
                            <span class="inp-label">Device SN</span>：<input type="text" name="sn" id="sn"/>
                        </li>

                        <li>
                            <span class="inp-label">Manufacturer</span>：
                            <select name="producer" th:with="type=${@dict.getType('producer_list')}">
                                <option value="">All</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <span class="inp-label">Institution</span>：<input type="text" name="deptId" id="treeId"/>
                        </li>

                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;Search</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;Reset</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm hidden-xs" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.importExcel()" shiro:hasPermission="msgcenter:device:import">
                <i class="fa fa-upload"></i> Import
            </a>
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="msgcenter:device:add">
                <i class="fa fa-plus"></i> Add
            </a>
            <a class="btn btn-success" onclick="$.operate.batchAdd()" shiro:hasPermission="msgcenter:device:add">
                <i class="fa fa-plus"></i> Batch Add
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('msgcenter:device:edit')}]];
    var editBroadcastFlag = [[${@permission.hasPermi('msgcenter:broadcastconfig:edit')}]];
    var removeFlag = [[${@permission.hasPermi('msgcenter:device:remove')}]];
    var prefix = ctx + "msgcenter/aliyunDevice";
    var deviceStatus = [[${@dict.getType('t_device_status')}]];
    var deviceNetworkStatus = [[${@dict.getType('t_device_network_status')}]];
    var deviceType = [[${@dict.getType('t_device_type')}]];
    var producerList = [[${@dict.getType('producer_list')}]];
    var modelList = [[${@dict.getType('model_list')}]];
    var broadcastPrefix = ctx + "msgcenter/broadcastconfig";
    var broadcastEditUrl = broadcastPrefix + "/edit/{id}";
    $(function () {
        $('#sn').on('keyup', function (e) {
            if (e.keyCode == 13) {
                $.table.search();
                return false;
            }

        });
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            createBatchUrl: prefix + "/batchAdd",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            modalName: "Device",
            search: false,
            showExport: true,
            showImport: true,
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'sn',
                    title: 'Device SN',
                    sortable: true
                },
                {
                    field: 'type',
                    title: 'Type',
                    sortable: true,
                    formatter: function (value, row, index) {
                        return $.table.selectDictText(deviceType, value);
                    }
                },
                {
                    field: 'status',
                    title: 'Status',
                    sortable: true,
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(deviceStatus, value);
                    }
                },
                {
                    field: 'producer',
                    title: 'Manufacturer',
                    sortable: true,
                    formatter: function (value, row, index) {
                        return $.table.selectDictText(producerList, value);
                    }
                },
                {
                    field: 'model',
                    title: 'Model',
                    sortable: true,
                    formatter: function (value, row, index) {
                        return $.table.selectDictText(modelList, value);
                    }
                },
                {
                    field: 'deptName',
                    title: 'Institution',
                    sortable: true,
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (value == "" || value == null) {
                            value = "-";
                        }
                        actions.push("<span>" + value + "</span>");
                        return actions.join('');
                    }
                },
                {
                    field: 'productKey',
                    title: 'Product Key'
                },
                {
                    field: 'os',
                    title: 'Operation System',
                    sortable: true,
                    visible: false
                },
                {
                    field: 'version',
                    title: 'Version',
                    sortable: true,
                    visible: false
                },
                {
                    field: 'createTime',
                    title: 'Create Time',
                    sortable: true
                }]
        };
        $.table.init(options);
    });
    $.tree.selectDept($('#treeId'));
    $.tree.getTreeName($('#treeId').val());

    function editConfig(id, editUrl, modalName) {
        var url = "/404.html";
        if ($.common.isNotEmpty(id)) {
            url = (editUrl || $.table._option.updateUrl).replace("{id}", id);
        } else {
            var id = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
            if (id.length == 0) {
                $.modal.alertWarning("Please select a row");
                return;
            }
            url = (editUrl || $.table._option.updateUrl).replace("{id}", id);
        }
        $.modal.open("Edit" + (modalName || edit$.table._option.modalName), url);
    }
</script>
</body>
<form id="importForm" enctype="multipart/form-data" class="mt20 mb10" style="display: none;">
    <div class="col-xs-offset-1">
        <input type="file" id="file" name="file"/>
        <div class="mt10 pt5">
            <input type="checkbox" id="updateSupport" name="updateSupport" title="If the device already exists, update this line."> Update Existing Device
            &nbsp; <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i
                class="fa fa-file-excel-o"></i> Template</a>
        </div>
        <font color="red" class="pull-left mt10">
            Note：Please upload the file in XLS or XLSX format
        </font>
    </div>
</form>
</html>