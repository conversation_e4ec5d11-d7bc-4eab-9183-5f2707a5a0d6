<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-device-add">
			<div class="form-group">	
				<label class="col-sm-3 control-label">Device SN：</label>
				<div class="col-sm-8">
					<input id="sn" name="sn" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">Name：</label>
				<div class="col-sm-8">
					<input id="name" name="name" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Product Key：</label>
				<div class="col-sm-8">
					<input id="productKey" name="productKey" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Type：</label>
				<div class="col-sm-8">
					<select id="type" name="type"  class="form-control m-b" th:with="type=${@dict.getType('t_device_type')}">
						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Status：</label>
				<div class="col-sm-8">
					<select id="status" name="status"  class="form-control m-b" th:with="type=${@dict.getType('t_device_status')}">
						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Manufacturer：</label>
				<div class="col-sm-8">
					<select id="producer" name="producer"  class="form-control m-b" th:with="type=${@dict.getType('producer_list')}">
						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
					</select>
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">Model：</label>
				<div class="col-sm-8">
					<select id="model" name="model"  class="form-control m-b" th:with="type=${@dict.getType('model_list')}">
						<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Institution：</label>
				<div class="col-sm-8">
					<input id="treeId"  name="deptId" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Group：</label>
				<div class="col-sm-8">
					<select id="groupId" name="groupId" class="form-control m-b" th:with="groups=${@groupService.selectGroupList()}">
						<option value="">None</option>
						<option th:each="group : ${groups}" th:text="${group.groupName}" th:value="${group.id}"></option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Remark：</label>
				<div class="col-sm-8">
					<input id="remark" name="remark" class="form-control" type="text">
				</div>
			</div>

		</form>
	</div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var checkUnique = ctx + "msgcenter/device";
		const prefix = ctx + "msgcenter/aliyunDevice";
		$("#form-device-add").validate({
			rules:{
				sn:{
					required:true,
					remote: {
						url: checkUnique + "/checkSnUnique",
						type: "post",
						dataType: "json",
						data: {
							sn: function () {
								return $.common.trim($("#sn").val());
							}
						},
						dataFilter: function (data, type) {
							return $.validate.unique(data);
						}
					}
				},
				treeId:{
					required:true,
				},
				deptId:{
					required:true,
				}
			},
			messages: {
				"sn": {
					remote: "The device already exists"
				}
			}
		});
		$.tree.selectDept($('#treeId'));
		$.tree.getTreeName($('#treeId').val());
		function submitHandler() {
	        if ($.validate.form()) {
				if(!$('#treeId').val()){
					$.modal.msgError("Please select a institution");
					return;
				}
				const formData = new FormData(document.getElementById("form-device-add"));
				const config = {
					url: prefix + "/add",
					type: "post",
					dataType: "json",
					data: formData,
					async: true,
					timeout: 0,
					processData: false,
					contentType: false,
					success: function (result) {
						const data = result.data;
						const resultMsg = data.msg;
						const status = data.status;

						if (status === '1') {
							window.parent.$.table.refresh();
							window.parent.$.modal.alertSuccess(resultMsg);
							$.modal.close();
						} else {
							window.parent.$.table.refresh();
							window.parent.$.modal.alertError(resultMsg);
						}

					}
				};
				$.ajax(config);
	        }
	    }
	</script>
</body>
</html>