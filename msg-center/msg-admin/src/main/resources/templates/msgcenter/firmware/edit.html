<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-firmware-edit" th:object="${firmware}">
            <input id="id" name="id" th:field="*{id}"  type="hidden">
			<div class="form-group">	
				<label class="col-sm-3 control-label">任务名称：</label>
				<div class="col-sm-8">
					<input id="title" name="title" th:field="*{title}" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">任务内容：</label>
				<div class="col-sm-8">
					<input id="content" name="content" th:field="*{content}" class="form-control" type="text">
				</div>
			</div>

			<div class="form-group">
				<label class="col-sm-3 control-label">备注信息：</label>
				<div class="col-sm-8">
					<input id="remark" name="remark" th:field="*{remark}" class="form-control" type="text">
				</div>
			</div>
		</form>
    </div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var prefix = ctx + "msgcenter/firmware"
		$("#form-firmware-edit").validate({
			rules:{
				xxxx:{
					required:true,
				},
			}
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/edit", $('#form-firmware-edit').serialize());
	        }
	    }
	</script>
</body>
</html>
