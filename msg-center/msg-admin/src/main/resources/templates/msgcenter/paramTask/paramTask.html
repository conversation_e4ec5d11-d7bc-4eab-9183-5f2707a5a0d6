<!DOCTYPE HTML>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">

<div class="container-div">
	<div class="row">
		<div class="col-sm-12 search-collapse">
			<form id="formId">
				<div class="select-list">
					<ul>
						<input id="jobId" name="jobId" th:value="*{jobId}" hidden/>

						<li>
							Device SN：<input type="text" name="termSeq"/>
						</li>

						<li>
							<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;Search</a>
							<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;Reset</a>
						</li>
					</ul>
				</div>
			</form>
		</div>
		<div class="col-sm-12 select-table">
			<div class="table-view-cont">
				<div class="table-view">
					<table>
						<tbody>
							<td>Param Content：</td>
							<td th:text="${paramJob.paramContent}"></td>


						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div class="btn-group-sm hidden-xs" id="toolbar" role="group">
			<a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="msgcenter:paramTask:export">
				<i class="fa fa-download"></i> Export
			</a>
			<!--<a class="btn btn-success" th:href="@{'/msgcenter/downloadJob'}">
                <i class="fa fa-undo"></i> 返回列表
            </a>-->
		</div>
		<div class="col-sm-12 select-table table-striped">
			<table id="bootstrap-table" data-mobile-responsive="true"></table>
		</div>
	</div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
	var editFlag = [[${@permission.hasPermi('msgcenter:paramTask:edit')}]];
	var removeFlag = [[${@permission.hasPermi('msgcenter:paramTask:remove')}]];

	var jobStatus = [[${@dict.getType('job_status')}]];
	var prefix = ctx + "msgcenter/paramTask";
	function queryParams(params) {
		return {
			jobId:       /*<![CDATA[*/[[${jobId}]]/*]]>*/,
			pageSize:       params.limit,
			pageNum:        params.offset / params.limit + 1,
			searchValue:    params.search,
			orderByColumn:  params.sort,
			isAsc:          params.order
		};
	}
	$(function() {
		var options = {
			url: prefix + "/list",
			createUrl: prefix + "/add",
			updateUrl: prefix + "/edit/{id}",
			removeUrl: prefix + "/remove",
			exportUrl: prefix + "/export",
			queryParams: queryParams,
			modalName: "Param Task",
			search: false,
			showExport: true,
			columns: [{
				checkbox: true
			},
				{
					field : 'id',
					title : 'ID',
					visible: false
				},

				{
					field : 'termSeq',
					title : 'Device SN',
					sortable: true
				},
				{
					field : 'dlFlag',
					title : 'Download Status',
					sortable: true,
					formatter: function(value, row, index) {
						return $.table.selectDictText(jobStatus, value);
					}
				},
				{
					field : 'recordCreateTime',
					title : 'Create Time',
					sortable: true
				},
				{
					field : 'resultMessage',
					title : 'Result Message',
					sortable: true
				},

				{
					title: 'Operation',
					align: 'center',
					formatter: function(value, row, index) {
						var actions = [];
						actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="#" onclick="$.operate.remove(\'' + row.taskId + '\')"><i class="fa fa-remove"></i>Delete</a>');
						return actions.join('');
					}
				}]
		};
		$.table.init(options);
	});
</script>
</body>
</html>