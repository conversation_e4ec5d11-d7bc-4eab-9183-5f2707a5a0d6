<!DOCTYPE HTML>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">

<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            设备序列号：<input type="text" name="reserve1"/>
                        </li>

                        <li>
                            消息状态：<select name="status" th:with="type=${@dict.getType('log_msg_status')}">
                            <option value="">所有</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"></option>
                        </select>
                        </li>
                        <li class="select-time">
                            <label>付款时间范围： </label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="startTime"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="endTime"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" id="search-btn"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div id="table-cont" class="hide col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
        <div id="tip-cont" class="col-sm-12 select-table table-striped">
            <div class="none-info search-tip"></div>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('msgcenter:logMsgPush:edit')}]];
    var removeFlag = [[${@permission.hasPermi('msgcenter:logMsgPush:remove')}]];
    var logMsgStatus = [[${@dict.getType('log_msg_status')}]];
    var payChannel = [[${@dict.getType('voice_channel')}]];
    var prefix = ctx + "msgcenter/logMsgPush";

    $(function () {
        var options = {
            url: prefix + "/listHistory",
            modalName: "历史交易消息记录",
            search: false,
            showExport: false,
            columns: [{
                checkbox: true
            },
                {
                    field: 'reserve1',
                    title: '设备序列号',
                    sortable: true
                },
                {
                    field: 'createTime',
                    title: '下发时间',
                    sortable: true
                },
                {
                    field: 'payTime',
                    title: '付款时间',
                    sortable: true
                },
                /*{
                    field: 'id',
                    title: '流水号',
                    sortable: true
                },*/
                {
                    field: 'content',
                    title: '付款渠道',
                    formatter: function (value, row, index) {
                        value = JSON.parse(value);
                        return $.table.selectDictLabel(payChannel, value.channel);
                    }
                },
                {
                    field: 'content',
                    title: '金额',
                    formatter: function (value, row, index) {
                        value = JSON.parse(value);
                        return value.money;
                    }
                },
                {
                    field: 'content',
                    title: '广告内容',
                    formatter: function (value, row, index) {
                        value = JSON.parse(value);
                        return value.advert;
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    sortable: true,
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(logMsgStatus, value);
                    }
                },

                {
                    title: '操作',
                    align: 'center',
                    visible: false,
                    formatter: function (value, row, index) {
                        var actions = [];
                        /*actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="#" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');*/
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="#" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
        $('#search-btn').on('click', function () {
            search();
        });
    });

    function search() {
        $.table.search();
        $('#table-cont').addClass('hide');
        $('#tip-cont').addClass('hide');
        $('#formId input').each(function () {
            if ($(this).val()) {
                $('#table-cont').removeClass('hide')
            }
        })
        if ($('#table-cont').hasClass('hide')) {
            $('#tip-cont').removeClass('hide');
        }
    }
</script>
</body>
</html>