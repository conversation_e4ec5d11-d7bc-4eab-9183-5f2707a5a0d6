<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
	<form class="form-horizontal m" id="form-mqttUser-add">
		<div class="form-group">
			<label class="col-sm-3 control-label">用户名：</label>
			<div class="col-sm-8">
				<input id="username" name="username" class="form-control" type="text">
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-3 control-label">密码：</label>
			<div class="col-sm-8">
				<input id="password" name="password" class="form-control" type="text">
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-3 control-label">设备SN号：</label>
			<div class="col-sm-8">
				<input id="remark" name="remark" class="form-control" type="text">
			</div>
		</div>
		<!--<div class="form-group">
			<label class="col-sm-3 control-label">是否超级用户：</label>
			<div class="col-sm-8">
				<input id="isSuperuser" name="isSuperuser" class="form-control" type="text">
			</div>
		</div>-->

	</form>
</div>
<div th:include="include::footer"></div>
<script type="text/javascript">
	var prefix = ctx + "msgcenter/mqttUser"
	$("#form-mqttUser-add").validate({
		rules:{
			username:{
				required:true,
			},
			password:{
				required:true,
			},
			isSuperuser:{
				required:true,
			},
		}
	});

	function submitHandler() {
		if ($.validate.form()) {
			$.operate.save(prefix + "/add", $('#form-mqttUser-add').serialize());
		}
	}
</script>
</body>
</html>
