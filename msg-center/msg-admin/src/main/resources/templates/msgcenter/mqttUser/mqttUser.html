<!DOCTYPE HTML>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">

<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            用户名：<input type="text" name="username"/>
                        </li>

                        <li>
                            设备SN号：<input type="text" name="remark"/>
                        </li>

                        <!--<li>
                            是否超级用户：<input type="text" name="isSuperuser"/>
                        </li>-->

                        <!--<li>
                            创建时间：<input type="text" name="created"/>
                        </li>-->

                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm hidden-xs" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="msgcenter:mqttUser:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary btn-edit disabled" onclick="$.operate.edit()"
               shiro:hasPermission="msgcenter:mqttUser:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger btn-del btn-del disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="msgcenter:mqttUser:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="msgcenter:mqttUser:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('msgcenter:mqttUser:edit')}]];
    var removeFlag = [[${@permission.hasPermi('msgcenter:mqttUser:remove')}]];
    var prefix = ctx + "msgcenter/mqttUser";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "MQTT认证用户",
            search: false,
            showExport: true,
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: '编号',
                    visible: false
                },
                {
                    field: 'remark',
                    title: '设备SN号',
                    sortable: true
                },
                {
                    field: 'username',
                    title: '用户名',
                    sortable: true
                },
                {
                    field: 'password',
                    title: '秘钥'
                },
                {
                    field: 'isSuperuser',
                    title: '是否超级用户',
                    sortable: true,
                    visible: false
                },
                {
                    field: 'created',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="#" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="#" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>