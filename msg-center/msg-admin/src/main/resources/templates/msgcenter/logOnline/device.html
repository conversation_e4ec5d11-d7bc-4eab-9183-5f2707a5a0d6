<!DOCTYPE HTML>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<style>
    .inp-label {
        min-width: 65px;
        display: inline-block;
    }
</style>
<body class="gray-bg">

<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId" onkeydown="if(event.keyCode==13){return false;}">
                <div class="select-list">
                    <ul>
                        <li>
                            <span class="inp-label">Device SN</span>：<input type="text" name="sn" id="sn"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;Search</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;Reset</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var viewFlag = [[${@permission.hasPermi('msgcenter:logOnline:view')}]];
    var prefix = ctx + "msgcenter/device";
    var prefix2 = ctx + "msgcenter/logOnline";
    var deviceStatus = [[${@dict.getType('t_device_status')}]];
    var deviceType = [[${@dict.getType('t_device_type')}]];
    var producerList = [[${@dict.getType('producer_list')}]];
    var modelList = [[${@dict.getType('model_list')}]];
    var broadcastPrefix = ctx + "msgcenter/broadcastconfig";
    $(function () {
        $('#sn').on('keyup', function (e) {
            if (e.keyCode == 13) {
                $.table.search();
                return false;
            }

        });
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            createBatchUrl: prefix + "/batchAdd",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            modalName: "Device",
            search: false,
            showExport: true,
            showImport: true,
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'sn',
                    title: 'Device SN',
                    sortable: true
                },
                {
                    field: 'type',
                    title: 'Type',
                    sortable: true,
                    formatter: function (value, row, index) {
                        return $.table.selectDictText(deviceType, value);
                    }
                },
                {
                    field: 'status',
                    title: 'Status',
                    sortable: true,
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(deviceStatus, value);
                    }
                },
                {
                    field: 'producer',
                    title: 'Manufacturer',
                    sortable: true,
                    formatter: function (value, row, index) {
                        return $.table.selectDictText(producerList, value);
                    }
                },
                {
                    field: 'model',
                    title: 'Model',
                    sortable: true,
                    formatter: function (value, row, index) {
                        return $.table.selectDictText(modelList, value);
                    }
                },
                {
                    field: 'deptName',
                    title: 'Institution',
                    sortable: true,
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (value == "" || value == null) {
                            value = "-";
                        }
                        actions.push("<span>" + value + "</span>");
                        return actions.join('');
                    }
                },
                {
                    field: 'groupName',
                    title: 'Group',
                    sortable: true,
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (value == "" || value == null) {
                            value = "-";
                        }
                        actions.push("<span>" + value + "</span>");
                        return actions.join('');
                    }
                },
                {
                    field: 'os',
                    title: 'Operation System',
                    sortable: true,
                    visible: false
                },
                {
                    field: 'version',
                    title: 'Software Version',
                    sortable: true,
                    visible: false
                },
                {
                    field: 'createTime',
                    title: 'Create Time',
                    sortable: true
                },
                {
                    title: 'Operation',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + viewFlag + '" href="#" onclick="viewLog(\'' + row.sn + '\')"><i class="fa fa-view"></i>Switch Record</a> ');

                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
function viewLog(sn){
    $.modal.open("Switch List", prefix2+"?sn="+sn);
}


</script>
</body>

</html>