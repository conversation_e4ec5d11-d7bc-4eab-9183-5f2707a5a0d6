<!DOCTYPE html>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">

<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<!--360浏览器优先以webkit内核解析-->
<title>平台介绍</title>
<head th:include="include :: header"></head>
<link th:href="@{/js/datetime/daterangepicker.css}" rel="stylesheet"/>
<style>
    .report-card {
        display: inline-block;
        text-align: left;
        padding-left: 10%;
    }
    .badge-primary {
        background-color: #04BE02;
        color: #FFF;
    }
    #chart2 {
        display: flex;
        justify-content: center;
    }
    #chart3 {
        display: flex;
        justify-content: center;
    }
</style>
<body class="gray-bg">

<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <span class="inp-label">Institution</span>：<input type="text" name="deptId" id="treeId" />
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" id="search-btn"><i class="fa fa-search"></i>&nbsp;Search</a>
                            <a class="btn btn-warning btn-rounded btn-sm" id="reset-btn"><i class="fa fa-refresh"></i>&nbsp;Reset</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div id="main-cont">
            <div class="col-sm-12 select-table">
                <div class="card-title log-card-title">
                    <h3>Summary</h3>
                </div>
                <div class="report-card-group">

                    <div class="report-card">
                        <div>
                            <div class="meta">Transaction</div>
                            <div class="total" id="requestNum">0</div>
                        </div>
                    </div>

                    <div class="report-card">
                        <div>
                            <div class="meta">Broadcast</div>
                            <div class="total" id="publishNum">0</div>
                        </div>
                    </div>
                    <div class="report-card">
                        <div>
                            <div class="meta">Broadcast Successful (%)</div>
                            <div class="total" id="successRate">0</div>
                        </div>
                    </div>
<!--                    <div class="report-card">-->
<!--                        <div>-->
<!--                            <div class="meta">终端在线总数</div>-->
<!--                            <div class="total" id="onlineNum">0</div>-->
<!--                        </div>-->
<!--                    </div>-->

                </div>



                <div class="report-card-group">

                    <div class="report-card">
                        <div>
                            <div class="meta">Device</div>
                            <div class="total" id="dev_total">0</div>
                        </div>
                    </div>

                    <div class="report-card">
                        <div>
                            <div class="meta">Online Device</div>
                            <div class="total" id="dev_onlineNum">0</div>
                        </div>
                    </div>
                    <div class="report-card">
                        <div>
                            <div class="meta">Active Device (%)</div>
                            <div class="total" id="dev_rate">0</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-12 select-table">
                <div class="card-title log-card-title">
                    <h3>Trend</h3>
                </div>
                <div id="chart2" style="height: 250px;margin:auto"></div>
            </div>

            <div class="col-sm-12 select-table">
                <div class="card-title log-card-title">
                    <h3>Device</h3>
                </div>
                <div id="chart3" style="height: 250px;margin:auto"></div>
            </div>

        </div>

    </div>
</div>


</body>
<div th:include="include :: footer"></div>
<script th:src="@{/js/echarts.js}"></script>
<script th:src="@{/js/datetime/moment.min.js}"></script>
<script th:src="@{/js/datetime/daterangepicker.js}"></script>
<script>


    $(function() {
        var option = {
            pagination: true,
            pageNumber: 1,                                      // 初始化加载第一页，默认第一页
            pageSize: 5,                                       // 每页的记录行数（*）
            pageList: [5, 10, 15],
        }
        $('#bootstrap-table').bootstrapTable({
            columns: [{
                field: 'name',
                title: 'Cluster Name',
                visible: true
            }, {
                field: 'node_status',
                title: 'Status',
                formatter:function (value, row, index) {
                    if (value == 'Running')  {
                        return  "<span class='badge badge-primary'>" + value + "</span>"
                    }
                    else {
                        return  "<span class='badge badge-danger'>" + value + "</span>"
                    }
                }
            }],
            data: [],
            ...option
        });


        $.tree.selectDept($('#treeId'));
        $.tree.getTreeName($('#treeId').val());

        $('#search-btn').on('click', handleSearch);
        $('#reset-btn').on('click', function () {
            $('#treeId').val("")
            $.tree.getTreeName("");
        });

        handleSearch();

    });

    function handleSearch () {

        var date = new Date();
        var dateFormat = date.getFullYear() + '-' + (date.getMonth() + 1) + "-" + date.getDate();
        var startTime = dateFormat + ' 00:00:00';
        var endTime = dateFormat + ' 23:59:59';

        // $.service.post(ctx + 'msgcenter/logReceive/getLogReceiveTotal', {"deptId" : $('#treeId').val(), "startTime" : startTime, "endTime" : endTime}, function (res) {
        //     $('#requestNum').html(res.data)
        // });

        $.service.post(ctx + 'msgcenter/logMoney/getLogMoneyTotal', {"deptId" : $('#treeId').val()}, function (res1) {
            $('#requestNum').html(res1.data)
            $.service.post(ctx + 'msgcenter/logMoney/getLogMoneyTotal', {"deptId" : $('#treeId').val(), "status" : "4"}, function (res2) {
                $('#publishNum').html(res2.data);
                $('#successRate').html((res1.data === '0' ? 0 : (res2.data / res1.data * 100).toFixed(2)));
            });
        });


        $.service.post(ctx + 'msgcenter/device/getDeviceTotal', {"deptId" : $('#treeId').val()}, function (res1) {
            $('#dev_total').html(res1.data)
            $.service.post(ctx + 'msgcenter/device/getDeviceTotal', {"deptId" : $('#treeId').val(), "networkStatus" : "1"}, function (res2) {
                $('#dev_onlineNum').html(res2.data)
                $('#dev_rate').html(res1.data === '0' ? 0 : (res2.data / (res1.data) * 100).toFixed(2));
            });
        });


        $.service.post(ctx + 'msgcenter/emqClient/listEmqNotes', {}, function (res) {

            $('#bootstrap-table').bootstrapTable('load', {
                data: res.data.rows
            });
        });

        var deptId = $('#treeId').val() =='' ? [[${user.deptId}]] : $('#treeId').val();
        $.service.post(ctx + 'msgcenter/logMoneyReport/indexList', $.extend({"deptId" : deptId}, getWeekTimeRange()) , function (res) {
            list = res.data;
            initChartData(list);
            initChart();
        });


        $.service.post(ctx + 'msgcenter/terminalOnlineReport/indexList', $.extend({"deptId" : deptId}, getTimeRange()) , function (res) {
            list = res.data;
            initChart2(list);
        });
    }

    var chart2List = [new Array(),new Array(),new Array()];
    // var timeShowX = [];
    var chart2 = echarts.init($('#chart2').get(0));
    var chart3 = echarts.init($('#chart3').get(0));

    var timeX = [];
    var timeShowX = [];

    var list = [];//获取数据进行存储

    var initChart = function () {
        var option2 = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: chart2List[2]
            },
            xAxis: {
                name: 'Time',
                type: 'category',
                boundaryGap: false,
                axisLine: {
                    lineStyle: {
                        color: '#e5e5e5'
                    }
                },
                axisLabel: {
                    color: '#999',
                    interval: Math.floor(timeX.length/6) || 1
                },
                data: timeX
            },
            calculable: true,
            yAxis: {
                name: 'Number',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#e5e5e5'
                    }
                },
                axisLabel: {
                    color: '#999'
                },
            },
            series: [{
                name: chart2List[2][0],
                data: chart2List[0],
                type: 'line',
                smooth: true,
                color: '#488af0',
                areaStyle: {
                    opacity: 0.2
                }
            },{
                name: chart2List[2][1],
                data: chart2List[1],
                type: 'line',
                smooth: true,
                color: '#1ab394',
                areaStyle: {
                    opacity: 0.2
                }}]
        };
        chart2.setOption(option2);
        option2.yAxis.minInterval = 1;
    }


    var initChart2 = function (res) {
        var deptNameArray = new Array();
        var totalArray = new Array();
        var totalOnlineArray = new Array();

        [...res].forEach(function (val) {
            deptNameArray.push(val.deptName);
            totalArray.push(val.total);
            totalOnlineArray.push(val.totalOnline);
        });

        var option3 = {
            xAxis: {
                type: 'category',
                data: deptNameArray
            },
            yAxis: {
                type: 'value'
            },
            legend: {
                data: ['Total Number', 'Online Number']
            },
            series: [{
                name: 'Total Number',
                data: totalArray,
                type: 'bar',
                label: {
                    show: true,
                    position: 'top'
                }
            },
            {
                name: 'Online Number',
                data: totalOnlineArray,
                type: 'bar',
                label: {
                    show: true,
                    position: 'top'
                }
            }]
        };
        chart3.setOption(option3);
        option3.yAxis.minInterval = 1;
    }

    //获取时间范围
    var getTimeRange = function () {

        var day = moment().subtract(0, 'days').startOf('day');
        var dayList = [];

        for(var i=0; i<24;i++){
            dayList.push(moment(day));
            day.add(1,'h');
        }

        // timeShowX = [].concat(dayList).map(function (val, index) {
        //     return index === 0?val.format('MM-DD HH:mm'):val.format('HH:mm');
        // });
        //
        // timeX = [].concat(dayList).map(function (val, index) {
        //     return val.format('YYYY-MM-DD HH:mm:ss');
        // });


        var date = new Date();
        var today = date.getFullYear() + '-' + (date.getMonth() + 1) + "-" + date.getDate() ;

        return { startDate:  today + ' 00:00:00', endDate: today + ' 23:59:59', dateType: 'hour'}
    }


    //获取时间范围
    var getWeekTimeRange = function () {

        var daysArray = [];
        for (var i = 0; i < 7; i++) {
            daysArray.push(moment().subtract(i, 'days').startOf('day').format('MM-DD'));
        }

        // 反转数组，使最近的日期排在前面
        daysArray.reverse();

        timeShowX = daysArray.map(function (date) {
            return date;
        });

        timeX = daysArray.map(function (date) {
            return date;
        });


        var date = new Date();
        var today = date.getFullYear() + '-' + (date.getMonth() + 1).toString().padStart(2,0) + "-" + date.getDate().toString().padStart(2,0);

        date.setDate(date.getDate() - 7);
        var sevenDaysAgo = date.getFullYear() + '-' + (date.getMonth() + 1).toString().padStart(2,0) + '-' + date.getDate().toString().padStart(2,0);

        return { startDate:  sevenDaysAgo , endDate: today}
    }




    //初始化图标
    var initChartData = function(res) {
        var list = res.listNoChannel;
        var data = {};

        getTimeRange();
        [...list].forEach(function (val) {
            var name = val.paidTime? val.paidTime.substring(5, 10):'';
            data[name] = {
                paidCount: val.paidCount,
                money: val.money,
                snCount: val.snCount,
                successCount: val.successCount
            }
        });

        chart2List = [new Array(),new Array(),["Transaction", "Transaction (per device)"]];

        timeX.forEach(function (t) {
            if(data[t]){
                chart2List[0].push(data[t].paidCount);
                chart2List[1].push(data[t].snCount == 0 ? 0 : parseInt(data[t].paidCount / data[t].snCount));
            }
            else{
                chart2List[0].push(0);
                chart2List[1].push(0);
            }
        })
    }

</script>
</html>