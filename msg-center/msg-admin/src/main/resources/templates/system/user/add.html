<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-user-add">
			<input name="deptId"  type="hidden" id="treeId"/>
			<input type="hidden" name="userName" id="userName">
			<div class="form-group">
				<label class="col-sm-3 control-label ">Login Name<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" id="loginName" name="loginName"/>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Institution<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="deptName" onclick="selectDeptTree()" readonly="true" id="treeName">
				</div>
			</div>
<!--			<div class="form-group">-->
<!--				<label class="col-sm-3 control-label">Username：</label>-->
<!--				<div class="col-sm-8">-->
<!--					<input class="form-control" type="text" name="userName" id="userName">-->
<!--				</div>-->
<!--			</div>-->
			<div class="form-group">
				<label class="col-sm-3 control-label">Password<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<input class="form-control" type="password" name="password" id="password">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Confirm Password<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<input class="form-control" type="password" name="confirm" id="confirm">
					<span class="help-block m-b-none"><i class="fa fa-info-circle"></i> Please confirm your password</span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">E-mail：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="email" id="email">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Phone：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="phone" id="phone">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Gender：</label>
				<div class="col-sm-8">
					<select id="sex" class="form-control m-b" th:with="type=${@dict.getType('sys_user_sex')}">
	                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
	                </select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Status：</label>
				<div class="col-sm-8">
					<div class="onoffswitch">
                         <input type="checkbox" th:checked="true" class="onoffswitch-checkbox" id="status" name="status">
                         <label class="onoffswitch-label" for="status">
                             <span class="onoffswitch-inner"></span>
                             <span class="onoffswitch-switch"></span>
                         </label>
                     </div>
				</div>
			</div>
<!--			<div class="form-group">-->
<!--				<label class="col-sm-3 control-label">Post：</label>-->
<!--				<div class="col-sm-8">-->
<!--					<select id="post" name="post" class="form-control select2-hidden-accessible" multiple="">-->
<!--						<option th:each="post:${posts}" th:value="${post.postId}" th:text="${post.postName}" th:disabled="${post.status == '1'}"></option>-->
<!--					</select>-->
<!--					-->
<!--				</div>-->
<!--			</div>-->
			<div class="form-group">
				<label class="col-sm-3 control-label">Role<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<label th:each="role:${roles}" class="check-box">
						<input name="role" type="checkbox" th:value="${role.roleId}" th:text="${role.roleName}" th:disabled="${role.status == '1'}">
					</label>
				</div>
			</div>
		</form>
	</div>
	<div th:include="include::footer"></div>
	<script th:src="@{/ajax/libs/select/select2.js}"></script>
	<script th:src="@{/js/md5.min.js}"></script>
	<script>
        $("#form-user-add").validate({
        	rules:{
        		loginName:{
        			required:true,
        			minlength: 2,
        			maxlength: 20,
        			remote: {
                        url: ctx + "system/user/checkLoginNameUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                        	name : function() {
                                return $.common.trim($("#loginName").val());
                            }
                        },
                        dataFilter: function(data, type) {
                        	return $.validate.unique(data);
                        }
                    }
        		},
        		// userName:{
        		// 	required:true,
        		// },
        		deptName:{
        			required:true,
        		},
        		password:{
        			required:true,
        			minlength: 5,
        			maxlength: 20
        		},
				confirm: {
					required: true,
					equalTo: "#password"
				},
        		email:{
        			required:false,
                    email:true,
                    remote: {
                        url: ctx + "system/user/checkEmailUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            name: function () {
                                return $.common.trim($("#email").val());
                            }
                        },
                        dataFilter: function (data, type) {
                        	return $.validate.unique(data);
                        }
                    }
        		},
        		phone:{
        			required:false,
					digits:true,
                    remote: {
                        url: ctx + "system/user/checkPhoneUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            name: function () {
                                return $.common.trim($("#phone").val());
                            }
                        },
                        dataFilter: function (data, type) {
                        	return $.validate.unique(data);
                        }
                    }
        		},
        	},
        	messages: {
                "loginName": {
                    remote: "Login Name already exists"
                },
        		"email": {
                    remote: "E-mail already exists"
                },
        		"phone":{
                	remote: "Phone Number already exists"
        		}
            }
        });
        
        function submitHandler() {
	        if ($.validate.form()) {
	        	add();
	        }
	    }

        function add() {
        	var userId = $("input[name='userId']").val();
        	var deptId = $("input[name='deptId']").val();
        	var loginName = $("input[name='loginName']").val();
        	var userName = $("input[name='loginName']").val();
        	var password = $("input[name='password']").val();
        	var email = $("input[name='email']").val();
        	var phone = $("input[name='phone']").val();
        	var sex = $("#sex option:selected").val();
        	var status = $("input[name='status']").is(':checked') == true ? 0 : 1;
        	var roleIds = $.form.selectCheckeds("role");
        	// var postIds = $.form.selectSelects("post");
        	$.ajax({
        		cache : true,
        		type : "POST",
        		url : ctx + "system/user/add",
        		data : {
        			"userId": userId,
        			"deptId": deptId,
        			"loginName": loginName,
        			"userName": userName,
        			"password": md5(password),
        			"email": email,
        			"phone": phone,
        			"sex": sex,
        			"status": status,
        			"roleIds": roleIds
        			// "postIds": postIds
        		},
        		async : false,
        		error : function(request) {
        			$.modal.alertError("System Error");
        		},
        		success : function(data) {
        			$.operate.successCallback(data);
        		}
        	});
        }

        /*用户管理-新增-选择部门树*/
        function selectDeptTree() {
        	var treeId = $("#treeId").val();
        	var deptId = $.common.isEmpty(treeId) ? "1" : $("#treeId").val();
        	var url = ctx + "system/dept/selectDeptTree/" + deptId;
			var options = {
				title: 'Select Institution',
				width: "380",
				url: ctx + "system/dept/selectDeptTree/" + deptId,
				callBack: doSubmit
			};
			$.modal.openOptions(options);
		}
		
		function doSubmit(index, layero){
			var tree = layero.find("iframe")[0].contentWindow.$._tree;
			var body = layer.getChildFrame('body', index);
    		$("#treeId").val(body.find('#treeId').val());
    		$("#treeName").val(body.find('#treeName').val());
    		layer.close(index);
		}
    </script>
</body>
</html>