<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-user-resetPwd">
        <input name="userId" type="hidden" th:value="${user.userId}"/>
        <div class="form-group">
            <label class="col-sm-3 control-label ">Login Name：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" readonly="true" name="loginName" th:value="${user.loginName}"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">Current Password：</label>
            <div class="col-sm-8">
                <input class="form-control" type="password" name="oldPassword" id="oldPassword">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">New Password：</label>
            <div class="col-sm-8">
                <input class="form-control" type="password" name="newPassword" id="newPassword">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">Confirm Password：</label>
            <div class="col-sm-8">
                <input class="form-control" type="password" name="confirm" id="confirm">
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> Please confirm your new password</span>
            </div>
        </div>
    </form>
</div>
<div th:include="include :: footer"></div>
<script th:src="@{/js/md5.min.js}"></script>

<script>
    $("#form-user-resetPwd").validate({
        rules: {
            oldPassword: {
                required: true,
                remote: {
                    url: ctx + "system/user/profile/checkPassword",
                    type: "get",
                    dataType: "json",
                    data: {
                        password: function () {
                            return md5($("input[name='oldPassword']").val());
                        }
                    },
                    dataFilter: function (data, type) {
                        return $.validate.pass(data);
                    }
                }
            },
            newPassword: {
                required: true,
                minlength: 10,
                maxlength: 20
            },
            confirm: {
                required: true,
                equalTo: "#newPassword"
            }
        },
        messages: {
            oldPassword: {
                required: "Please enter your current password",
                remote: "The password you entered is incorrect"
            },
            newPassword: {
                required: "Please enter your new password",
                minlength: "New password must be longer than 10 characters",
                maxlength: "New password must be shorter than 20 characters"
            },
            confirm: {
                required: "Please confirm your new password",
                equalTo: "Two new passwords must be the same"
            }

        }
    });

    function submitHandler() {
        if ($.validate.form()) {
            var oldInput = $("#oldPassword");
            var newInput = $("#newPassword");

            var pwdRegex = RegExp('(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[^a-zA-Z0-9]).{10,20}');

            if (!pwdRegex.test(newInput.val())) {
                $.modal.alertError("您的密码复杂度太低（密码中必须包含大小写字母、数字、特殊字符,且长度大于10），请及时修改密码！");
                return;
            }

            var formData = $('#form-user-resetPwd').awesomeFormSerializer({
                oldPassword: md5(oldInput.val()),
                newPassword: md5(newInput.val())
            });
            $.operate.save(ctx + "system/user/profile/resetPwd", formData);
        }
    }
</script>
</body>

</html>