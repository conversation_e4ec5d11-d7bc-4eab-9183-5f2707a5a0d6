<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<meta charset="utf-8">
<head th:include="include :: header"></head>
<link th:href="@{/ajax/libs/jquery-ztree/3.5/css/metro/zTreeStyle.css}" rel="stylesheet"/>
<style>
body{height:auto;font-family: "Microsoft YaHei";}
button{font-family: "SimSun","Helvetica Neue",Helvetica,Arial;}
</style>
<body class="hold-transition box box-main">
	<input id="treeId"   name="treeId"    type="hidden" th:value="${menu.menuId}"/>
	<input id="treeName" name="treeName"  type="hidden" th:value="${menu.menuName}"/>
	<div class="wrapper"><div class="treeShowHideButton" onclick="$.tree.toggleSearch();">
		<label id="btnShow" title="Show" style="display:none;">︾</label>
		<label id="btnHide" title="Hide">︽</label>
	</div>
	<div class="treeSearchInput" id="search">
		<label for="keyword">Keyword：</label><input type="text" class="empty" id="keyword" maxlength="50">
		<button class="btn" id="btn" onclick="$.tree.searchNode()"> Search </button>
	</div>
	<div class="treeExpandCollapse">
		<a href="#" onclick="$.tree.expand()">Unfold</a> /
		<a href="#" onclick="$.tree.collapse()">Fold</a>
	</div>
	<div id="tree" class="ztree treeselect"></div>
	</div>
	<div th:include="include::footer"></div>
	<script th:src="@{/ajax/libs/jquery-ztree/3.5/js/jquery.ztree.all-3.5.js}"></script>
	<script th:inline="javascript">
		$(function() {
			var url = ctx + "system/menu/menuTreeData";
			var options = {
		        url: url,
		        expandLevel: 1,
		        onClick : zOnClick
		    };
			$.tree.init(options);
		});
		
		function zOnClick(event, treeId, treeNode) {
		    var treeId = treeNode.id;
		    var treeName = treeNode.name;
		    $("#treeId").val(treeId);
		    $("#treeName").val(treeName);
		}
	</script>
</body>
</html>