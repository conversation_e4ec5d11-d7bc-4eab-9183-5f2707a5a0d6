<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-menu-edit" th:object="${menu}">
			<input name="menuId"   type="hidden" th:field="*{menuId}"   />
			<input id="treeId" name="parentId" type="hidden" th:field="*{parentId}" />
			<div class="form-group">
				<label class="col-sm-3 control-label ">Parent Menu：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" onclick="selectMenuTree()" id="treeName" readonly="true" th:value="${menu.parentName == null ? '无' : menu.parentName}"/>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Type：</label>
				<div class="col-sm-8">
					<label class="radio-box"> <input type="radio" th:field="*{menuType}" name="menuType" value="M" /> Category </label>
					<label class="radio-box"> <input type="radio" th:field="*{menuType}" name="menuType" value="C" /> Menu </label>
					<label class="radio-box"> <input type="radio" th:field="*{menuType}" name="menuType" value="F" /> Button </label>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Menu Name：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="menuName" id="menuName" th:field="*{menuName}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">URL：</label>
				<div class="col-sm-8">
					<input id="url" name="url" class="form-control" type="text" th:field="*{url}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Permission：</label>
				<div class="col-sm-8">
					<input id="perms" name="perms" class="form-control" type="text" th:field="*{perms}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Ordering：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="orderNum" th:field="*{orderNum}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Icon：</label>
				<div class="col-sm-8">
					<input id="icon" name="icon" class="form-control" type="text" placeholder="Select a icon" th:field="*{icon}">
                    <div class="ms-parent" style="width: 100%;">
                        <div class="icon-drop animated flipInX" style="display: none;max-height:200px;overflow-y:auto">
                            <div data-th-include="system/menu/icon"></div>
                        </div>
                    </div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Status：</label>
				<div class="col-sm-8">
					<div class="radio-box" th:each="dict : ${@dict.getType('sys_show_hide')}">
						<input type="radio" th:id="${dict.dictCode}" name="visible" th:value="${dict.dictValue}" th:field="*{visible}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
		</form>
	</div>
	<div th:include="include::footer"></div>
	 <script>
        var prefix = ctx + "system/menu";

        $(function() {
            var menuType = $('input[name="menuType"]:checked').val();
            menuVisible(menuType);
        });

        $("#form-menu-edit").validate({
        	rules:{
        		menuType:{
        			required:true,
        		},
        		menuName:{
        			required:true,
        			remote: {
                        url: prefix + "/checkMenuNameUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                        	"menuId": function() {
                                return $("#menuId").val();
                            },
                            "parentId": function() {
		                		return $("input[name='parentId']").val();
		                    },
                			"menuName": function() {
                                return $.common.trim($("#menuName").val());
                            }
                        },
                        dataFilter: function(data, type) {
                        	return $.validate.unique(data);
                        }
                    }
        		},
        		orderNum:{
        			required:true,
        			digits:true
        		},
        	},
        	messages: {
                "menuName": {
                    remote: "Menu Name already exists"
                }
            }
        });
        
        function submitHandler() {
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/edit", $('#form-menu-edit').serialize());
	        }
	    }

        $(function() {
            $("input[name='icon']").focus(function() {
                $(".icon-drop").show();
            });
            $("#form-menu-edit").click(function(event) {
                var obj = event.srcElement || event.target;
                if (!$(obj).is("input[name='icon']")) {
                    $(".icon-drop").hide();
                }
            });
            $(".icon-drop").find(".ico-list i").on("click",
            function() {
                $('#icon').val($(this).attr('class'));
            });
            $('input').on('ifChecked',
            function(event) {
                var menuType = $(event.target).val();
                menuVisible(menuType);
            });
        });

        function menuVisible(menuType) {
            if (menuType == "M") {
                $("#url").parents(".form-group").hide();
                $("#perms").parents(".form-group").hide();
                $("#icon").parents(".form-group").show();
            } else if (menuType == "C") {
                $("#url").parents(".form-group").show();
                $("#perms").parents(".form-group").show();
                $("#icon").parents(".form-group").hide();
            } else if (menuType == "F") {
                $("#url").parents(".form-group").hide();
                $("#perms").parents(".form-group").show();
                $("#icon").parents(".form-group").hide();
            }
        }

        /*菜单管理-修改-选择菜单树*/
        function selectMenuTree() {
        	var menuId = $("#treeId").val();
        	if(menuId > 0) {
        		var url = prefix + "/selectMenuTree/" + menuId;
        		$.modal.open("Select Menu", url, '380', '380');
        	} else {
        		$.modal.alertError("Main menu selection is not allowed");
        	}
        }
        
        function selectMenuTree() {
        	var menuId = $("#treeId").val();
        	if(menuId > 0) {
        		var url = prefix + "/selectMenuTree/" + menuId;
        		var options = {
       				title: 'Menu Selection',
       				width: "380",
       				url: url,
       				callBack: doSubmit
       			};
       			$.modal.openOptions(options);
        	} else {
        		$.modal.alertError("Main menu selection is not allowed");
        	}
		}
		
		function doSubmit(index, layero){
			var body = layer.getChildFrame('body', index);
   			$("#treeId").val(body.find('#treeId').val());
   			$("#treeName").val(body.find('#treeName').val());
   			layer.close(index);
		}
    </script>
</body>
</html>