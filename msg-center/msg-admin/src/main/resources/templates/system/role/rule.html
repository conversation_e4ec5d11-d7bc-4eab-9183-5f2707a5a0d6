<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<meta charset="utf-8">
<head th:include="include :: header"></head>
<link th:href="@{/ajax/libs/jquery-ztree/3.5/css/metro/zTreeStyle.css}" rel="stylesheet"/>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-role-edit" th:object="${role}">
			<input id="roleId" name="roleId" type="hidden" th:field="*{roleId}"/>
			<div class="form-group">
				<label class="col-sm-3 control-label ">Role Name：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="roleName" id="roleName" th:field="*{roleName}" readonly="true"/>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Role Code：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="roleKey" id="roleKey" th:field="*{roleKey}" readonly="true">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Data Scope：</label>
				<div class="col-sm-8">
					<select id="dataScope" name="dataScope" class="form-control m-b">
						<option value="1" th:field="*{dataScope}">Full Data Access</option>
						<option value="2" th:field="*{dataScope}">Custom Data Access</option>
					</select>
					<span class="help-block m-b-none"><i class="fa fa-info-circle"></i> In special cases, set as "Custom Data Access"</span>
				</div>
			</div>
			<div class="form-group" id="roleRule" th:style="'display:' + @{(*{dataScope=='1'} ? 'none' : 'block')} + ''">
				<label class="col-sm-3 control-label">Data Authorization</label>
				<div class="col-sm-8">
					<div id="deptTrees" class="ztree"></div>
				</div>
			</div>
		</form>
	</div>
	<div th:include="include::footer"></div>
	<script th:src="@{/ajax/libs/jquery-ztree/3.5/js/jquery.ztree.all-3.5.js}"></script>
	<script type="text/javascript">
	
	    $(function() {
			var url = ctx + "system/dept/roleDeptTreeData?roleId=" + $("#roleId").val();
			var options = {
				id: "deptTrees",
		        url: url,
		        check: { enable: true, nocheckInherit: true, chkboxType: { "Y": "s", "N": "ps" } },
		        expandLevel: 2
		    };
			$.tree.init(options);
	    });

		function submitHandler() {
		    log.log(1111)
	        if ($.validate.form()) {
	        	edit();
	        }
	    }
		
		function edit() {
			var roleId = $("input[name='roleId']").val();
			var roleName = $("input[name='roleName']").val();
			var roleKey = $("input[name='roleKey']").val();
			var dataScope = $("#dataScope").val();
            var deptIds = $.tree.getCheckedNodes();
			$.ajax({
				cache : true,
				type : "POST",
				url : ctx + "system/role/rule",
				data : {
					"roleId": roleId,
					"roleName": roleName,
					"roleKey": roleKey,
					"dataScope": dataScope,
			        "deptIds": deptIds
				},
				async : false,
				error : function(request) {
					$.modal.alertError("System Error");
				},
				success : function(data) {
					$.operate.successCallback(data);
				}
			});
		}
		
		$("#dataScope").change(function(event){
        	var dataScope = $(event.target).val();
        	dataScopeVisible(dataScope);
        });
		
		function dataScopeVisible(dataScope) {
			if (dataScope == 2) {
	    		$("#roleRule").show();
	    	} else {
	    		$._tree.checkAllNodes(false);
	    		$("#roleRule").hide();
	    	}
		}
	</script>
</body>
</html>