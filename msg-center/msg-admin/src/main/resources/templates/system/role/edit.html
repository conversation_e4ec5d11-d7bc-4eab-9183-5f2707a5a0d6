<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<meta charset="utf-8">
<head th:include="include :: header"></head>
<link th:href="@{/ajax/libs/jquery-ztree/3.5/css/metro/zTreeStyle.css}" rel="stylesheet"/>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-role-edit" th:object="${role}">
			<input id="roleId" name="roleId" type="hidden" th:field="*{roleId}"/>
			<div class="form-group">
				<label class="col-sm-3 control-label ">Role Name<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="roleName" id="roleName" th:field="*{roleName}"/>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Role Code<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="roleKey" id="roleKey" th:field="*{roleKey}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Ordering<span style="color: red; ">*</span>：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="roleSort" id="roleSort" th:field="*{roleSort}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Status：</label>
				<div class="col-sm-8">
					<div class="onoffswitch">
                         <input type="checkbox" th:checked="${role.status == '0' ? true : false}" class="onoffswitch-checkbox" id="status" name="status">
                         <label class="onoffswitch-label" for="status">
                             <span class="onoffswitch-inner"></span>
                             <span class="onoffswitch-switch"></span>
                         </label>
                     </div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Remark：</label>
				<div class="col-sm-8">
					<input id="remark" name="remark" class="form-control" type="text" th:field="*{remark}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">Authorization</label>
				<div class="col-sm-8">
					<div id="menuTrees" class="ztree"></div>
				</div>
			</div>
		</form>
	</div>
	<div th:include="include::footer"></div>
	<script th:src="@{/ajax/libs/jquery-ztree/3.5/js/jquery.ztree.all-3.5.js}"></script>
	<script type="text/javascript">
	     $(function() {
			var url = ctx + "system/menu/roleMenuTreeData?roleId=" + $("#roleId").val();
			var options = {
				id: "menuTrees",
		        url: url,
		        check: { enable: true, nocheckInherit: true, chkboxType: { "Y": "ps", "N": "ps" } },
		        expandLevel: 0
		    };
			$.tree.init(options);
		});
	
		$("#form-role-edit").validate({
			rules:{
				roleName:{
					required:true,
					remote: {
		                url: ctx + "system/role/checkRoleNameUnique",
		                type: "post",
		                dataType: "json",
		                data: {
							"roleId": function() {
							    return $("#roleId").val();
							},
							"roleName": function() {
							    return $.common.trim($("#roleName").val());
							}
		                },
		                dataFilter: function(data, type) {
		                	return $.validate.unique(data);
		                }
		            }
				},
				roleKey:{
					required:true,
					remote: {
		                url: ctx + "system/role/checkRoleKeyUnique",
		                type: "post",
		                dataType: "json",
		                data: {
							"roleId": function() {
								return $("#roleId").val();
							},
							"roleKey": function() {
							    return $.common.trim($("#roleKey").val());
							}
		                },
		                dataFilter: function(data, type) {
		                	return $.validate.unique(data);
		                }
		            }
				},
				roleSort:{
					required:true,
					digits:true
				},
			},
			messages: {
		        "roleName": {
		            remote: "Role Name already exists"
		        },
		        "roleKey": {
		            remote: "Role Code already exists"
		        }
		    }
		});

		function edit() {
			var roleId = $("input[name='roleId']").val();
			var roleName = $("input[name='roleName']").val();
			var roleKey = $("input[name='roleKey']").val();
			var roleSort = $("input[name='roleSort']").val();
			var status = $("input[name='status']").is(':checked') == true ? 0 : 1;
			var remark = $("input[name='remark']").val();
			var menuIds = $.tree.getCheckedNodes();
			$.ajax({
				cache : true,
				type : "POST",
				url : ctx + "system/role/edit",
				data : {
					"roleId": roleId,
					"roleName": roleName,
					"roleKey": roleKey,
					"roleSort": roleSort,
					"status": status,
					"remark": remark,
					"menuIds": menuIds
				},
				async : false,
				error : function(request) {
					$.modal.alertError("System Error");
				},
				success : function(data) {
					$.operate.successCallback(data);
				}
			});
		}
		
		function submitHandler() {
	        if ($.validate.form()) {
	        	edit();
	        }
	    }
	</script>
</body>
</html>