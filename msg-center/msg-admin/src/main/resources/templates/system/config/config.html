<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="gray-bg">
    
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="config-form">
					<div class="select-list">
						<ul>
							<li>
								Config Name：<input type="text" name="configName"/>
							</li>
							<li>
								Config Key：<input type="text" name="configKey"/>
							</li>
							<li>
								System Config：<select name="configType" th:with="type=${@dict.getType('sys_yes_no')}">
									<option value="">All</option>
									<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
								</select>
							</li>
							<li class="select-time">
								<label>Create Time： </label>
								<input type="text" class="time-input" id="startTime" placeholder="Start Time" name="params[beginTime]"/>
								<span>-</span>
								<input type="text" class="time-input" id="endTime" placeholder="End Time" name="params[endTime]"/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;Search</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;Reset</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
	        <div class="btn-group-sm hidden-xs" id="toolbar" role="group">
		        <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:config:add">
		            <i class="fa fa-plus"></i> Add
		        </a>
		        <a class="btn btn-primary btn-edit disabled" onclick="$.operate.edit()" shiro:hasPermission="system:config:edit">
		            <i class="fa fa-edit"></i> Edit
		        </a>
		        <a class="btn btn-danger btn-del disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:config:remove">
		            <i class="fa fa-remove"></i> Delete
		        </a>
		        <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:config:export">
		            <i class="fa fa-download"></i> Export
		        </a>
	        </div>
	        <div class="col-sm-12 select-table table-striped">
	            <table id="bootstrap-table" data-mobile-responsive="true"></table>
	        </div>
	    </div>
	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:config:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:config:remove')}]];
        var datas = [[${@dict.getType('sys_yes_no')}]];
        var prefix = ctx + "system/config";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                sortName: "createTime",
		        sortOrder: "desc",
                modalName: "Param",
                search: false,
		        showExport: false,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'configId',
                    title: 'ID'
                },
                {
                    field: 'configName',
                    title: 'Config Name'
                },
                {
                    field: 'configKey',
                    title: 'Config Key'
                },
                {
                    field: 'configValue',
                    title: 'Config Value'
                },
                {
                    field: 'configType',
                    title: 'System Config',
                    align: 'center',
                    formatter: function(value, row, index) {
                    	return $.table.selectDictLabel(datas, value);
                    }
                },
                {
                    field: 'createTime',
                    title: 'Create Time'
                },
                {
                    title: 'Operation',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="#" onclick="$.operate.edit(\'' + row.configId + '\')"><i class="fa fa-edit"></i>Edit</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="#" onclick="$.operate.remove(\'' + row.configId + '\')"><i class="fa fa-remove"></i>Delete</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>