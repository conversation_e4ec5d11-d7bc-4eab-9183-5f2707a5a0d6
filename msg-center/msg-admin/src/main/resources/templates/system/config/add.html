<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<meta charset="utf-8">
<head th:include="include :: header"></head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-config-add" name="form-config-add">
        <div class="form-group">	
            <label class="col-sm-3 control-label">Config Name：</label>
            <div class="col-sm-8">
                <input id="configName" name="configName" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">	
            <label class="col-sm-3 control-label">Config Key：</label>
            <div class="col-sm-8">
                <input id="configKey" name="configKey" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">	
            <label class="col-sm-3 control-label">Config Value：</label>
            <div class="col-sm-8">
                <input id="configValue" name="configValue" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
			<label class="col-sm-3 control-label">System Config：</label>
			<div class="col-sm-8">
			    <div class="radio-box" th:each="dict : ${@dict.getType('sys_yes_no')}">
					<input type="radio" th:id="${dict.dictCode}" name="configType" th:value="${dict.dictValue}" th:checked="${dict.isDefault == 'Y' ? true : false}">
					<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
				</div>
			</div>
		</div>
		<div class="form-group">	
            <label class="col-sm-3 control-label">Remark：</label>
            <div class="col-sm-8">
                <textarea id="remark" name="remark" class="form-control"></textarea>
            </div>
        </div>
    </form>
    </div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
	    var prefix = ctx + "system/config";
	
	    $("#form-config-add").validate({
	        rules: {
	            configKey: {
	                required: true,
	                remote: {
	                    url: prefix + "/checkConfigKeyUnique",
	                    type: "post",
	                    dataType: "json",
	                    data: {
	                        "configKey": function() {
	                            return $.common.trim($("#configKey").val());
	                        }
	                    },
	                    dataFilter: function(data, type) {
	                        return $.validate.unique(data);
	                    }
	                }
	            },
	            configName: {
	                required: true
	            },
	            configValue: {
	                required: true
	            },
	        },
	        messages: {
	            "configKey": {
	                remote: "Config Key already exists"
	            }
	        }
	    });
	    
	    function submitHandler() {
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/add", $('#form-config-add').serialize());
	        }
	    }
    </script>
</body>
</html>