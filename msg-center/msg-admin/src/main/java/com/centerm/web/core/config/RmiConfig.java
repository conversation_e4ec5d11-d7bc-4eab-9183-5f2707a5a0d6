package com.centerm.web.core.config;

import com.centerm.framework.service.IRmiService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.remoting.rmi.RmiProxyFactoryBean;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/6/19 15:32
 **/
@Configuration
public class RmiConfig {
	@Value("${gate.rmi.host}")
	private String registryHost;
	@Value("${gate.rmi.port}")
	private int registryPort;

	@Bean
	public RmiProxyFactoryBean rmiServiceExporter(){
		RmiProxyFactoryBean rmiProxy = new RmiProxyFactoryBean();
		rmiProxy.setServiceUrl("rmi://"+ registryHost + ":" + registryPort+"/rmiService");
		rmiProxy.setServiceInterface(IRmiService.class);
		//失败重连
		rmiProxy.setRefreshStubOnConnectFailure(true);
		//启动不连
		rmiProxy.setLookupStubOnStartup(false);
		return rmiProxy;
	}
}
