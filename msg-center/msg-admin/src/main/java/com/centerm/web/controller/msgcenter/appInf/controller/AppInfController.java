package com.centerm.web.controller.msgcenter.appInf.controller;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.basic.manager.ParamsUtils;
import com.centerm.basic.msgcenter.appInf.domain.AppInf;
import com.centerm.basic.msgcenter.appInf.dto.AppInfDto;
import com.centerm.basic.msgcenter.appInf.service.IAppInfService;
import com.centerm.basic.msgcenter.appVersion.domain.AppVersion;
import com.centerm.basic.msgcenter.appVersion.service.IAppVersionService;
import com.centerm.basic.msgcenter.sysfile.domain.MsgFile;
import com.centerm.basic.msgcenter.sysfile.service.IMsgFileService;
import com.centerm.common.annotation.Log;
import com.centerm.common.config.ParamConfig;
import com.centerm.common.dto.file.FileUploadResponse;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.common.utils.xmlUtils.Config;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * 软件 信息操作处理
 *
 * <AUTHOR> Chong auto
 * @date 2019-04-08
 */
@Controller
@RequestMapping("/msgcenter/appInf")
public class AppInfController extends BaseController {
    private String prefix = "msgcenter/appInf";

    @Autowired
    private IAppInfService appInfService;
    @Autowired
    private IAppVersionService versionService;
    @Autowired
    private IMsgFileService sysFileService;

    @RequiresPermissions("msgcenter:appInf:view")
    @GetMapping()
    public String appInf() {
        return prefix + "/appInf";
    }

    /**
     * 查询软件列表
     */
    @RequiresPermissions("msgcenter:appInf:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AppInfDto appInf) {
        startPage();
        if(appInf.getDeptId() == null){
            appInf.setDeptId(getSysUser().getDeptId());
        }
        List<AppInfDto> list = appInfService.list(appInf);
        return getDataTable(list);
    }

    /**
     * 新增软件
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存软件
     */
    @RequiresPermissions("msgcenter:appInf:add")
    @Log(title = "Software Management", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(AppInfDto appInfDto) {
        appInfDto.setDeptId(getSysUser().getDeptId());
        return appInfService.addApp(appInfDto);
    }

    @PostMapping("/uploadFile")
    @ResponseBody
    public FileUploadResponse uploadFile(MultipartFile file) throws Exception {
        return appInfService.upload(file);
    }

    /**
     * 修改软件
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Integer id, ModelMap mmap) {
        AppInf appInf =appInfService.getById(id);
        mmap.put("appInf", appInf);
        if("true".equals(ParamConfig.getIsfastDFS())){
            mmap.put("gateFileUrl", ParamConfig.getFastDfsUrl());
        }else{
            mmap.put("gateFileUrl", ParamConfig.getPlatformFileUrl());
        }
        return prefix + "/edit";
    }

    /**
     * 修改保存软件
     */
    @RequiresPermissions("msgcenter:appInf:edit")
    @Log(title = "Software Management", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(AppInf appInf) {
        return appInfService.updateById(appInf);
    }

    /**
     * 删除软件
     */
    @RequiresPermissions("msgcenter:appInf:remove")
    @Log(title = "Software Management", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<Integer> lst = Arrays.asList(Convert.toIntArray(ids));
        return appInfService.removeByIds(lst);
    }

}