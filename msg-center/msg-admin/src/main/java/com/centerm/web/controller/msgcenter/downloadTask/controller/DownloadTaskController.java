package com.centerm.web.controller.msgcenter.downloadTask.controller;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.basic.msgcenter.appVersion.domain.AppVersion;
import com.centerm.basic.msgcenter.appVersion.service.IAppVersionService;
import com.centerm.basic.msgcenter.downloadJob.domain.DownloadJob;
import com.centerm.basic.msgcenter.downloadJob.dto.TaskCountDto;
import com.centerm.basic.msgcenter.downloadJob.service.IDownloadJobService;
import com.centerm.basic.msgcenter.downloadTask.domain.DownloadTask;
import com.centerm.basic.msgcenter.downloadTask.service.IDownloadTaskService;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.DateUtils;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.common.utils.spring.SpringUtils;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务下发 信息操作处理
 *
 * <AUTHOR> Chong auto
 * @date 2019-04-08
 */
@Controller
@RequestMapping("/msgcenter/downloadTask")
public class DownloadTaskController extends BaseController {
    private String prefix = "msgcenter/downloadTask";

    @Autowired
    private IDownloadTaskService downloadTaskService;
    @Autowired
    private IDownloadJobService downloadJobService;
    @Autowired
    private IAppVersionService appVersionService;

    @RequiresPermissions("msgcenter:downloadTask:view")
    @GetMapping("/{id}")
    public String downloadTask(@PathVariable("id") Integer id, ModelMap mmap) {
        DownloadJob downloadJob = downloadJobService.getById(id);
        TaskCountDto taskCountDto = downloadJobService.taskCount(id);
        AppVersion appVersion = appVersionService.getById(downloadJob.getAppVersionId());

        //为空时，表示固件已经删除
        mmap.put("appVersion", CommonUtils.isEmpty(appVersion) ? new AppVersion() : appVersion);
        mmap.put("jobId", id);
        mmap.put("taskCount",taskCountDto);
        return prefix + "/downloadTask";
    }

    /**
     * 查询任务下发列表
     */
    @RequiresPermissions("msgcenter:downloadTask:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(DownloadTask downloadTask) {
        startPage();
        QueryWrapper<DownloadTask> queryWrapper = new QueryWrapper<>(downloadTask);
        List<DownloadTask> list = downloadTaskService.list(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 导出任务下发列表
     */
    @RequiresPermissions("msgcenter:downloadTask:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(DownloadTask downloadTask) {

        if (CommonUtils.isEmpty(downloadTask.getTermSeq())) {
            downloadTask.setTermSeq(null);
        }

        if (CommonUtils.isEmpty(downloadTask.getDlFlag())) {
            downloadTask.setDlFlag(null);
        }
        QueryWrapper<DownloadTask> queryWrapper = new QueryWrapper<DownloadTask>(downloadTask);
        List<DownloadTask> list = downloadTaskService.list(queryWrapper);
        if (list.size() > 0) {
            DownloadJob downloadJob = downloadJobService.getById(list.get(0).getJobId());
            list.forEach(e -> e.setJobName(downloadJob.getJobName()));
        }
        ExcelUtil<DownloadTask> util = new ExcelUtil<DownloadTask>(DownloadTask. class);
        return util.exportExcel(list, "downloadTask");
    }

    /**
     * 新增任务下发
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存任务下发
     */
    @RequiresPermissions("msgcenter:downloadTask:add")
    @Log(title = "任务下发", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(DownloadTask downloadTask) {
        downloadTask.setUpdateTime(DateUtils.getNowDate()); //add 2022-12-05
        return downloadTaskService.save(downloadTask);
    }

    /**
     * 修改任务下发
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Integer id, ModelMap mmap) {
        DownloadTask downloadTask =downloadTaskService.getById(id);
        mmap.put("downloadTask", downloadTask);
        return prefix + "/edit";
    }

    /**
     * 修改保存任务下发
     */
    @RequiresPermissions("msgcenter:downloadTask:edit")
    @Log(title = "任务下发", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(DownloadTask downloadTask) {
        downloadTask.setUpdateTime(DateUtils.getNowDate()); //add 2022-12-05
        return downloadTaskService.updateById(downloadTask);
    }

    /**
     * 删除任务下发
     */
    @RequiresPermissions("msgcenter:downloadTask:remove")
    @Log(title = "任务下发", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<String> lst = Arrays.asList(Convert.toStrArray(ids));
        return downloadTaskService.removeByIds(lst);
    }

}
