package com.centerm.web.controller.msgcenter.logMoney.controller;
import com.centerm.basic.msgcenter.logMoneyReport.domain.LogMoneyReport;
import com.centerm.basic.msgcenter.logMoneyReport.service.ILogMoneyReportService;
import com.centerm.common.utils.CommonUtils;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 消息记录 信息操作处理
 *
 * <AUTHOR> auto
 * @date 2019-06-06
 */
@Controller
@RequestMapping("/msgcenter/logMoneyReport")
public class LogMoneyReportController extends BaseController {
    private String prefix = "msgcenter/logMoney";

    @Autowired
    private ILogMoneyReportService logMoneyReportService;

    @RequiresPermissions("msgcenter:logMoneyReport:view")
    @GetMapping()
    public String logMoneyReport() {
        return prefix + "/moneyReport";
    }

    /**
     * 查询不含支付渠道的交易统计
     */
    @RequiresPermissions("msgcenter:logMoneyReport:list")
    @PostMapping("/list")
    @ResponseBody
    public Map list(LogMoneyReport logMoneyReport) {
        Map<String, Object> result = new HashMap<>(2);
       if(CommonUtils.isEmpty(logMoneyReport) ){
           return result;
       }
       if(CommonUtils.isEmpty(logMoneyReport.getDeptId())){
           logMoneyReport.setDeptId(Integer.parseInt(getSysUser().getDeptId()+""));
       }
        //处理dateType 字段，昨日 yesterday，上周 lastWeek，上月 lastMonth。
        List<LogMoneyReport> listNoChannel =  logMoneyReportService.selectListNoChannel(logMoneyReport);
       List<LogMoneyReport> listOnlyChannel =  logMoneyReportService.selectListOnlyChannel(logMoneyReport);

       result.put("listNoChannel", listNoChannel);
       result.put("listOnlyChannel", listOnlyChannel);
       return result;
    }


    /**
     * 查询不含支付渠道的交易统计
     */
//    @RequiresPermissions("msgcenter:logMoneyReport:list")
    @PostMapping("/indexList")
    @ResponseBody
    public Map indexList(LogMoneyReport logMoneyReport) {
        Map<String, Object> result = new HashMap<>(2);
        if(CommonUtils.isEmpty(logMoneyReport) || CommonUtils.isEmpty(logMoneyReport.getDeptId())){
            return result;
        }
        //处理dateType 字段，昨日 yesterday，上周 lastWeek，上月 lastMonth。
        List<LogMoneyReport> listNoChannel =  logMoneyReportService.selectListNoChannel(logMoneyReport);
        List<LogMoneyReport> listOnlyChannel =  logMoneyReportService.selectListOnlyChannel(logMoneyReport);

        result.put("listNoChannel", listNoChannel);
        result.put("listOnlyChannel", listOnlyChannel);
        return result;
    }

}
