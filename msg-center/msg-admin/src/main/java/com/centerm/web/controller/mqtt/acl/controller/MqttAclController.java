package com.centerm.web.controller.mqtt.acl.controller;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.basic.msgcenter.mqtt.acl.domain.MqttAcl;
import com.centerm.basic.msgcenter.mqtt.acl.service.IMqttAclService;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.StringUtils;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * emq认证acl 信息操作处理
 *
 * <AUTHOR> auto
 * @date 2019-03-05
 */
@Controller
@RequestMapping("/msgcenter/mqttAcl")
public class MqttAclController extends BaseController {
    private String prefix = "msgcenter/mqttAcl";

    @Autowired
    private IMqttAclService mqttAclService;

    @RequiresPermissions("msgcenter:mqttAcl:view")
    @GetMapping()
    public String mqttAcl() {
        return prefix + "/mqttAcl";
    }

    /**
     * 查询emq认证acl列表
     */
    @RequiresPermissions("msgcenter:mqttAcl:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(MqttAcl mqttAcl) {
        startPage();
        QueryWrapper<MqttAcl> queryWrapper = new QueryWrapper<>(mqttAcl);
        if (StringUtils.isBlank(mqttAcl.getUsername())) {
            mqttAcl.setUsername(null);
        }
        queryWrapper.orderByDesc("create_time");
        List<MqttAcl> list = mqttAclService.list(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 导出emq认证acl列表
     */
    @RequiresPermissions("msgcenter:mqttAcl:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(MqttAcl mqttAcl) {
        QueryWrapper<MqttAcl> queryWrapper = new QueryWrapper<MqttAcl>(mqttAcl);
        List<MqttAcl> list = mqttAclService.list(queryWrapper);
        ExcelUtil<MqttAcl> util = new ExcelUtil<MqttAcl>(MqttAcl. class);
        return util.exportExcel(list, "mqttAcl");
    }

    /**
     * 新增emq认证acl
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存emq认证acl
     */
    @RequiresPermissions("msgcenter:mqttAcl:add")
    @Log(title = "EMQ Authorization", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(MqttAcl mqttAcl) {
        return mqttAclService.save(mqttAcl);
    }

    /**
     * 修改emq认证acl
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Integer id, ModelMap mmap) {
        MqttAcl mqttAcl =mqttAclService.getById(id);
        mmap.put("mqttAcl", mqttAcl);
        return prefix + "/edit";
    }

    /**
     * 修改保存emq认证acl
     */
    @RequiresPermissions("msgcenter:mqttAcl:edit")
    @Log(title = "EMQ Authorization", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(MqttAcl mqttAcl) {
        return mqttAclService.updateById(mqttAcl);
    }

    /**
     * 删除emq认证acl
     */
    @RequiresPermissions("msgcenter:mqttAcl:remove")
    @Log(title = "EMQ Authorization", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<Integer> lst = Arrays.asList(Convert.toIntArray(ids));
        return mqttAclService.removeByIds(lst);
    }

}