package com.centerm.web.controller.msgcenter.logOnline.controller;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.basic.msgcenter.logOnline.domain.LogOnline;
import com.centerm.basic.msgcenter.logOnline.dto.LogOnlineRequest;
import com.centerm.basic.msgcenter.logOnline.service.IAdminLogOnlineService;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 终端上下线记录 信息操作处理
 *
 * <AUTHOR> auto
 * @date 2020-04-07
 */
@Controller
@RequestMapping("/msgcenter/logOnline")
public class LogOnlineController extends BaseController {
    private String prefix = "msgcenter/logOnline";

    @Autowired
    private IAdminLogOnlineService logOnlineService;

    @RequiresPermissions("msgcenter:logOnline:view")
    @GetMapping()
    public String logOnline(String sn, ModelMap mmap) {
        mmap.put("sn", sn);
        return prefix + "/logOnline";
    }


    @RequiresPermissions("msgcenter:logOnline:view")
    @GetMapping("/device")
    public String logOnlineDeviceList() {
        return prefix + "/device";
    }

    /**
     * 查询终端上下线记录列表
     */
    @RequiresPermissions("msgcenter:logOnline:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(LogOnlineRequest logOnline) {
        startPage();
        List<LogOnline> list = logOnlineService.selectList(logOnline);
        return getDataTable(list);
    }


    /**
     * 导出终端上下线记录列表
     */
    @RequiresPermissions("msgcenter:logOnline:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(LogOnline logOnline) {
        QueryWrapper<LogOnline> queryWrapper = new QueryWrapper<LogOnline>(logOnline);
        List<LogOnline> list = logOnlineService.list(queryWrapper);
        ExcelUtil<LogOnline> util = new ExcelUtil<LogOnline>(LogOnline. class);
        return util.exportExcel(list, "logOnline");
    }

    /**
     * 新增终端上下线记录
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存终端上下线记录
     */
    @RequiresPermissions("msgcenter:logOnline:add")
    @Log(title = "终端上下线记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(LogOnline logOnline) {
        return logOnlineService.save(logOnline);
    }

    /**
     * 修改终端上下线记录
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        LogOnline logOnline =logOnlineService.getById(id);
        mmap.put("logOnline", logOnline);
        return prefix + "/edit";
    }

    /**
     * 修改保存终端上下线记录
     */
    @RequiresPermissions("msgcenter:logOnline:edit")
    @Log(title = "终端上下线记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(LogOnline logOnline) {
        return logOnlineService.updateById(logOnline);
    }

    /**
     * 删除终端上下线记录
     */
    @RequiresPermissions("msgcenter:logOnline:remove")
    @Log(title = "终端上下线记录", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<Integer> lst = Arrays.asList(Convert.toIntArray(ids));
        return logOnlineService.removeByIds(lst);
    }

}
