package com.centerm.web.controller.system;

import com.centerm.basic.msgcenter.deptConfig.domain.TDeptConfig;
import com.centerm.basic.msgcenter.deptConfig.service.ITDeptConfigService;
import com.centerm.basic.msgcenter.printTemplate.domain.PrintTemplate;
import com.centerm.basic.msgcenter.printTemplate.service.ITPrintTemplateService;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.exception.BusinessException;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.StringUtils;
import com.centerm.framework.manager.RedisCacheManager;
import com.centerm.framework.util.ShiroUtils;
import com.centerm.framework.web.base.BaseController;
import com.centerm.system.domain.SysDept;
import com.centerm.system.domain.SysRole;
import com.centerm.system.service.ISysDeptService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 部门信息
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/system/dept")
public class DeptController extends BaseController {
    private String prefix = "system/dept";

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ITPrintTemplateService printTemplateService;

    @Autowired
    private ITDeptConfigService deptConfigService;

    @RequiresPermissions("system:dept:view")
    @GetMapping()
    public String dept() {
        return prefix + "/dept";
    }

    @RequiresPermissions("system:dept:list")
    @GetMapping("/list")
    @ResponseBody
    public List<SysDept> list(SysDept dept) {
        List<SysDept> deptList = deptService.selectDeptList(dept);
        return deptList;
    }

    /**
     * 新增部门
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        mmap.put("dept", deptService.selectDeptById(getSysUser().getDeptId()));
        return prefix + "/add";
    }

    /**
     * 新增保存部门
     */
    @Log(title = "Institution Management", businessType = BusinessType.INSERT)
    @RequiresPermissions("system:dept:add")
    @PostMapping("/add")
    @ResponseBody
    public Integer addSave(SysDept dept) {
        dept.setCreateBy(ShiroUtils.getLoginName());
        return deptService.insertDept(dept);
    }

    /**
     * 修改
     */
    @GetMapping("/edit/{deptId}")
    public String edit(@PathVariable("deptId") Long deptId, ModelMap mmap) {
        SysDept dept = deptService.selectDeptById(deptId);
        if (StringUtils.isNotNull(dept) && 100L == deptId) {
            dept.setParentName("None");
        }
        mmap.put("dept", dept);
        return prefix + "/edit";
    }

    /**
     * 保存
     */
    @Log(title = "Institution Management", businessType = BusinessType.UPDATE)
    @RequiresPermissions("system:dept:edit")
    @PostMapping("/edit")
    @ResponseBody
    public Integer editSave(SysDept dept) {
        dept.setUpdateBy(ShiroUtils.getLoginName());
        RedisCacheManager.getInstance().removeDeptKey(dept.getCid());
        return deptService.updateDept(dept);
    }

    /**
     * 删除
     */
    @Log(title = "Institution Management", businessType = BusinessType.DELETE)
    @RequiresPermissions("system:dept:remove")
    @PostMapping("/remove/{deptId}")
    @ResponseBody
    public Integer remove(@PathVariable("deptId") Long deptId) {
        if (deptService.selectDeptCount(deptId) > 0) {
          throw new BusinessException("dept.has.children");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            throw new BusinessException("dept.has.user");
        }
        return deptService.deleteDeptById(deptId);
    }

    /**
     * 校验部门名称
     */
    @PostMapping("/checkDeptNameUnique")
    @ResponseBody
    public String checkDeptNameUnique(SysDept dept) {
        return deptService.checkDeptNameUnique(dept);
    }
    /**
     * 校验部门名称
     */
    @PostMapping("/checkCidUnique")
    @ResponseBody
    public String checkCidUnique(SysDept dept) {
        return deptService.checkCidUnique(dept);
    }

    /**
     * 选择部门树
     */
    @GetMapping("/selectDeptTree/{deptId}")
    public String selectDeptTree(@PathVariable("deptId") Long deptId, ModelMap mmap) {
        mmap.put("dept", deptService.selectDeptById(deptId));
        return prefix + "/tree";
    }

    /**
     * 加载部门列表树
     */
    @GetMapping("/treeData")
    @ResponseBody
    public List<Map<String, Object>> treeData() {
        List<Map<String, Object>> tree = deptService.selectDeptTree(new SysDept());
        return tree;
    }

    /**
     * 加载角色部门（数据权限）列表树
     */
    @GetMapping("/roleDeptTreeData")
    @ResponseBody
    public List<Map<String, Object>> deptTreeData(SysRole role) {
        List<Map<String, Object>> tree = deptService.roleDeptTreeData(role);
        return tree;
    }


    @RequiresPermissions("system:dept:edit")
    @Log(title = "Institution Management", businessType = BusinessType.UPDATE)
    @GetMapping("/editParam/{deptId}")
    public String editParam(@PathVariable("deptId") Long deptId, ModelMap mmap) {
        TDeptConfig sysDeptConfig =deptConfigService.selectTDeptConfigById(deptId);
        if(CommonUtils.isEmpty(sysDeptConfig)){
            sysDeptConfig = new TDeptConfig();
            sysDeptConfig.setDeptId(deptId);
        }
        List<PrintTemplate> printTemplateList = printTemplateService.selectUsePrintTemplateList();
        mmap.put("deptConfig", sysDeptConfig);
        mmap.put("printTemplateList", printTemplateList);
        return prefix + "/editParam";
    }
}