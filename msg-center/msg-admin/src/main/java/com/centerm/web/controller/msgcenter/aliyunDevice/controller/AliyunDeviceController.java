package com.centerm.web.controller.msgcenter.aliyunDevice.controller;

import com.alibaba.fastjson.JSONObject;
import com.centerm.basic.msgcenter.broadcastconfig.service.IBroadcastconfigService;
import com.centerm.basic.msgcenter.device.domain.Device;
import com.centerm.basic.msgcenter.device.service.AdminIDeviceService;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @created 2022/3/29 下午5:01
 */
@Controller
@RequestMapping("/msgcenter/aliyunDevice")
public class AliyunDeviceController extends BaseController {
    private String prefix = "msgcenter/aliyunDevice";

    @Autowired
    private AdminIDeviceService deviceService;
    @Autowired
    private IBroadcastconfigService broadcastconfigService;


    @RequiresPermissions("msgcenter:aliyun:device:view")
    @GetMapping()
    public String device() {
        return prefix + "/aliyunDevice";
    }

    /**
     * 查询设备列表
     */
    @RequiresPermissions("msgcenter:aliyun:device:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Device device) {
        device.setIotType(AdminIDeviceService.IS_ALIYUN_IOT_TRUE);
        startPage();
        List<Device> list = deviceService.selectList(device);
        return getDataTable(list);
    }

    @Log(title = "Device Management", businessType = BusinessType.IMPORT)
    @RequiresPermissions("msgcenter:aliyun:device:import")
    @PostMapping("/importData")
    @ResponseBody
    public String importData(MultipartFile file) throws Exception {
        ExcelUtil<Device> util = new ExcelUtil<Device>(Device.class);
        List<Device> userList = util.importExcel(file.getInputStream());
        String operName = getSysUser().getLoginName();
        Long userDeptId = getSysUser().getDeptId();
        String message = deviceService.importDevice(userList, true, userDeptId, operName);
        return message;
    }

    @GetMapping("/importTemplate")
    @ResponseBody
    public String importTemplate() {
        ExcelUtil<Device> util = new ExcelUtil<>(Device.class);
        return util.importTemplateExcel("Device Template");
    }

    /**
     * 新增批量设备
     */
    @GetMapping("/batchAdd")
    public String batchAdd() {
        return prefix + "/batchAdd";
    }

    /**
     * 新增批量保存设备
     */
    @RequiresPermissions("msgcenter:aliyunDevice:add")
    @Log(title = "Device", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    @ResponseBody
    public String batchAddSave(Device device, String snHead, String snStart, int snLength, String snEnd) {
        List<Device> deviceList = new ArrayList<>();
        String rule = "%0" + (snLength - snHead.length()) + "d";
        int start = Integer.parseInt(snStart);
        int end = Integer.parseInt(snEnd);
        for (int num = start; num <= end; num++) {
            Device newDevice = new Device();

            BeanUtils.copyProperties(device, newDevice);

            newDevice.setSn(snHead + String.format(rule, num));
            newDevice.setIotType(AdminIDeviceService.IS_ALIYUN_IOT_TRUE);

            deviceList.add(newDevice);
        }

        String operName = getSysUser().getLoginName();
        Long userDeptId = getSysUser().getDeptId();
        String message = deviceService.importDevice(deviceList, false, userDeptId, operName);
        return message;
    }

    /**
     * 新增设备
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    @RequiresPermissions("msgcenter:aliyunDevice:add")
    @Log(title = "Device", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public JSONObject addSave(Device device) {
        String operName = getSysUser().getLoginName();
        Long userDeptId = getSysUser().getDeptId();
        return deviceService.importDeviceForAlibabaCloud(device, userDeptId, operName);
    }
}