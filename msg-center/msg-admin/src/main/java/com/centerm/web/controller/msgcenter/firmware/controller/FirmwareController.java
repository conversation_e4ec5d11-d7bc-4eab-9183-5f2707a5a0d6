package com.centerm.web.controller.msgcenter.firmware.controller;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.basic.msgcenter.firmware.domain.Firmware;
import com.centerm.basic.msgcenter.firmware.dto.FirmwareDTO;
import com.centerm.basic.msgcenter.firmware.service.IAdminFirmwareService;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.DescConstants;
import com.centerm.common.utils.StringUtils;
import com.centerm.common.utils.http.HttpClient;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 固件升级
 *
 * <AUTHOR> Chong
 * @date 2019-03-04
 */
@Controller
@RequestMapping("/msgcenter/firmware")
public class FirmwareController extends BaseController {
    private String prefix = "msgcenter/firmware";

    @Autowired
    private IAdminFirmwareService firmwareService;
    @Value("${gate.url}")
    private String gatePrefix;

    @RequiresPermissions("msgcenter:firmware:view")
    @GetMapping()
    public String firmware() {
        return prefix + "/firmware";
    }

    /**
     * 查询消息推送日志列表
     */
    @RequiresPermissions("msgcenter:firmware:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Firmware firmware) {
        //FIXME 临时从日志表中读记录
        firmware.setFunc(DescConstants.FIRMWARE_UPGRADE);
        startPage();
        QueryWrapper<Firmware> queryWrapper = new QueryWrapper<>(firmware);
        if (StringUtils.isBlank(firmware.getTarget())) {
            firmware.setTarget(null);
        }
        if (StringUtils.isBlank(firmware.getTitle())) {
            firmware.setTitle(null);
        }
        queryWrapper.orderByDesc("create_time");
        List<Firmware> list = firmwareService.list(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 导出消息推送日志列表
     */
    @RequiresPermissions("msgcenter:firmware:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(Firmware firmware) {
        QueryWrapper<Firmware> queryWrapper = new QueryWrapper<Firmware>(firmware);
        List<Firmware> list = firmwareService.list(queryWrapper);
        ExcelUtil<Firmware> util = new ExcelUtil<Firmware>(Firmware. class);
        return util.exportExcel(list, "firmware");
    }

    /**
     * 新增消息推送日志
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存消息推送日志
     */
    @RequiresPermissions("msgcenter:firmware:add")
    @Log(title = "Firmware Update", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(FirmwareDTO firmwareDTO) {
        HttpClient.sendPost(gatePrefix+"/v1/firmware/upgrade",firmwareDTO);
        return true;
    }

    /**
     * 修改消息推送日志
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        Firmware firmware =firmwareService.getById(id);
        mmap.put("firmware", firmware);
        return prefix + "/edit";
    }

    /**
     * 修改保存消息推送日志
     */
    @RequiresPermissions("msgcenter:firmware:edit")
    @Log(title = "Firmware Update", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(Firmware firmware) {
        return firmwareService.updateById(firmware);
    }

    /**
     * 删除消息推送日志
     */
    @RequiresPermissions("msgcenter:firmware:remove")
    @Log(title = "Firmware Update", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<String> lst = Arrays.asList(Convert.toStrArray(ids));
        return firmwareService.removeByIds(lst);
    }

}