package com.centerm.web.controller.monitor;

import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.enums.OnlineStatus;
import com.centerm.common.exception.BusinessException;
import com.centerm.common.page.TableDataInfo;
import com.centerm.framework.shiro.session.OnlineSession;
import com.centerm.framework.shiro.session.OnlineSessionDAO;
import com.centerm.framework.util.ShiroUtils;
import com.centerm.framework.web.base.BaseController;
import com.centerm.system.domain.SysUserOnline;
import com.centerm.system.service.impl.SysUserOnlineServiceImpl;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线用户监控
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/monitor/online")
public class UserOnlineController extends BaseController {
    private String prefix = "monitor/online";

    @Autowired
    private SysUserOnlineServiceImpl userOnlineService;

    @Autowired
    private OnlineSessionDAO onlineSessionDAO;

    @RequiresPermissions("monitor:online:view")
    @GetMapping()
    public String online() {
        return prefix + "/online";
    }

    @RequiresPermissions("monitor:online:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysUserOnline userOnline) {
        startPage();
        List<SysUserOnline> list = userOnlineService.selectUserOnlineList(userOnline);
        return getDataTable(list);
    }

    @RequiresPermissions("monitor:online:batchForceLogout")
    @Log(title = "Online User", businessType = BusinessType.FORCE)
    @PostMapping("/batchForceLogout")
    @ResponseBody
    public void batchForceLogout(@RequestParam("ids[]") String[] ids) {
        for (String sessionId : ids) {
            forceLogout(sessionId);
        }
    }

    @RequiresPermissions("monitor:online:forceLogout")
    @Log(title = "Online User", businessType = BusinessType.FORCE)
    @PostMapping("/forceLogout")
    @ResponseBody
    public void forceLogout(String sessionId) {
        SysUserOnline online = userOnlineService.selectOnlineById(sessionId);
        if (online == null) {
            throw new BusinessException("user.has.offline");
        }
        OnlineSession onlineSession = (OnlineSession) onlineSessionDAO.readSession(online.getSessionId());
        if (onlineSession == null) {
            throw new BusinessException("user.has.offline");
        }
        if (sessionId.equals(ShiroUtils.getSessionId())) {
            throw new BusinessException("user.self.not.force");
        }
        onlineSession.setStatus(OnlineStatus.off_line);
        online.setStatus(OnlineStatus.off_line);
        userOnlineService.saveOnline(online);
    }
}