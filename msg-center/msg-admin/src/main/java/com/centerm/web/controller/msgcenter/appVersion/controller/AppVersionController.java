package com.centerm.web.controller.msgcenter.appVersion.controller;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.basic.msgcenter.appVersion.domain.AppVersion;
import com.centerm.basic.msgcenter.appVersion.service.IAppVersionService;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 驱动版本 信息操作处理
 *
 * <AUTHOR> auto
 * @date 2019-04-08
 */
@Controller
@RequestMapping("/msgcenter/appVersion")
public class AppVersionController extends BaseController {
    private String prefix = "msgcenter/appVersion";

    @Autowired
    private IAppVersionService appVersionService;

    @RequiresPermissions("msgcenter:appVersion:view")
    @GetMapping()
    public String appVersion() {

        return prefix + "/appVersion";
    }

    /**
     * 查询驱动版本列表
     */
    @RequiresPermissions("msgcenter:appVersion:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AppVersion appVersion) {
        startPage();
        QueryWrapper<AppVersion> queryWrapper = new QueryWrapper<>(appVersion);
        List<AppVersion> list = appVersionService.list(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 导出驱动版本列表
     */
    @RequiresPermissions("msgcenter:appVersion:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(AppVersion appVersion) {
        QueryWrapper<AppVersion> queryWrapper = new QueryWrapper<AppVersion>(appVersion);
        List<AppVersion> list = appVersionService.list(queryWrapper);
        ExcelUtil<AppVersion> util = new ExcelUtil<AppVersion>(AppVersion. class);
        return util.exportExcel(list, "appVersion");
    }

    /**
     * 新增驱动版本
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存驱动版本
     */
    @RequiresPermissions("msgcenter:appVersion:add")
    @Log(title = "App Version", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(AppVersion appVersion) {
        return appVersionService.save(appVersion);
    }

    /**
     * 修改驱动版本
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Integer id, ModelMap mmap) {
        AppVersion appVersion =appVersionService.getById(id);
        mmap.put("appVersion", appVersion);
        return prefix + "/edit";
    }

    /**
     * 修改保存驱动版本
     */
    @RequiresPermissions("msgcenter:appVersion:edit")
    @Log(title = "App Version", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(AppVersion appVersion) {
        return appVersionService.updateById(appVersion);
    }

    /**
     * 删除驱动版本
     */
    @RequiresPermissions("msgcenter:appVersion:remove")
    @Log(title = "App Version", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<Integer> lst = Arrays.asList(Convert.toIntArray(ids));
        return appVersionService.removeByIds(lst);
    }

}