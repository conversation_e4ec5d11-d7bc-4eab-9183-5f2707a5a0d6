package com.centerm.web.controller.msgcenter.sim.controller;

import com.baomidou.mybatisplus.annotation.TableField;
import com.centerm.basic.msgcenter.sim.domain.SimAlert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @created 2021/9/8 上午10:12
 */

@Data
@ApiModel
@ToString
public class SimAlertsRequest {

    @ApiModelProperty("请求标识，用于唯一标识当前请求")
    private String reqId;

    @ApiModelProperty("订单时间")
    private String ts;

    @ApiModelProperty("告警记录数量")
    private int count;

    @ApiModelProperty("告警列表")
    private List<SimAlert> simAlertList;

    @ApiModelProperty("签名")
    private String sign;

    @ApiModelProperty(value = "测试")
    private String cid;

}
