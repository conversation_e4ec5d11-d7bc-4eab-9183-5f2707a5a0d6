package com.centerm.web.controller.msgcenter.adverlog.controller;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.basic.msgcenter.adverlog.domain.Adverlog;
import com.centerm.basic.msgcenter.adverlog.domain.AdverlogHistory;
import com.centerm.basic.msgcenter.adverlog.service.IAdverlogHistoryService;
import com.centerm.basic.msgcenter.adverlog.service.IAdverlogService;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.DateUtils;
import com.centerm.common.utils.StringUtils;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 广告记录 信息操作处理
 *
 * <AUTHOR> Chong auto
 * @date 2019-03-17
 */
@Controller
@RequestMapping("/msgcenter/adverlog")
public class AdverlogController extends BaseController {
    private String prefix = "msgcenter/adverlog";

    @Autowired
    private IAdverlogService adverlogService;
    @Autowired
    private IAdverlogHistoryService adverlogHistoryService;

    @RequiresPermissions("msgcenter:adverlog:view")
    @GetMapping()
    public String adverlog() {
        return prefix + "/adverlog";
    }
    @RequiresPermissions("msgcenter:adverlogHistory:view")
    @GetMapping("/adverlogHistory")
    public String adverlogHistory() {
        return prefix + "/adverlogHistory";
    }
    /**
     * 查询当日广告记录列表
     */
    @RequiresPermissions("msgcenter:adverlog:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Adverlog adverlog,String startDate, String endDate) {
        startPage();
        QueryWrapper<Adverlog> queryWrapper = new QueryWrapper<>(adverlog);

        queryWrapper.orderByDesc("create_time");
        List<Adverlog> list = adverlogService.list(queryWrapper);
        return getDataTable(list);
    }
    /**
     * 查询历史广告记录列表
     */
    @RequiresPermissions("msgcenter:adverlog:listHistory")
    @PostMapping("/listHistory")
    @ResponseBody
    public TableDataInfo listHistory(AdverlogHistory adverlogHistory, String startTime, String endTime) {
        startPage();
        QueryWrapper<AdverlogHistory> queryWrapper = new QueryWrapper<>(adverlogHistory);

        if (StringUtils.isBlank(adverlogHistory.getSn())) {
            return getDataTable(new ArrayList<>());
        }
        if (StringUtils.isBlank(adverlogHistory.getStatus())) {
            adverlogHistory.setStatus(null);
        }
        //获取时间段内的内容
        if(!StringUtils.isBlank(startTime)) {
            queryWrapper.ge("create_time",startTime);
        }
        if(!StringUtils.isBlank(endTime)) {
            queryWrapper.le("create_time",endTime);
        }
        //当日记录
        queryWrapper.ge("create_time", DateUtils.getDate());
        queryWrapper.orderByDesc("create_time");
        List<AdverlogHistory> list = adverlogHistoryService.list(queryWrapper);
        return getDataTable(list);
    }

    /**
     * 导出广告记录列表
     */
    @RequiresPermissions("msgcenter:adverlog:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(Adverlog adverlog) {
        QueryWrapper<Adverlog> queryWrapper = new QueryWrapper<Adverlog>(adverlog);
        List<Adverlog> list = adverlogService.list(queryWrapper);
        ExcelUtil<Adverlog> util = new ExcelUtil<Adverlog>(Adverlog. class);
        return util.exportExcel(list, "adverlog");
    }

    /**
     * 新增广告记录
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存广告记录
     */
    @RequiresPermissions("msgcenter:adverlog:add")
    @Log(title = "AD Log", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(Adverlog adverlog) {
        return adverlogService.save(adverlog);
    }

    /**
     * 修改广告记录
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        Adverlog adverlog =adverlogService.getById(id);
        mmap.put("adverlog", adverlog);
        return prefix + "/edit";
    }

    /**
     * 修改保存广告记录
     */
    @RequiresPermissions("msgcenter:adverlog:edit")
    @Log(title = "AD Log", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(Adverlog adverlog) {
        return adverlogService.updateById(adverlog);
    }

    /**
     * 删除广告记录
     */
    @RequiresPermissions("msgcenter:adverlog:remove")
    @Log(title = "AD Log", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<Integer> lst = Arrays.asList(Convert.toIntArray(ids));
        return adverlogService.removeByIds(lst);
    }

}