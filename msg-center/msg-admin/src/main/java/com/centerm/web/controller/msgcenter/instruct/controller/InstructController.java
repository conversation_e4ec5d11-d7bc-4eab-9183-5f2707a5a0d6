package com.centerm.web.controller.msgcenter.instruct.controller;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.basic.msgcenter.instruct.domain.Instruct;
import com.centerm.basic.msgcenter.instruct.service.IInstructService;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.DateUtils;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 运维指令 信息操作处理
 *
 * <AUTHOR> auto
 * @date 2019-03-17
 */
@Controller
@RequestMapping("/msgcenter/instruct")
public class InstructController extends BaseController {
    private String prefix = "msgcenter/instruct";

    @Autowired
    private IInstructService instructService;

    @RequiresPermissions("msgcenter:instruct:view")
    @GetMapping()
    public String instruct() {
        return prefix + "/instruct";
    }

    /**
     * 查询运维指令列表
     */
    @RequiresPermissions("msgcenter:instruct:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Instruct instruct) {
        startPage();
        QueryWrapper<Instruct> queryWrapper = new QueryWrapper<>(instruct);
        List<Instruct> list = instructService.list(queryWrapper);
        queryWrapper.orderByDesc("create_time");
        return getDataTable(list);
    }


    /**
     * 导出运维指令列表
     */
    @RequiresPermissions("msgcenter:instruct:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(Instruct instruct) {
        QueryWrapper<Instruct> queryWrapper = new QueryWrapper<Instruct>(instruct);
        List<Instruct> list = instructService.list(queryWrapper);
        ExcelUtil<Instruct> util = new ExcelUtil<Instruct>(Instruct. class);
        return util.exportExcel(list, "instruct");
    }

    /**
     * 新增运维指令
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存运维指令
     */
    @RequiresPermissions("msgcenter:instruct:add")
    @Log(title = "运维指令", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(Instruct instruct) {
        instruct.setCreateTime(DateUtils.getNowDate());
        return instructService.save(instruct);
    }

    /**
     * 修改运维指令
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        Instruct instruct =instructService.getById(id);
        mmap.put("instruct", instruct);
        return prefix + "/edit";
    }

    /**
     * 修改保存运维指令
     */
    @RequiresPermissions("msgcenter:instruct:edit")
    @Log(title = "运维指令", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(Instruct instruct) {
        return instructService.updateById(instruct);
    }

    /**
     * 删除运维指令
     */
    @RequiresPermissions("msgcenter:instruct:remove")
    @Log(title = "运维指令", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<String> lst = Arrays.asList(Convert.toStrArray(ids));
        return instructService.removeByIds(lst);
    }

    /**
     * 发送运维指令
     */
    @RequiresPermissions("msgcenter:instruct:push")
    @Log(title = "运维指令", businessType = BusinessType.OTHER)
    @GetMapping("/push/{id}")
    @ResponseBody
    public Boolean push(@PathVariable("id") String id) {
        return instructService.instructPush(id);
    }

}
