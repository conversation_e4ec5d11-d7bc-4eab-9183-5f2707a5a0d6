package com.centerm.web.controller.msgcenter.params.controller;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.basic.msgcenter.downloadJob.domain.DownloadJob;
import com.centerm.basic.msgcenter.paramJob.domain.ParamJob;
import com.centerm.basic.msgcenter.paramJob.service.IParamJobService;
import com.centerm.basic.msgcenter.paramTask.domain.ParamTask;
import com.centerm.basic.msgcenter.paramTask.service.IParamTaskService;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 参数下发 信息操作处理
 *
 * <AUTHOR> Chong auto
 * @date 2019-06-13
 */
@Controller
@RequestMapping("/msgcenter/paramTask")
public class ParamTaskController extends BaseController {
    private String prefix = "msgcenter/paramTask";
    @Autowired
    private IParamJobService paramJobService;

    @Autowired
    private IParamTaskService paramTaskService;

    @RequiresPermissions("msgcenter:paramTask:view")
    @GetMapping("/{id}")
    public String paramTask(@PathVariable("id") Integer id, ModelMap mmap) {
        ParamJob paramJob =paramJobService.getById(id);
        mmap.put("paramJob", paramJob);
        mmap.put("jobId", id);
        return prefix + "/paramTask";
    }

    /**
     * 查询参数下发列表
     */
    @RequiresPermissions("msgcenter:paramTask:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ParamTask paramTask) {
        startPage();
        QueryWrapper<ParamTask> queryWrapper = new QueryWrapper<>(paramTask);
        List<ParamTask> list = paramTaskService.list(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 导出参数下发列表
     */
    @RequiresPermissions("msgcenter:paramTask:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(ParamTask paramTask) {
        QueryWrapper<ParamTask> queryWrapper = new QueryWrapper<ParamTask>(paramTask);
        List<ParamTask> list = paramTaskService.list(queryWrapper);
        if (list.size() > 0){
            ParamJob paramJob = paramJobService.getById(list.get(0).getJobId());
            list.forEach(e -> e.setJobName(paramJob.getJobName()));
        }
        ExcelUtil<ParamTask> util = new ExcelUtil<ParamTask>(ParamTask. class);
        return util.exportExcel(list, "paramTask");
    }

    /**
     * 新增参数下发
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存参数下发
     */
    @RequiresPermissions("msgcenter:paramTask:add")
    @Log(title = "Parameter Task", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(ParamTask paramTask) {
        return paramTaskService.save(paramTask);
    }

    /**
     * 修改参数下发
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Integer id, ModelMap mmap) {
        ParamTask paramTask =paramTaskService.getById(id);
        mmap.put("paramTask", paramTask);
        return prefix + "/edit";
    }

    /**
     * 修改保存参数下发
     */
    @RequiresPermissions("msgcenter:paramTask:edit")
    @Log(title = "Parameter Task", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(ParamTask paramTask) {
        return paramTaskService.updateById(paramTask);
    }

    /**
     * 删除参数下发
     */
    @RequiresPermissions("msgcenter:paramTask:remove")
    @Log(title = "Parameter Task", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<String> lst = Arrays.asList(Convert.toStrArray(ids));
        return paramTaskService.removeByIds(lst);
    }

}