package com.centerm.web.controller.msgcenter.downloadJob.controller;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.centerm.basic.msgcenter.device.domain.Device;
import com.centerm.basic.msgcenter.device.service.AdminIDeviceService;
import com.centerm.basic.msgcenter.downloadJob.domain.DownloadJob;
import com.centerm.basic.msgcenter.downloadJob.service.IDownloadJobService;
import com.centerm.basic.msgcenter.downloadTask.domain.DownloadTask;
import com.centerm.basic.msgcenter.downloadTask.service.IDownloadTaskService;
import com.centerm.common.annotation.Log;
import com.centerm.common.constant.JobConstants;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.exception.BusinessException;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.DateUtils;
import com.centerm.common.utils.TimeZoneUtils;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.time.ZoneId;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

/**
 * 任务 信息操作处理
 *
 * <AUTHOR> Chong auto
 * @date 2019-04-08
 */
@Controller
@RequestMapping("/msgcenter/downloadJob")
public class DownloadJobController extends BaseController {
    private String prefix = "msgcenter/downloadJob";

    @Autowired
    private IDownloadJobService downloadJobService;
    @Autowired
    private IDownloadTaskService downloadTaskService;
    @Autowired
    private AdminIDeviceService deviceService;

    @RequiresPermissions("msgcenter:downloadJob:view")
    @GetMapping()
    public String downloadJob() {
        return prefix + "/downloadJob";
    }

    /**
     * 查询任务列表
     */
    @RequiresPermissions("msgcenter:downloadJob:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(DownloadJob downloadJob) {
        startPage();
        if(downloadJob.getDeptId() == null){
            downloadJob.setDeptId(getSysUser().getDeptId());
        }
        List<DownloadJob> list = downloadJobService.list(downloadJob);
        return getDataTable(list);
    }

    /**
     * 新增任务
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存任务
     */
    @RequiresPermissions("msgcenter:downloadJob:add")
    @Log(title = "Download Job", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(DownloadJob downloadJob) {
        downloadJob.setReleaseStatus("0");
        //非按机构发布的任务，存储用户所属机构，用于数据筛选
        if(!JobConstants.RELEASE_TYPE_GROUP.equals(downloadJob.getReleaseType())){
            downloadJob.setDeptId(getSysUser().getDeptId());
            checkSn(downloadJob.getCollection().split(","));
        }

        downloadJob.setCreateBy(getUsername());
        downloadJob.setCreateTime(DateUtils.getNowDate());
        downloadJob.setReleaseTime(TimeZoneUtils.covertZone(downloadJob.getReleaseTime(), downloadJob.getTimeZone(), ZoneId.systemDefault().toString()));
        downloadJob.setValidDate(TimeZoneUtils.covertZone(downloadJob.getValidDate(), downloadJob.getTimeZone(), ZoneId.systemDefault().toString()));
        return downloadJobService.save(downloadJob);
    }

    /**
     * 修改任务
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Integer id, ModelMap mmap) {
        DownloadJob downloadJob =downloadJobService.getById(id);
        mmap.put("downloadJob", downloadJob);
        return prefix + "/edit";
    }

    /**
     * 修改有效期
     */
    @GetMapping("/editValidDate/{id}")
    public String editValidDate(@PathVariable("id") Integer id, ModelMap mmap) {
        DownloadJob downloadJob =downloadJobService.getById(id);
        mmap.put("downloadJob", downloadJob);
        return prefix + "/editValidDate";
    }

    /**
     * 修改保存任务
     */
    @RequiresPermissions("msgcenter:downloadJob:edit")
    @Log(title = "Download Job", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(DownloadJob downloadJob) {
        downloadJob.setReleaseStatus("0");
        //非按机构发布的任务，存储用户所属机构，用于数据筛选
        if(!JobConstants.RELEASE_TYPE_GROUP.equals(downloadJob.getReleaseType())){
            downloadJob.setDeptId(getSysUser().getDeptId());
            checkSn(downloadJob.getCollection().split(","));
        }
        downloadJob.setUpdateBy(getUsername());
        downloadJob.setUpdateTime(DateUtils.getNowDate());
        downloadJob.setReleaseTime(TimeZoneUtils.covertZone(downloadJob.getReleaseTime(), downloadJob.getTimeZone(), ZoneId.systemDefault().toString()));
        downloadJob.setValidDate(TimeZoneUtils.covertZone(downloadJob.getValidDate(), downloadJob.getTimeZone(), ZoneId.systemDefault().toString()));
        return downloadJobService.updateById(downloadJob);
    }


    /**
     * 修改保存任务
     */
    @RequiresPermissions("msgcenter:downloadJob:edit")
    @Log(title = "Download Job", businessType = BusinessType.UPDATE)
    @PostMapping("/editValidDate")
    @ResponseBody
    public Boolean editValidDateSave(DownloadJob downloadJob) {
        downloadJob.setUpdateBy(getUsername());
        downloadJob.setUpdateTime(DateUtils.getNowDate());
        return downloadJobService.updateValidDate(downloadJob);
    }


    /**
     * 删除任务
     */
    @RequiresPermissions("msgcenter:downloadJob:remove")
    @Log(title = "Download Job", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<Integer> lst = Arrays.asList(Convert.toIntArray(ids));
        //删除task表
        downloadTaskService.remove(new LambdaQueryWrapper<DownloadTask>()
                .in(DownloadTask::getJobId, lst));
        return downloadJobService.removeByIds(lst);
    }

    /**
     * 发布任务
     */
    @RequiresPermissions("msgcenter:downloadJob:publish")
    @Log(title = "Download Job", businessType = BusinessType.UPDATE)
    @GetMapping("/publish/{id}")
    @ResponseBody
    public Boolean publish(@PathVariable("id") String id) {
        return downloadJobService.publish(id);
    }

    void checkSn(String[] sns){
        Device device = new Device();
        Stream.of(sns).forEach(sn ->{
            device.setSn(sn);
            int count = deviceService.checkSnPermission(device);
            if(count != 1) {
                throw new BusinessException("device.add.task.error", sn);
            }
        });



    }


    /**
     * 暂停任务
     */
    @RequiresPermissions("msgcenter:downloadJob:pause")
    @Log(title = "Download Job", businessType = BusinessType.UPDATE)
    @GetMapping("/pause/{id}")
    @ResponseBody
    public Boolean pause(@PathVariable("id") String id) {
        return downloadJobService.pause(id);
    }


    /**
     * 启动任务
     */
    @RequiresPermissions("msgcenter:downloadJob:start")
    @Log(title = "Download Job", businessType = BusinessType.UPDATE)
    @GetMapping("/start/{id}")
    @ResponseBody
    public Boolean start(@PathVariable("id") String id) {
        return downloadJobService.start(id);
    }


}