package com.centerm.web.controller.msgcenter.changeIns.controller;


import com.centerm.basic.msgcenter.changeIns.domain.ChangeInsJob;
import com.centerm.basic.msgcenter.changeIns.domain.TusnImport;
import com.centerm.basic.msgcenter.changeIns.service.IChangeInsJobService;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.framework.web.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@Api(tags = "终端机构变更管理")
@RequestMapping("/msgcenter/changInsJob")
@Controller
public class ChangeInsJobController extends BaseController {

    private String prefix = "msgcenter/changins";


    @Autowired
    private IChangeInsJobService iChangeInsJobService;


    @RequiresPermissions("task:changInsJob:view")
    @GetMapping()
    public String info() {
        return prefix + "/info";
    }

    @ApiOperation(value = "查询终端机构变更任务列表")
    @RequiresPermissions("task:changInsJob:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ChangeInsJob changeInsJob) {
        startPage();
        List<ChangeInsJob> list = iChangeInsJobService.selectJobList(changeInsJob);
        return getDataTable(list);
    }


    @ApiOperation(value = "变更机构")
    @GetMapping("/add")
    public String editOrgPage(ModelMap mmap) {
        return prefix + "/add";
    }


    @ApiOperation(value = "变更机构")
    @RequiresPermissions("task:changInsJob:add")
    @Log(title = "Change Institution", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    @Transactional(rollbackFor = Exception.class)
    public Boolean addSave(ChangeInsJob changeInsJob) throws Exception {
        return iChangeInsJobService.add(changeInsJob, getSysUser().getDeptId());
    }


    @RequiresPermissions("task:changInsJob:add")
    @GetMapping("/importTemplate")
    @ResponseBody
    public String importTemplate() {
        ExcelUtil<TusnImport> util = new ExcelUtil<TusnImport>(TusnImport.class);
        return util.importTemplateExcel("ImportDevice");
    }

    @Log(title = "Task Basic Information ImportData", businessType = BusinessType.IMPORT)
    @RequiresPermissions("task:changInsJob:add")
    @PostMapping("/importData")
    @ResponseBody
    public List<TusnImport> importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<TusnImport> util = new ExcelUtil<>(TusnImport.class);
        List<TusnImport> excelImportResult = util.importExcel(file.getInputStream());
        //TODO 验证
        return excelImportResult;
    }

}