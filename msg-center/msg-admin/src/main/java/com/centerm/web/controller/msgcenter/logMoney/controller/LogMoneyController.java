package com.centerm.web.controller.msgcenter.logMoney.controller;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.basic.msgcenter.logMoney.domain.LogMoney;
import com.centerm.basic.msgcenter.logMoney.dto.LogMoneyRequest;
import com.centerm.basic.msgcenter.logMoney.dto.StatisticsRequest;
import com.centerm.basic.msgcenter.logMoney.service.ILogMoneyService;
import com.centerm.basic.msgcenter.logReceive.dto.LogReceiveRequest;
import com.centerm.common.annotation.Log;
import com.centerm.common.constant.MsgCenterConstants;
import com.centerm.common.dto.transaction.LogMoneySummary;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.DateUtils;
import com.centerm.common.utils.StringUtils;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.common.utils.spring.SpringUtils;
import com.centerm.framework.web.base.BaseController;
import com.centerm.system.domain.SysDept;
import com.centerm.system.mapper.SysDeptMapper;
import com.centerm.system.service.ISysConfigService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jsoup.helper.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.xml.crypto.Data;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 消息记录 信息操作处理
 *
 * <AUTHOR> Chong auto
 * @date 2019-03-25
 */
@Controller
@RequestMapping("/msgcenter/logMoney")
public class LogMoneyController extends BaseController {
    private String prefix = "msgcenter/logMoney";

    @Autowired
    private ILogMoneyService logMoneyService;

    private static ISysConfigService sysConfigService = SpringUtils.getBean(ISysConfigService.class);

    @RequiresPermissions("msgcenter:logMoney:view")
    @GetMapping()
    public String logMoney() {
        return prefix + "/logMoney";
    }

    /**
     * 查询消息记录列表
     */
    @RequiresPermissions("msgcenter:logMoney:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(LogMoneyRequest request) {
        startPage();
        if (CommonUtils.isEmpty(request.getDeptId())) {
            request.setDeptId(getSysUser().getDeptId().intValue());
        }
        List<LogMoney> list = logMoneyService.selectList(request);
        return getDataTable(list);
    }

    /**
     * 导出消息记录列表
     */
    @RequiresPermissions("msgcenter:logMoney:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(LogMoneyRequest request) {
        request.setCurDate(DateUtils.getDate());
        if(CommonUtils.isEmpty(request.getDeptId())){
            request.setDeptId(getSysUser().getDeptId().intValue());
        }else{
            request.setDeptId(request.getDeptId());
        }
        List<LogMoney> list = logMoneyService.selectList(request);
        ExcelUtil<LogMoney> util = new ExcelUtil<LogMoney>(LogMoney. class);
        return util.exportExcel(list, "logMoney");
    }


    @RequiresPermissions("msgcenter:logMoney:statistics")
    @GetMapping("/statistics")
    public String statisticsView () {
        return prefix + "/statistics";
    }

    /**
     * 交易统计
     */
    @RequiresPermissions("msgcenter:logMoney:statistics")
    @Log(title = "交易统计")
    @PostMapping("/statistics")
    @ResponseBody
    public List<LogMoneySummary> statistics(LogMoneyRequest request) {
        return logMoneyService.summary(request);
    }

    @RequiresPermissions("msgcenter:logMoney:detail")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") String id, ModelMap mmap) {
        mmap.put("logMoney", logMoneyService.getById(id));
        return prefix + "/detail";
    }





    /**
     * 查询消息记录列表
     */
    @PostMapping("/getDeviceLogMoneyList")
    @ResponseBody
    @CrossOrigin
    public TableDataInfo getDeviceLogMoneyList(LogMoneyRequest request) {
        startPage();
        List<LogMoney> list = logMoneyService.selectListBySn(request.getSn());
        return getDataTable(list);
    }

    /**
     * 查询当日播报交易日志总数
     * @param request
     * @return
     */
    @PostMapping("/getLogMoneyTotal")
    @ResponseBody
    public String getLoMoneyTotal(LogMoneyRequest request) {
        if(request.getDeptId() == null){
            request.setDeptId(getSysUser().getDeptId().intValue());
        }
        return logMoneyService.selectCountTotal(request) + "";
    }
}