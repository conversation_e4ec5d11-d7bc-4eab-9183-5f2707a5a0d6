package com.centerm.msg.sms.common.constants;

/**
 * 错误码常量
 *
 * <AUTHOR>
 * @date 2019/02/15 11:12
 **/
public class ErrorConstants {
    /**
     * 成功码
     */
    public static final String SUCCESS = "response.success";
    /**
     * 错误码
     */
    public static final String FAIL = "response.fail";

    /**
     * 返回登陆
     */
    public static final String RESPONSE_LOGIN = "response.login";

    public static final String PARAM_NOT_NULL ="param.not.null";

    /**
     * 参数不合法
     */
    public static final String PARAM_NOT_EXCEPT = "param.not.except";

    /**
     * 返回登陆
     */
    public static final String UNAUTHORIZED = "unauthorized";


    /**
     * 数据库操作
     */
    public static final String DATABASE_EXCEPTION = "database.exception";

    /**
     * 已经绑定，不能删除
     */
    public static final String BIND_NOT_DELETE = "bind.not.delete";
    /**
     * 数据丢失
     */
    public static final String DATA_NOT_EXISTS = "data.not.exists";
    /**
     * 受影响的行数为0
     */
    public static final String RESPONSE_AFFECTED_ROWS_ZERO = "response.affected.rows.zero";
    /**
     * 非本公司员工
     */
    public static final String login_not_company_employee = "login.not.company.employee";

    /**
     * eos创建账号错误
     */
    public static final String EOS_CREATE_ACCOUNT_ERROR = "eos.create.account.error";
    /**
     * eos转账错误
     */
    public static final String EOS_TRANSFER_ERROR ="eos.transfer.error";
    /**
     * eos解锁异常
     */
    public static final String EOS_UNLOCK_ERROR ="eos.unlock.error";
    /**
     * EOS 私钥为空
     */
    public static final String EOS_PRIVATE_KEY_NULL ="eos.private.key.null";

    /**
     * 员工密码错误或不存在
     */
    public static final String LOGIN_EMPLOYEE_WRONG ="login.employee.wrong";


}
