/*
 * Copyright (c) 2018-2022 Caratacus, (<EMAIL>).
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
package com.centerm.msg.sms.common;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.centerm.common.exception.BusinessException;
import com.centerm.common.utils.StringUtils;
import com.centerm.msg.sms.common.constants.ErrorConstants;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/***
 *  返回结果判断是否异常
 *
 * <AUTHOR>
 * @date 2019/3/6 15:07
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ApiAssert {


    public static void equals(String errorCode, Object obj1, Object obj2) {
        if (!Objects.equals(obj1, obj2)) {
            failure(errorCode);
        }
    }

    public static void isTrue(String errorCode, boolean condition) {
        if (!condition) {
            failure(errorCode);
        }
    }
    public static void isFalse(String errorCode, boolean condition) {
        if (condition) {
            failure(errorCode);
        }
    }

    public static void isNull(String errorCode, Object... conditions) {
        if (ObjectUtils.isNotNull(conditions)) {
            failure(errorCode);
        }
    }

    public static void notNull(String errorCode, Object... conditions) {
        if (ObjectUtils.isNull(conditions)) {
            failure(errorCode);
        }
    }

    public static void paramNotNull(Object object,String filed){
        if (ObjectUtils.isNull(object)) {
            failure(ErrorConstants.PARAM_NOT_NULL,filed);
        }
    }
    public static void dataNotExists(Object object,String filed){
        if (ObjectUtils.isNull(object)) {
            failure(ErrorConstants.DATA_NOT_EXISTS,filed);
        }
    }
    public static void notEmpty(String errorCode, Object[] array) {
        if (ObjectUtils.isEmpty(array)) {
            failure(errorCode);
        }
    }


    public static void noNullElements(String errorCode, Object[] array) {
        if (array != null) {
            for (Object element : array) {
                if (element == null) {
                    failure(errorCode);
                }
            }
        }
    }

    public static void notEmpty(String errorCode, Collection<?> collection) {
        if (CollectionUtils.isEmpty(collection)) {
            failure(errorCode);
        }
    }

    public static void notBlank(String errorCode, String param,Object... args) {
        if (StringUtils.isEmpty(param)) {
            failure(errorCode,args);
        }
    }

    public static void notEmpty(String errorCode, Map<?, ?> map) {
        if (MapUtils.isEmpty(map)) {
            failure(errorCode);
        }
    }


    public static void isEmpty(String errorCode, Collection<?> collection) {
        if (CollectionUtils.isNotEmpty(collection)) {
            failure(errorCode);
        }
    }


    public static void isEmpty(String errorCode, Map<?, ?> map) {
        if (MapUtils.isNotEmpty(map)) {
            failure(errorCode);
        }
    }

    public static void failure(String errorCode, Object... args) {
        throw new BusinessException(errorCode, args);
    }
    public static void failure( ) {
        throw new BusinessException(ErrorConstants.FAIL);
    }

}
