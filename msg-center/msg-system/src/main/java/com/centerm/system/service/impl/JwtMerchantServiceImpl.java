package com.centerm.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.system.domain.MerchantUser;
import com.centerm.system.mapper.JwtMerchantMapper;
import com.centerm.system.service.IJwtMerchantService;
import org.springframework.stereotype.Service;

/**
 * 商户 服务层实现
 * 
 * <AUTHOR> auto
 * @date 2019-04-01
 */
@Service
public class JwtMerchantServiceImpl extends ServiceImpl<JwtMerchantMapper, MerchantUser> implements IJwtMerchantService
{

    @Override
    public MerchantUser selectUserByLoginName(String username) {
        return this.baseMapper.selectUserByLoginName(username);
    }

    @Override
    public boolean updatePwdByUsername(MerchantUser merchantUser) {
        return this.baseMapper.updatePwdByUsername(merchantUser);
    }
}
