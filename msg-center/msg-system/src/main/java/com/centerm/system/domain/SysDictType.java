package com.centerm.system.domain;

import com.centerm.common.annotation.Excel;
import com.centerm.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典类型表 sys_dict_type
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysDictType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 字典主键
     */
    @Excel(name = "Dict ID")
    private Long dictId;

    /**
     * 字典名称
     */
    @Excel(name = "Dict Name")
    private String dictName;

    /**
     * 字典类型
     */
    @Excel(name = "Dict Type")
    private String dictType;

    /**
     * 状态（0正常 1停用）
     */
    @Excel(name = "Status", readConverterExp = "0=Normal,1=Disabled")
    private String status;

}