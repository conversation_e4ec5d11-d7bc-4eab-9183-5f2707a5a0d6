package com.centerm.system.domain;

import com.centerm.common.annotation.Excel;
import com.centerm.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 字典数据表 sys_dict_data
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class SysDictData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 字典编码
     */
    @Excel(name = "Dict Code")
    private Long dictCode;

    /**
     * 字典排序
     */
    @Excel(name = "Ordering")
    private Long dictSort;

    /**
     * 字典标签
     */
    @Excel(name = "Dict Label")
    private String dictLabel;

    /**
     * 字典键值
     */
    @Excel(name = "Dict Value")
    private String dictValue;

    /**
     * 字典类型
     */
    @Excel(name = "Dict Type")
    private String dictType;

    /**
     * 样式属性（其他样式扩展）
     */
    @Excel(name = "CSS Extent")
    private String cssClass;

    /**
     * 表格字典样式
     */
    private String listClass;

    /**
     * 是否默认（Y是 N否）
     */
    @Excel(name = "Is Default", readConverterExp = "Y=Yes,N=No")
    private String isDefault;

    /**
     * 状态（0正常 1停用）
     */
    @Excel(name = "Status", readConverterExp = "0=Normal,1=Disable")
    private String status;

}