package com.centerm.system.domain;

import com.centerm.common.annotation.Excel;
import com.centerm.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色表 sys_role
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysRole extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @Excel(name = "Role ID")
    private Long roleId;

    /**
     * 角色名称
     */
    @Excel(name = "Role Name")
    private String roleName;

    /**
     * 角色权限
     */
    @Excel(name = "Permission")
    private String roleKey;

    /**
     * 角色排序
     */
    @Excel(name = "Ordering")
    private String roleSort;

    /**
     * 数据范围（1：所有数据权限；2：自定数据权限）
     */
    @Excel(name = "Data Scope", readConverterExp = "1=Full Permission,2=Customization Permission")
    private String dataScope;

    /**
     * 角色状态（0正常 1停用）
     */
    @Excel(name = "Status", readConverterExp = "0=Normal,1=Disable")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 用户是否存在此角色标识 默认不存在
     */
    private boolean flag = false;

    /**
     * 菜单组
     */
    private Long[] menuIds;

    /**
     * 部门组（数据权限）
     */
    private Long[] deptIds;

}