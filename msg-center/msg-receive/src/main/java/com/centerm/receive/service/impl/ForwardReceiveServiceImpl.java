package com.centerm.receive.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.centerm.common.constant.EmqConstants;
import com.centerm.common.constant.MsgCenterConstants;
import com.centerm.common.enums.CommonStatusEnums;
import com.centerm.common.utils.ThreadManagerUtil;
import com.centerm.mqtt.dto.MessageRequest;
import com.centerm.mqtt.enums.IotEnum;
import com.centerm.mqtt.factory.MqttWorkFactory;
import com.centerm.mqtt.msg.commonStatus.ReceivedMqtt;
import com.centerm.mqtt.msg.commonStatus.mapper.CommonStatusMapper;
import com.centerm.mqtt.msg.device.domain.Device;
import com.centerm.mqtt.msg.device.mapper.DeviceMapper;
import com.centerm.mqtt.msg.logOnline.domain.LogOnline;
import com.centerm.mqtt.msg.logOnline.mapper.LogOnlineMapper;
import com.centerm.mqtt.utils.Sign;
import com.centerm.receive.service.IForwardReceiveService;
import com.centerm.system.domain.SysDept;
import com.centerm.system.service.ISysDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class ForwardReceiveServiceImpl implements IForwardReceiveService {
    @Autowired
    private CommonStatusMapper commonStatusMapper;
    @Autowired
    private DeviceMapper deviceMapper;
    @Autowired
    private LogOnlineMapper logOnlineMapper;

    @Autowired
    private ISysDeptService sysDeptService;

    @Override
    public void tradeReceiveHandle(String message) {
        JSONObject msgJson = JSONUtil.parseObj(message);
        msgJson.getStr("payload");
        MessageRequest messageRequest = JSONUtil.toBean(msgJson.getStr("payload"), MessageRequest.class);
        ReceivedMqtt receivedMqtt = new ReceivedMqtt();
        receivedMqtt.setMsgId(messageRequest.getMsgId());
        receivedMqtt.setStatus(CommonStatusEnums.BROADCAST_SUCCESS.getStatus());
        receivedMqtt.setCurrentTime(new Date());
        commonStatusMapper.updateLogMoneyStatus(receivedMqtt);
    }

    /**
     * {"payload":{"deviceName":"S059999900001","event":"EV_ONLINE","productID":"1CTCBWIEML",
     * "reason":"REASON_DEVICE_CONNECT","timestamp":1750998988,"topic":"$state/report/1CTCBWIEML/S059999900001"},
     * "timemills":1750998988576, "seq":1431957, "timestamp":1750998988,
     * "topic":"$state/report/1CTCBWIEML/S059999900001", "devicename":"S059999900001", "productid":"1CTCBWIEML"}
     * @param message
     */
    @Override
    public void statusReceiveHandle(String message) {
        JSONObject deviceJson = JSONUtil.parseObj(message);
        JSONObject payloadJson = deviceJson.getJSONObject("payload");
        String sn = payloadJson.getStr("deviceName");
        if (sn == null) {
            log.info("腾讯云===>设备上下线：sn为空");
            return ;
        }
        Device device = deviceMapper.selectBySn(sn);
        if(device == null ) {
            log.info("设备上下线：未找到设备，sn:{}", sn);
            return ;
        }

        LogOnline online = new LogOnline();
        online.setDeptId(device.getDeptId());
        online.setTime(new Date(deviceJson.getLong("timemills")));
        online.setSn(device.getSn());

        //判断上下线
        if ("EV_OFFLINE".equals(payloadJson.getStr("event"))) {
            //1. 设备离线处理
            online.setStatus(MsgCenterConstants.DEVICE_NETWORK_OUTLINE);
            device.setLastLogoutTime(new Date(deviceJson.getLong("timemills")));
            device.setNetworkStatus(MsgCenterConstants.DEVICE_NETWORK_DISCONNECT);
            device.setLastLoginTime(null); //不更新

            log.info("腾讯云===>设备已离线：sn = " + device.getSn());
            deviceMapper.updateBySn(device);
            logOnlineMapper.insert(online);
            log.info("腾讯云===>更新设备状态：下线，sn = " + device.getSn() + "：" + device.getNetworkStatus());
        }
        else if ("EV_ONLINE".equals(payloadJson.getStr("event"))) {
            //2-1. 设备在线处理
            online.setStatus(MsgCenterConstants.DEVICE_NETWORK_ONLINE);
            device.setNetworkStatus(MsgCenterConstants.DEVICE_NETWORK_CONNECT);
            if (device.getActiveTime() == null) {
                device.setActiveTime(new Date(deviceJson.getLong("timemills")));
            }

            device.setLastLoginTime(new Date(deviceJson.getLong("timemills")));
            device.setLastLogoutTime(null); //不更新

            device.setIotType(IotEnum.TENCENT_IOT.getType());
            device.setProductKey(payloadJson.getStr("productID"));

            deviceMapper.updateBySn(device);
            logOnlineMapper.insert(online);
            log.info("腾讯云===>更新设备状态：上线：sn = " + device.getSn() + "：" + device.getNetworkStatus());

            //检测设备更新任务
            MqttWorkFactory.firmwareJobCheck(device.getSn());
            MqttWorkFactory.paramJobCheck(device.getSn());
//                MqttWorkFactory.templateJobCheck(device);

        }
        else {
            log.error("腾讯云===>设备上下线：不是上下线消息， status={}", deviceJson.getStr("status"));
        }

        notify(sn);
    }

    private void notify(String sn){
        ThreadManagerUtil.execute(new Runnable() {
            @Override
            public void run() {
                Device device = deviceMapper.selectBySn(sn);
                SysDept sysDept = sysDeptService.selectDeptById(Long.parseLong(device.getDeptId()+""));
                if (StrUtil.isBlank(sysDept.getNotifyUrl()))return;
                // 构造 JSON 数据
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("timestamp", System.currentTimeMillis());
                paramMap.put("sn", device.getSn());
                paramMap.put("status", device.getStatus());
                paramMap.put("loginTime", device.getLastLoginTime());
                paramMap.put("logoutTime", device.getLastLogoutTime());
                SortedMap<Object, Object> parameters = new TreeMap<Object, Object>(paramMap);
                String signStr = Sign.getSign(parameters, sysDept.getCid());
                paramMap.put("sign", signStr);
                String jsonBody = JSONUtil.toJsonStr(paramMap);
                log.info("回调设备状态请求：{}", jsonBody);
                // 发送 POST 请求
                HttpResponse response = HttpRequest.post(sysDept.getNotifyUrl())
                        .header("Content-Type", ContentType.JSON.toString()) // 设置请求头
                        .body(jsonBody) // 设置请求体
                        .execute();
                log.info("回调设备状态应答：{}", response.body());
            }
        });
    }
}