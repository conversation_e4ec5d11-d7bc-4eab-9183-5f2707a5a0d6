<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>msg-center</artifactId>
        <groupId>com.centerm</groupId>
        <version>2.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>msg-receive</artifactId>
    <packaging>war</packaging>
    <description>消息回执服务</description>

    <dependencies>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.centerm</groupId>
            <artifactId>msg-framework</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!--单元测试-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.centerm</groupId>
            <artifactId>msg-emq-service</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.icbc</groupId>-->
<!--            <artifactId>icbc-api</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.ccb</groupId>-->
<!--            <artifactId>ccb-ecipher-decode-prim</artifactId>-->
<!--            <version>1.0</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->

        <!--dependency>
            <groupId>com.lsy.baselib</groupId>
            <artifactId>crypto</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/CNCBCryptoPkg.jar</systemPath>
        </dependency-->

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.18</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>${artifactId}</finalName>
        <resources>
            <resource>
                <directory>${basedir}/lib</directory>
                <targetPath>BOOT-INF/lib/</targetPath>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.jar</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <!--关键地方  包含本地jar-->
                    <includeSystemScope>true</includeSystemScope>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven.war.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration><encoding>UTF-8</encoding>
                    <!-- 过滤后缀为pem、pfx的证书文件 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>pri</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pub</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
