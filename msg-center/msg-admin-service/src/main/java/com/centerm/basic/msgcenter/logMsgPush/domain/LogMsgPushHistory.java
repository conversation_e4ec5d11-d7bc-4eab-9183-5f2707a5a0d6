package com.centerm.basic.msgcenter.logMsgPush.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 历史消息推送日志表 t_log_msg_push_history
 *
 * <AUTHOR>
 * @date 2019-03-25
 */
@ApiModel(value = "消息推送日志")
@Data
@ToString
@TableName("t_log_msg_push_history")
@Accessors(chain = true)
public class LogMsgPushHistory {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "编号")
    @TableId(value = "id", type = IdType.UUID)
    private String id;

    @ApiModelProperty(value = "功能名称", required = false)
    private String func;

    @ApiModelProperty(value = "消息接收目标", required = false)
    private String target;

    @ApiModelProperty(value = "消息类型", required = false)
    private String type;

    @ApiModelProperty(value = "消息来源", required = false)
    private String source;

    @ApiModelProperty(value = "消息内容", required = false)
    private String content;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "成功数", required = false)
    private String successCount;

    @ApiModelProperty(value = "创建者", required = false)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者", required = false)
    private String updateBy;

    @ApiModelProperty(value = "更新时间", required = false)
    private Date updateTime;

    @ApiModelProperty(value = "备注信息", required = false)
    private String remark;

    @ApiModelProperty(value = "储备字段1")
    private String reserve1;

    @ApiModelProperty(value = "储备字段2", required = false)
    private String reserve2;

    @ApiModelProperty(value = "开始日期", required = false)
    private Date startTime;

    @ApiModelProperty(value = "结束日期", required = false)
    private Date endTime;

    @ApiModelProperty(value = "付款时间", required = false)
    private String payTime;

}
