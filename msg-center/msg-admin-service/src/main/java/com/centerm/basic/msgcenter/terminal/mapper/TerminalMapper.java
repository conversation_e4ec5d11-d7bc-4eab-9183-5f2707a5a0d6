package com.centerm.basic.msgcenter.terminal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.centerm.basic.msgcenter.terminal.domain.Terminal;

import java.util.List;

/**
 * 终端 数据层
 *
 * <AUTHOR>
 * @date 2023-03-20
 */
public interface TerminalMapper extends BaseMapper<Terminal> {

    /**
     * 查询设备列表（带机构分组信息,页面列表页使用）
     * @param terminal
     * @return
     */
    List<Terminal> selectList(Terminal terminal);

    Terminal selectTerminalBySn(String sn);

    Boolean bindTerminal(Terminal terminal);

    Boolean unBindTerminal(String sn);

    List<Terminal> selectExportList(Terminal terminal);

    List<Terminal> selectListByIdsAndBind(List<String> list);

    List<Terminal> selectCountByParentDept(Terminal terminal);
    List<Terminal> selectCountByDept(Terminal terminal);
}