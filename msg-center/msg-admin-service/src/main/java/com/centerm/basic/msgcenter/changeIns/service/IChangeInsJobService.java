package com.centerm.basic.msgcenter.changeIns.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.basic.msgcenter.changeIns.domain.ChangeInsJob;

import java.util.List;

public interface IChangeInsJobService extends IService<ChangeInsJob> {


    List<ChangeInsJob> selectJobList(ChangeInsJob changeInsJob);


    Boolean add(ChangeInsJob changeInsJob, Long deptId);
}
