package com.centerm.basic.manager;


import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.*;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/4/10 16:54
 **/
@Component
public class JobManager {

    private static ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("jobManager-pool-%d").build();
    static final int nThreads = Runtime.getRuntime().availableProcessors() * 2;

    public static ExecutorService pool = new ThreadPoolExecutor(nThreads, nThreads,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(1024), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

    public static void execute(Runnable object){
        pool.execute(object);
    }

}
