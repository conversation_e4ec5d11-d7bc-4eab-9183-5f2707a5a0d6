package com.centerm.basic.msgcenter.advert.mapper;

import com.centerm.basic.msgcenter.advert.domain.Advert;
import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 广告 数据层
 *
 * <AUTHOR> auto
 * @date 2019-03-17
 */
public interface AdvertMapper extends BaseMapper<Advert> {
    boolean downAdverts(List<String> lst);

    List<Advert> list(Advert advert);

    List<Advert> selectAdvertActive();

    List<Advert> selectAdvertActiveTemplate();

    List<Integer> selectAdvertTempletIds();
}