package com.centerm.basic.msgcenter.instruct.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 运维指令表 t_instruct
 * 
 * <AUTHOR> auto
 * @date 2019-03-17
 */
@ApiModel(value = "运维指令")
@Data
@ToString
@TableName("t_instruct")
@Accessors(chain = true)
public class Instruct{
private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "编号")
	@TableId(value = "id", type = IdType.UUID)
	private String id;

	@ApiModelProperty(value = "设备序列号")
	private String sn;

	@ApiModelProperty(value = "运维指令")
	private String command;

	@ApiModelProperty(value = "指令状态")
	private String status;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "备注信息")
	private String remark;

}
