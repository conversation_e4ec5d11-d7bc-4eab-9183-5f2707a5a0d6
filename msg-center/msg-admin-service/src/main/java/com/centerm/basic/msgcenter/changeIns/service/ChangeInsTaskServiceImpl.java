package com.centerm.basic.msgcenter.changeIns.service;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.basic.msgcenter.changeIns.domain.ChangeInsTask;
import com.centerm.basic.msgcenter.changeIns.mapper.ChangeInsTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class ChangeInsTaskServiceImpl extends ServiceImpl<ChangeInsTaskMapper, ChangeInsTask> implements IChangeInsTaskService {



    @Override
    public List<ChangeInsTask> selectTaskList(ChangeInsTask changeInsTask) {
        return baseMapper.selectTaskList(changeInsTask);
    }

    @Override
    public Integer insert(ChangeInsTask changeInsTask) {
        return baseMapper.insert(changeInsTask);
    }


}
