package com.centerm.basic.msgcenter.merchantbind.service;

import java.util.List;

import com.centerm.basic.msgcenter.merchantbind.domain.MerchantBind;
import com.centerm.basic.msgcenter.merchantbind.mapper.MerchantBindMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.common.support.Convert;

/**
 * 商户绑定 服务层实现
 * 
 * <AUTHOR> auto
 * @date 2020-05-26
 */
@Service
public class MerchantBindServiceImpl extends ServiceImpl<MerchantBindMapper, MerchantBind> implements IMerchantBindService
{
	
}
