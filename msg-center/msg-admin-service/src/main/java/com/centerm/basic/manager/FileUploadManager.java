package com.centerm.basic.manager;

import com.centerm.basic.msgcenter.appInf.service.IAppInfService;
import com.centerm.basic.msgcenter.appVersion.domain.AppVersion;
import com.centerm.basic.msgcenter.appVersion.service.IAppVersionService;
import com.centerm.basic.msgcenter.appVersionDetail.domain.AppVersionDetail;
import com.centerm.basic.msgcenter.appVersionDetail.service.IAppVersionDetailService;
import com.centerm.common.config.Global;
import com.centerm.common.exception.BusinessException;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.file.FileMD5;
import com.centerm.common.utils.file.FileUploadUtils;
import com.centerm.common.utils.file.ZipUtil;
import com.centerm.common.utils.xmlUtils.Config;
import com.centerm.common.utils.xmlUtils.Item;
import com.centerm.common.utils.xmlUtils.XmlToBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.xml.bind.JAXBException;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: msg-center
 * @description:
 * @author: Zhang Chong
 * @create: 2019/4/9 14:16
 **/
@Component
public class FileUploadManager {

    private static final String SETTINGS_NAME = "settings.xml";
    /**
     * 默认上传的地址
     */
    private static String defaultBaseDir = Global.getProfile();

    @Autowired
    private IAppInfService appInfService;
    @Autowired
    private IAppVersionService appVersionService;
    @Autowired
    private IAppVersionDetailService detailService;
    

    /**
     * 上传文件包
     * @param file
     * @return
     */
    public static String zipFileUpload(MultipartFile file){
        String filePath = null;
        try {
            filePath = FileUploadUtils.uploadZip(file);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return filePath;
    }

    /**
     * 解压文件
     * @param filePath 待解压文件路径
     * @return
     */
    public static String unzipFile(String filePath){
        String zipPath = defaultBaseDir + filePath;
        String unzipDir = filePath.substring(0,filePath.lastIndexOf("."));
        ZipUtil.unzip(zipPath, defaultBaseDir + unzipDir);
        return unzipDir;
    }
    

    /**
     * 文件解析解压
     * @param unzipDir
     * @return
     */
    public static Config xmlParseCheck(String unzipDir, String filePath){
        //获取解压文件的settings信息
        try{
            Config config = XmlToBean.getConfig(defaultBaseDir + unzipDir+"/"+SETTINGS_NAME);
            if(CommonUtils.isEmpty(config)){
                throwException(unzipDir, filePath, "配置文件不存在");
            }
            List<Item> items = config.getItems().getItems();

            if(CommonUtils.isEmpty(config.getVersion())
                    ||CommonUtils.isEmpty(config.getVersionCode())){
                throwException(unzipDir, filePath, "配置文件参数缺失");
            }
            for(Item item : items) {
                if (CommonUtils.isEmpty(item.getFilename())
                        || CommonUtils.isEmpty(item.getVersion())
                        || CommonUtils.isEmpty(item.getMd5())) {
                    throwException(unzipDir, filePath, "配置文件参数缺失");
                }
                String itemFileName = unzipDir + "/" + item.getFilename();
                File file1 = new File(defaultBaseDir + itemFileName);
                if (CommonUtils.isEmpty(file1)
                        || !FileMD5.getFileMD5String(file1).equalsIgnoreCase(item.getMd5())) {
                    throwException(unzipDir, filePath, "文件名或md5值有误");
                }
            }
            return config;
        }catch (IOException e){
            e.printStackTrace();
        } catch (JAXBException e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 文件解析解压
     * @param unzipDir
     * @return
     */
    public static List<AppVersionDetail> xmlParse(String unzipDir,String filePath){
        //获取解压文件的settings信息
        try{
            Config config = XmlToBean.getConfig(defaultBaseDir + unzipDir+"/"+SETTINGS_NAME);
            if(CommonUtils.isEmpty(config)){
                throwException(unzipDir, filePath,  "配置文件不存在");
            }
            List<Item> items = config.getItems().getItems();

            if(CommonUtils.isEmpty(config.getVersion())
                    ||CommonUtils.isEmpty(config.getVersionCode())){
                throwException(unzipDir, filePath, "配置文件参数缺失");
            }
            List<AppVersionDetail> details = new ArrayList<>(items.size());
            for(Item item : items) {
                if (CommonUtils.isEmpty(item.getFilename())
                        || CommonUtils.isEmpty(item.getVersion())
                        || CommonUtils.isEmpty(item.getMd5())) {
                    throwException(unzipDir, filePath, "配置文件参数缺失");
                }
                String itemFileName = unzipDir + "/" + item.getFilename();
                File file1 = new File(defaultBaseDir + itemFileName);
                AppVersionDetail detail = new AppVersionDetail();
                detail.setType(item.getType());
                detail.setAppVersion(item.getVersion());
                detail.setFileName(item.getFilename());
                detail.setMd5(item.getMd5());
                detail.setAppPath(itemFileName);
                detail.setSize(Integer.parseInt(String.valueOf(file1.length())));
                details.add(detail);
                if (CommonUtils.isEmpty(file1)
                        || !FileMD5.getFileMD5String(file1).equalsIgnoreCase(item.getMd5())) {
                    throwException(unzipDir, filePath, "文件名或md5值有误");
                }
            }
            return details;
        }catch (IOException e){
            e.printStackTrace();
        } catch (JAXBException e) {
            e.printStackTrace();
        }
        return null;
    }
    public static void throwException(String unzipDir,String filePath, String errorMsg){
        //删除解压后的文件
        File files = new File(defaultBaseDir + unzipDir);
        if(files.isDirectory()){
            for(File file1 : files.listFiles()){
                file1.delete();
            }
            files.delete();
        }
        //删除解压前的文件
        new File(defaultBaseDir + filePath).delete();
        throw new BusinessException("upload.file.xml.error", errorMsg);
        
    }

}
