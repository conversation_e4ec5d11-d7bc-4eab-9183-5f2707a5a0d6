package com.centerm.basic.msgcenter.logMoney.mapper;

import com.centerm.basic.msgcenter.logMoney.domain.LogMoney;
import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.centerm.basic.msgcenter.logMoney.dto.LogMoneyRequest;
import com.centerm.basic.msgcenter.logMoney.dto.StatisticsRequest;
import com.centerm.common.dto.transaction.LogMoneySummary;
import org.apache.ibatis.annotations.Param;

/**
 * 消息记录 数据层
 *
 * <AUTHOR> auto
 * @date 2019-03-25
 */
public interface LogMoneyMapper extends BaseMapper<LogMoney> {
    List<LogMoneySummary> summary(String sn);
    List<LogMoneySummary> summary(LogMoneyRequest request);
    List<LogMoneySummary> summary(StatisticsRequest request);
    List<LogMoney> selectList(LogMoneyRequest request);
    List<LogMoney> selectListBySn(@Param("sn") String sn);

    long selectCountTotal(LogMoneyRequest request);
}