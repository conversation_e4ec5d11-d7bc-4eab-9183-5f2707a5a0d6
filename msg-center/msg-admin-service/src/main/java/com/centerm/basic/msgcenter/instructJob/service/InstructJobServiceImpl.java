package com.centerm.basic.msgcenter.instructJob.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.basic.manager.JobManager;
import com.centerm.basic.manager.factory.JobFactory;
import com.centerm.basic.msgcenter.instructJob.domain.InstructJob;
import com.centerm.basic.msgcenter.instructJob.mapper.InstructJobMapper;
import com.centerm.basic.msgcenter.paramJob.domain.ParamJob;
import com.centerm.common.constant.JobConstants;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @created 2022/4/22 上午9:07
 */
@Service
public class InstructJobServiceImpl extends ServiceImpl<InstructJobMapper, InstructJob> implements IInstructJobService {


    @Override
    public boolean publish(String id) {
        InstructJob job = getById(id);
        job.setReleaseStatus(JobConstants.RELEASE_STATUS_START);
        getBaseMapper().updateById(job);
        JobManager.execute(JobFactory.instructPush(job));
        return true;
    }

    @Override
    public List<InstructJob> selectList(InstructJob job) {
       return getBaseMapper().selectList(job);
    }
}