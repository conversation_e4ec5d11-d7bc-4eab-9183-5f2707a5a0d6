package com.centerm.basic.msgcenter.merchant.service;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.centerm.basic.msgcenter.merchant.mapper.MerchantMapper;
import com.centerm.basic.msgcenter.merchant.domain.Merchant;
import com.centerm.basic.msgcenter.merchant.service.IMerchantService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.common.support.Convert;

/**
 * 商户 服务层实现
 * 
 * <AUTHOR> auto
 * @date 2019-04-01
 */
@Service
public class MerchantServiceImpl extends ServiceImpl<MerchantMapper, Merchant> implements IMerchantService
{

    @Override
    public boolean updateAccountById(Merchant merchant) {
        return this.retBool(this.baseMapper.updateById(merchant));
    }

    @Override
    public Merchant selectAccountByPhone(String phone) {
        return this.baseMapper.selectAccountByPhone(phone);
    }
}
