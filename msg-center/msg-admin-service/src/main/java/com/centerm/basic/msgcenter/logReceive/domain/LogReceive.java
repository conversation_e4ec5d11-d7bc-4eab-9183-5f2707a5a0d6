package com.centerm.basic.msgcenter.logReceive.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-11-24
 */
@ApiModel(value = "消息记录")
@Data
@ToString
@TableName("t_log_receive")
@Accessors(chain = true)
public class LogReceive {
    @TableId(value = "id",type = IdType.UUID)
    private String id;
    private String sn;
    private Long deptId;
    private Date createTime;
    private String message;
    private String reqNumber;
    private String result;
    private String status;
}
