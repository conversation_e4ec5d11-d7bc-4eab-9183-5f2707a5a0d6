package com.centerm.basic.msgcenter.instructTask.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.centerm.basic.msgcenter.instructJob.domain.InstructJob;
import com.centerm.basic.msgcenter.instructJob.service.IInstructJobService;
import com.centerm.basic.msgcenter.instructTask.domain.InstructTask;
import com.centerm.basic.msgcenter.instructTask.mapper.InstructTaskMapper;
import com.centerm.common.constant.JobConstants;
import com.centerm.common.enums.CommonTopicEnums;
import com.centerm.common.utils.DateUtils;
import com.centerm.common.utils.emq.MqttUtils;
import com.centerm.common.utils.spring.SpringUtils;
import com.centerm.framework.service.IRmiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @created 2022/4/22 上午9:09
 */
@Slf4j
@Service
public class IInstructTaskServiceImpl extends ServiceImpl<InstructTaskMapper, InstructTask> implements IInstructTaskService {

    @Autowired
    private IInstructJobService instructJobService;

    @Override
    public Boolean publis(String id) {

        InstructTask instructTask = getById(id);
        if (instructTask == null) {
            return false;
        }

        InstructJob instructJob =  instructJobService.getById(instructTask.getJobId());
        if (instructJob == null) {
            return false;
        }

        // 调用远程指令接口
        com.centerm.common.dto.Instruct instructRequest = new com.centerm.common.dto.Instruct();
        instructRequest.setCommand(instructJob.getCommand());
        instructRequest.setSn(instructTask.getTermSeq());
        instructRequest.setTaskId(id);
        boolean result = false;
        try {
            result = SpringUtils.getBean(IRmiService.class).instruct(instructRequest);
        }
        catch (Exception e) {
            log.error("{}", e);
        }

        instructTask.setDlFlag(result ? JobConstants.DL_STATUS_NORMAL : JobConstants.DL_STATUS_WAIT);
        instructTask.setUpdateTime(DateUtils.getNowDate());
        result = updateById(instructTask);

        return result;
    }
}
