/*
package com.centerm.basic.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.centerm.basic.msgcenter.downloadTask.domain.DownloadTask;
import com.centerm.basic.msgcenter.downloadTask.mapper.DownloadTaskMapper;
import com.centerm.basic.msgcenter.paramJob.mapper.ParamJobMapper;
import com.centerm.basic.msgcenter.paramTask.domain.ParamTask;
import com.centerm.basic.msgcenter.paramTask.mapper.ParamTaskMapper;
import com.centerm.common.config.ParamConfig;
import com.centerm.common.constant.JobConstants;
import com.centerm.common.constant.MsgCenterConstants;
import com.centerm.common.dto.Instruct;
import com.centerm.common.dto.params.Params;
import com.centerm.common.dto.params.ParamsDTO;
import com.centerm.common.enums.GatewayPath;
import com.centerm.common.utils.DateUtils;
import com.centerm.common.utils.http.HttpClient;
import com.centerm.common.utils.spring.SpringUtils;
import com.centerm.framework.service.IRmiService;
import com.centerm.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

*/
/**
 * @program: msg-center
 * @description: 任务重试机制
 * @author: Zhang Chong
 * @create: 2019/6/13 15:09
 **//*

@Component("jobTask")
@Slf4j
public class JobTask {
	*/
/**
	 * 1.任务重发功能
	 *//*

	public void downloadJobRetry(){
		DownloadTask downloadTask = new DownloadTask();
		Integer retryCount = Integer.parseInt(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(MsgCenterConstants.JOB_RETRY_COUNT));
		Integer retryLimitCount = Integer.parseInt(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(MsgCenterConstants.JOB_RETRY_LIMIT_COUNT));
		downloadTask.setRetryCount(retryCount);
		downloadTask.setRetryLimitCount(retryLimitCount);
		List<DownloadTask> listRetry = SpringUtils.getBean(DownloadTaskMapper.class).selectListToRetry(downloadTask);

		Instruct instruct = new Instruct();
		instruct.setCommand(MsgCenterConstants.COMMAND_INSTRUCT);

		for(DownloadTask task : listRetry){
			instruct.setSn(task.getTermSeq());
			boolean result = SpringUtils.getBean(IRmiService.class).instruct(instruct);
			if(result){
				downloadTask.setDlFlag(JobConstants.DL_STATUS_NORMAL);
			}else {
				downloadTask.setDlFlag(JobConstants.DL_STATUS_START);
			}
			downloadTask.setUpdateTime(DateUtils.getNowDate());
			downloadTask.setRetryCount(downloadTask.getRetryCount()+1);
			SpringUtils.getBean(DownloadTaskMapper.class).updateById(task);
		}
	}
	*/
/**
	 * 1.参数任务重发功能
	 *//*

	public void paramJobRetry(){
		ParamTask task = new ParamTask();
		Integer retryLimitCount = Integer.parseInt(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(MsgCenterConstants.JOB_RETRY_LIMIT_COUNT));
		task.setRetryLimitCount(retryLimitCount);
		List<ParamTask> listRetry = SpringUtils.getBean(ParamTaskMapper.class).selectListToRetry(task);



		for(ParamTask paramTask : listRetry){

			ParamsDTO paramsDTO = new ParamsDTO();
			paramsDTO.setParamContent(paramTask.getParamContent());
			paramsDTO.setSn(paramTask.getTermSeq());
			boolean result = SpringUtils.getBean(IRmiService.class).paramPub(paramsDTO);
			if(result){
				paramTask.setDlFlag(JobConstants.DL_STATUS_NORMAL);
			}else {
				paramTask.setDlFlag(JobConstants.DL_STATUS_START);
			}
			paramTask.setUpdateTime(DateUtils.getNowDate());
			paramTask.setRetryCount(paramTask.getRetryCount()+1);
			SpringUtils.getBean(ParamTaskMapper.class).insert(paramTask);
		}
	}
}
*/
