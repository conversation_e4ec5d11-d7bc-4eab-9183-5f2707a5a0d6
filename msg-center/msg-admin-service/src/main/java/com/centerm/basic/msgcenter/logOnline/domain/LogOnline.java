package com.centerm.basic.msgcenter.logOnline.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 终端上下线记录表 t_log_online
 * 
 * <AUTHOR> auto
 * @date 2020-04-07
 */
@ApiModel(value = "终端上下线记录")
@Data
@ToString
@TableName("t_log_online")
@Accessors(chain = true)
public class LogOnline{
private static final long serialVersionUID = 1L;


    @TableId(type = IdType.UUID)
    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "设备序列号")
    private String sn;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "时间")
    private Date time;

    @ApiModelProperty(value = "机构id")
    private Long deptId;

}
