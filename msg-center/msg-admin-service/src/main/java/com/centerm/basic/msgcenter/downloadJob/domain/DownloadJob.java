package com.centerm.basic.msgcenter.downloadJob.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.centerm.common.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 任务表 t_download_job
 * 
 * <AUTHOR> auto
 * @date 2019-04-11
 */
@ApiModel(value = "任务")
@Data
@ToString
@TableName("t_download_job")
@Accessors(chain = true)
public class DownloadJob extends BaseEntity {
private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "编号id")
	private Integer id;

	@ApiModelProperty(value = "作业名称")
	private String jobName;

	@ApiModelProperty(value = "软件id")
	private Integer appId;

	@ApiModelProperty(value = "软件版本信息")
	private Integer appVersionId;

	@ApiModelProperty(value = "厂商编号id")
	private Integer manufacturerId;

	@ApiModelProperty(value = "生产厂商")
	private String producer;

	@ApiModelProperty(value = "终端型号id")
	private Integer terminalTypeId;

	@ApiModelProperty(value = "设备型号")
	private String model;

	@ApiModelProperty(value = "文件大小")
	private Integer size;

	@ApiModelProperty(value = "发布时间")
	private Date releaseTime;

	@ApiModelProperty(value = "有效期")
	private Date validDate;

	@ApiModelProperty(value = "发布状态")
	private String releaseStatus;

	@ApiModelProperty(value = "发布类型")
	private String releaseType;

	@ApiModelProperty(value = "更新类型")
	private String updateType;

	@ApiModelProperty(value = "机构编号")
	private Long deptId;

	@ApiModelProperty(value = "发布组别编码")
	private Long groupId;

	@ApiModelProperty(value = "终端集合")
	private String collection;

    @ApiModelProperty(value = "终端固件版本")
	private String firmwareVersion;

	@TableField(exist = false)
	private String groupIds;

	@TableField(exist = false)
	@ApiModelProperty(value = "更新成功数目")
	private String success;

	@TableField(exist = false)
	@ApiModelProperty(value = "更新失败数目")
	private String failure;

	@TableField(exist = false)
	@ApiModelProperty(value = "更新暂停数目")
	private String pause;

	@TableField(exist = false)
	@ApiModelProperty(value = "更新待下发数目")
	private String pending;

	@TableField(exist = false)
	@ApiModelProperty(value = "更新下发中数目")
	private String onGoing;

	private String timeZone;
}