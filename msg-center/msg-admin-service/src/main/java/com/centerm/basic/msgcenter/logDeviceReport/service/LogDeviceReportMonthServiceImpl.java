package com.centerm.basic.msgcenter.logDeviceReport.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import com.centerm.basic.msgcenter.logDeviceReport.domain.LogDeviceReportMonth;
import com.centerm.basic.msgcenter.logDeviceReport.dto.LogDeviceReportDto;
import com.centerm.basic.msgcenter.logDeviceReport.mapper.LogDeviceReportMonthMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
/**
 * 每月终端数据统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-12-07
 */
@Service
public class LogDeviceReportMonthServiceImpl extends ServiceImpl<LogDeviceReportMonthMapper, LogDeviceReportMonth>  implements ILogDeviceReportMonthService {
 

    /**
     * 查询每月终端数据统计
     * 
     * @param deptId 每月终端数据统计ID
     * @return 每月终端数据统计
     */
    @Override
    public LogDeviceReportMonth selectLogDeviceReportMonthById(Long deptId){
        return baseMapper.selectLogDeviceReportMonthById(deptId);
    }

    /**
     * 查询每月终端数据统计列表
     * 
     * @param tLogDeviceReportMonth 每月终端数据统计
     * @return 每月终端数据统计
     */
    @Override
    public List<LogDeviceReportMonth> selectLogDeviceReportMonthList(LogDeviceReportMonth tLogDeviceReportMonth){
        return baseMapper.selectLogDeviceReportMonthList(tLogDeviceReportMonth);
    }

    @Override
    public LogDeviceReportMonth selectLogDeviceReportLastMonth(LogDeviceReportMonth logDeviceReportMonth) {
        return baseMapper.selectLogDeviceReportLastMonth(logDeviceReportMonth);
    }

    @Override
    public LogDeviceReportDto selectLogDeviceReport(LogDeviceReportMonth tLogDeviceReportMonth) {
        return baseMapper.selectLogDeviceReport(tLogDeviceReportMonth);
    }

    @Override
    public List<LogDeviceReportMonth> selectLogDeviceReportList(LogDeviceReportMonth logDeviceReportMonth) {
        return baseMapper.selectLogDeviceReportList(logDeviceReportMonth);
    }

}
