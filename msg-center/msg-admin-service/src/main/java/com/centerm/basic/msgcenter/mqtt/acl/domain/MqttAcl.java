package com.centerm.basic.msgcenter.mqtt.acl.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * emq认证acl表 mqtt_acl
 *
 * <AUTHOR> auto
 * @date 2019-03-05
 */
@ApiModel(value = "emq认证acl")
@Data
@ToString
@TableName("mqtt_acl")
@Accessors(chain = true)
public class MqttAcl{
	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "编号")
	private Integer id;

	@ApiModelProperty(value = "规则 0: 不允许, 1: 允许")
	private Integer allow;

	@ApiModelProperty(value = "IP地址")
	private String ipaddr;

	@ApiModelProperty(value = "用户名")
	private String username;

	@ApiModelProperty(value = "客户端ID")
	private String clientid;

	@ApiModelProperty(value = "操作权限 1: 订阅, 2: 发布, 3: 发布订阅")
	private Integer access;

	@ApiModelProperty(value = "主题过滤")
	private String topic;

}
