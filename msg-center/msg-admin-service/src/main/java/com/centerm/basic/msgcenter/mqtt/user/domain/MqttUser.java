package com.centerm.basic.msgcenter.mqtt.user.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * emq认证user表 mqtt_user
 *
 * <AUTHOR>
 * @date 2019-03-19
 */
@ApiModel(value = "emq认证user")
@Data
@ToString
@TableName("mqtt_user")
@Accessors(chain = true)
public class MqttUser{
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "编号")
	private Integer id;

	@ApiModelProperty(value = "用户名")
	private String username;

	@ApiModelProperty(value = "密码")
	private String password;

	@ApiModelProperty(value = "密码盐")
	private String salt;

	@ApiModelProperty(value = "设备SN号")
	private String remark;

	@ApiModelProperty(value = "是否超级用户")
	private Integer isSuperuser;

	@ApiModelProperty(value = "创建时间")
	private Date created;

}
