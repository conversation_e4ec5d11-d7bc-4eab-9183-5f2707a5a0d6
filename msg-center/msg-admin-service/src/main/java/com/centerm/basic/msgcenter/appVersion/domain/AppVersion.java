package com.centerm.basic.msgcenter.appVersion.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 驱动版本表 t_app_version
 * 
 * <AUTHOR> auto
 * @date 2019-04-08
 */
@ApiModel(value = "驱动版本")
@Data
@ToString
@TableName("t_app_version")
@Accessors(chain = true)
public class AppVersion{
private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "应用编号")
	private Integer id;

	@ApiModelProperty(value = "软件编号")
	private Integer appId;

	@ApiModelProperty(value = "软件名称")
	private String appName;

	@ApiModelProperty(value = "厂商编号id")
	private Integer manufacturerId;

	@ApiModelProperty(value = "生产厂商")
	private String producer;

	@ApiModelProperty(value = "终端类型编号")
	private Integer termTypeId;

	@ApiModelProperty(value = "终端型号")
	private String model;

	@ApiModelProperty(value = "应用版本号")
	private String appVersion;

	@ApiModelProperty(value = "应用内部版本号")
	private Integer appVersionCode;

	@ApiModelProperty(value = "原文件名称")
	private String fileName;

	@ApiModelProperty(value = "版本状态")
	private String versionStatus;

	@ApiModelProperty(value = "md5")
	private String md5;

	@ApiModelProperty(value = "app保存路径")
	private String appPath;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "操作标志")
	private String operFlag;

	@ApiModelProperty(value = "")
	private Integer size;

	@ApiModelProperty(value = "")
	private Integer insId;

	@ApiModelProperty(value = "")
	private String iconPath;

	@ApiModelProperty(value = "文件id")
	private Integer fileId;

}
