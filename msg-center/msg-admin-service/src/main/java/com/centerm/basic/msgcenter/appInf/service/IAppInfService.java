package com.centerm.basic.msgcenter.appInf.service;

import com.centerm.basic.msgcenter.appInf.domain.AppInf;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.basic.msgcenter.appInf.dto.AppInfDto;
import com.centerm.basic.msgcenter.appVersion.domain.AppVersion;
import com.centerm.common.dto.file.FileUploadResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 软件 服务层
 * 
 * <AUTHOR> auto
 * @date 2019-04-08
 */
public interface IAppInfService extends IService<AppInf>
{
   FileUploadResponse upload(MultipartFile file);
   boolean addApp(AppInfDto appInfDto);
   List<AppInfDto> list(AppInfDto appInfDto);



   boolean deleteApp(AppInfDto appInfDto);
   List<AppVersion> selectAppVersions(AppInf appInf);
   AppInf selectAppInf(AppInf appInf);

}
