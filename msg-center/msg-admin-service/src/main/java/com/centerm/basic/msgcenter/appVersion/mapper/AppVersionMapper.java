package com.centerm.basic.msgcenter.appVersion.mapper;

import com.centerm.basic.msgcenter.appVersion.domain.AppVersion;
import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 驱动版本 数据层
 *
 * <AUTHOR> auto
 * @date 2019-04-08
 */
public interface AppVersionMapper extends BaseMapper<AppVersion> {

    List<AppVersion> selectByAppId(Integer appId);
    AppVersion selectByAppIdAndVerCode(@Param("appId") Integer appId, @Param("appVersionCode") Integer appVersionCode);
}