package com.centerm.basic.msgcenter.group.service;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.centerm.common.annotation.DataScope;
import com.centerm.common.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.centerm.basic.msgcenter.group.mapper.GroupMapper;
import com.centerm.basic.msgcenter.group.domain.Group;
import com.centerm.basic.msgcenter.group.service.IGroupService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.common.support.Convert;
import org.springframework.web.bind.annotation.ModelAttribute;

/**
 * 组别 服务层实现
 * 
 * <AUTHOR> auto
 * @date 2019-03-15
 */
@Service("groupService")
public class GroupServiceImpl extends ServiceImpl<GroupMapper, Group> implements IGroupService 
{
    @Override
    public List<Group> selectGroupList(Group entity) {
        return this.baseMapper.selectGroupList(entity);
    }

    @Override
    public List<Group> selectGroupList() {
        return getBaseMapper().selectGroupList(new Group());
    }

    @Override
    public void checkGroup(Group group) {
        Group queryGroup = getOne(new LambdaQueryWrapper<Group>().eq(Group::getGroupName, group.getGroupName()));
        if (queryGroup != null){
            throw new BusinessException("add.data.result", "The group already exists.");
        }
    }
}
