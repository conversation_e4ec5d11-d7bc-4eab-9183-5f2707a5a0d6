package com.centerm.basic.msgcenter.merchant.service;

import com.centerm.basic.msgcenter.merchant.domain.Merchant;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 商户 服务层
 * 
 * <AUTHOR> auto
 * @date 2019-04-01
 */
public interface IMerchantService extends IService<Merchant>
{

    boolean updateAccountById(Merchant merchant);

    Merchant selectAccountByPhone(String phone);
}
