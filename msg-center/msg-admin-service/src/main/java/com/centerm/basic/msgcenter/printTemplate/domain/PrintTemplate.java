package com.centerm.basic.msgcenter.printTemplate.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 打印模板实体 t_print_template
 *
 * <AUTHOR>
 * @date 2020-12-10
 */
@ApiModel(value = "打印模板实体")
@Data
@TableName("t_print_template")
@Accessors(chain = true)
public class PrintTemplate{
    private static final long serialVersionUID = 1L;
    @TableId
    private Long id;
    @ApiModelProperty(value = "模板编号")
    private String templateName;
    @ApiModelProperty(value = "模板值")
    private String templateValue;
    @ApiModelProperty(value = "状态")
    private String status;
}
