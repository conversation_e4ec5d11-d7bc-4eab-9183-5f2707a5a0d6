package com.centerm.basic.msgcenter.advert.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: msg-center
 * @description: 播报规则
 * @author: <PERSON>
 * @create: 2019/3/20 11:25
 **/
@Data
public class BroadcastRuleDTO {
    @ApiModelProperty("每小时播报的上限次数")
    private Integer timesOnHour;
    @ApiModelProperty("最小间隔时间")
    private Integer minTimeInterval;
}
