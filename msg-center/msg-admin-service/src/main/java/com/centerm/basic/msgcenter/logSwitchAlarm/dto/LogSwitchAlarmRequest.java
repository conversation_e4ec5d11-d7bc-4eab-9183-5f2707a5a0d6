package com.centerm.basic.msgcenter.logSwitchAlarm.dto;

import com.centerm.common.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;


@ApiModel(value = "终端切换记录")
@Data
@ToString
public class LogSwitchAlarmRequest extends BaseEntity {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "")
	private Long id;

	@ApiModelProperty(value = "设备序列号")
	private String sn;

	@ApiModelProperty(value = "版本")
	private String version;

	@ApiModelProperty(value = "机构id")
	private int deptId;

	@ApiModelProperty(value = "时间")
	private Date createDate;

    @ApiModelProperty(value = "处理状态：0：待处理； 1：已处理")
    private String status;

}
