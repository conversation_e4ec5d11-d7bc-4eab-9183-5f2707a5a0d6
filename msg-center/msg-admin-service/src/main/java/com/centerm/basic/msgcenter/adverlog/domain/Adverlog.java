package com.centerm.basic.msgcenter.adverlog.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 广告记录表 t_advert_log
 * 
 * <AUTHOR> auto
 * @date 2019-03-17
 */
@ApiModel(value = "广告记录")
@Data
@ToString
@TableName("t_advert_log")
@Accessors(chain = true)
public class Adverlog{
private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "编号")
	private String id;

	@ApiModelProperty(value = "编号")
	private String advertId;

	@ApiModelProperty(value = "设备序列号")
	private String sn;

	@ApiModelProperty(value = "广告内容")
	private String content;

	@ApiModelProperty(value = "广告类型")
	private String type;

	@ApiModelProperty(value = "广告播报方式")
	private String broadcastType;

	@ApiModelProperty(value = "广告状态")
	private String status;

	@ApiModelProperty(value = "下发时间")
	private Date createTime;

	@ApiModelProperty(value = "备注信息")
	private String remark;

}
