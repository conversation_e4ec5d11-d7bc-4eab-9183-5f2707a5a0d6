package com.centerm.basic.msgcenter.paramTask.service;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.centerm.basic.msgcenter.paramTask.mapper.ParamTaskMapper;
import com.centerm.basic.msgcenter.paramTask.domain.ParamTask;
import com.centerm.basic.msgcenter.paramTask.service.IParamTaskService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.common.support.Convert;

/**
 * 参数下发 服务层实现
 * 
 * <AUTHOR> auto
 * @date 2019-06-13
 */
@Service
public class ParamTaskServiceImpl extends ServiceImpl<ParamTaskMapper, ParamTask> implements IParamTaskService 
{
	
}
