package com.centerm.basic.msgcenter.adverlog.service;

import com.centerm.basic.msgcenter.adverlog.domain.Adverlog;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.basic.msgcenter.adverlog.dto.AdvertStatistic;
import com.centerm.basic.msgcenter.advert.domain.Advert;

/**
 * 广告记录 服务层
 * 
 * <AUTHOR> auto
 * @date 2019-03-17
 */
public interface IAdverlogService extends IService<Adverlog>
{
    /**
     * 获取广告统计
     * @return
     */
    List<AdvertStatistic> countAdSToday(Advert advert);
}
