package com.centerm.basic.msgcenter.emqClient.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @created 2021/12/22 上午9:14
 */

@Data
@ApiModel
public class SubscribeProgress {

    @ApiModelProperty(value = "客户端总数")
    private long totalNum;

    @ApiModelProperty(value = "执行进度")
    private long progressNum;

    @ApiModelProperty(value = "执行百分比")
    private double progressPercent;

    @ApiModelProperty(value = "有订阅主题的终端数")
    private long subscribeNum;

    @ApiModelProperty(value = "是否停止订阅")
    private boolean isStop;

    public void init() {
        this.totalNum = 0;
        this.progressNum = 0;
        this.progressPercent = 0;
        this.subscribeNum = 0;
        this.isStop = false;
    }

}
