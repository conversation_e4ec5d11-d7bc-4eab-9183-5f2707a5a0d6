package com.centerm.basic.msgcenter.appVersion.service;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.centerm.basic.msgcenter.appVersion.mapper.AppVersionMapper;
import com.centerm.basic.msgcenter.appVersion.domain.AppVersion;
import com.centerm.basic.msgcenter.appVersion.service.IAppVersionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.common.support.Convert;

/**
 * 驱动版本 服务层实现
 * 
 * <AUTHOR> auto
 * @date 2019-04-08
 */
@Service
public class AppVersionServiceImpl extends ServiceImpl<AppVersionMapper, AppVersion> implements IAppVersionService
{

    @Override
    public List<AppVersion> selectByAppId(Integer appId) {
        return this.baseMapper.selectByAppId(appId);
    }
}
