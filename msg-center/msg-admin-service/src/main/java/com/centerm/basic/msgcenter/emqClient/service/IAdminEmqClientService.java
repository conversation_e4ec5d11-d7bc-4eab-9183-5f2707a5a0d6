package com.centerm.basic.msgcenter.emqClient.service;

import com.centerm.basic.msgcenter.emqClient.dto.EMQNode;
import com.centerm.common.page.TableDataInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @created 2021/12/20 上午10:24
 */
public interface IAdminEmqClientService {

    public TableDataInfo listEmqClient(String nodeName, int pageNum, int pageSize);

    public TableDataInfo getEmqClient(String clientId);

    public Boolean batchSubscribe(String nodeName);

    public Boolean subcribe(String clientid);

    public List<EMQNode> getEmqNodes();
}
