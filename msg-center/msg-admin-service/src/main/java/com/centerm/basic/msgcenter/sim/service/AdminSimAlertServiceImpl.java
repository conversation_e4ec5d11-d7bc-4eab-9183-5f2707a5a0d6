package com.centerm.basic.msgcenter.sim.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.basic.msgcenter.sim.domain.SimAlert;
import com.centerm.basic.msgcenter.sim.mapper.SimAlertMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @created 2021/9/7 下午5:43
 */
@Service
public class AdminSimAlertServiceImpl extends ServiceImpl<SimAlertMapper, SimAlert> implements IAdminSimAlertService {


    @Override
    public boolean deleteExpiresSimAlert() {

        return this.getBaseMapper().deleteExpiresSimAlert();
    }

    public List<SimAlert> listSimAlerts(SimAlert simAlert) {
        return this.getBaseMapper().listSimAlerts(simAlert);
    }
}
