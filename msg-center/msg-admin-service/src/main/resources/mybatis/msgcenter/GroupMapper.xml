<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.group.mapper.GroupMapper">
    
    <resultMap type="Group" id="GroupResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="groupName"    column="group_name"    />
        <result property="description"    column="description"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
	
	<sql id="selectGroupVo">
        select id, dept_id, group_name, description, create_by, create_time, update_by, update_time from t_group
    </sql>
    <sql id="selectGroupDeptVo">
        select g.id, g.dept_id,d.dept_name, g.group_name, g.description, g.create_by, g.create_time, g.update_by, g.update_time
        from t_group g
        left join sys_dept d on g.dept_id = d.dept_id
    </sql>
    <select id="selectGroupList" resultType="Group">
        <include refid="selectGroupDeptVo"/>
        where 1=1
        <if test="deptId != null and deptId != ''"> and g.dept_id = #{deptId}</if>

        <if test="groupName != null and groupName != ''">and g.group_name like concat('%', #{groupName}, '%')</if>
        ${params.dataScope}
        order by g.create_time desc
    </select>

</mapper>