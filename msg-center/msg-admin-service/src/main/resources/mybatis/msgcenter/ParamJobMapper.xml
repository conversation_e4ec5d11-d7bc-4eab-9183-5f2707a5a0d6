<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.paramJob.mapper.ParamJobMapper">
    <sql id="ifDeptIdSql">
        <if test="deptId != null and deptId != ''">
            and (a.dept_id = #{deptId}
            or a.dept_id in (SELECT dept_id FROM sys_dept
            where FIND_IN_SET (#{deptId},ancestors)))
        </if>
    </sql>
    <select id="selectList" resultType="com.centerm.basic.msgcenter.paramJob.domain.ParamJob">
        select m.* from (select t.*,
        case
        when t.release_status_tmp != 2 then t.release_status_tmp
        when t.number = (t.success + t.failure + t.pause) then 4
        when <![CDATA[ t.VALID_DATE <= CURRENT_TIMESTAMP ]]> then 4
        else t.release_status_tmp end as release_status from
        (select release_status AS release_status_tmp, id, job_name, param_content,
               manufacturer_id, terminal_type_id, release_time, valid_date,
               release_type, update_type, dept_id, group_id, collection, create_by,
               create_time, update_by, update_time, remark,
        (SELECT count(*) FROM t_param_task b WHERE b.JOB_ID = a.id ) AS number,
        (SELECT count(*) FROM t_param_task b WHERE b.JOB_ID = a.id and b.dl_flag = '4') AS success,
        (SELECT count(*) FROM t_param_task b WHERE b.JOB_ID = a.id and b.dl_flag = '3') AS failure,
        (SELECT count(*) FROM t_param_task b WHERE b.JOB_ID = a.id and b.dl_flag = '5') AS pause,
        (SELECT count(*) FROM t_param_task b WHERE b.JOB_ID = a.id and b.dl_flag = '1') AS pending,
        (SELECT count(*) FROM t_param_task b WHERE b.JOB_ID = a.id and b.dl_flag = '2') AS onGoing
        from
            t_param_job a
        where 1=1
        <if test="jobName != null and jobName != ''">
            and a.job_name like concat('%', #{jobName}, '%')
        </if>
        <include refid="ifDeptIdSql"/>
        ${params.dataScope}) t ) m
        order by m.create_time desc
    </select>
</mapper>