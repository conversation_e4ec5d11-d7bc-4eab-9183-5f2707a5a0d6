<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.merTerminalBind.mapper.MerTerminalBindMapper">
    
    <resultMap type="MerTerminalBind" id="MerTerminalBindResult">
        <result property="id"    column="id"    />
        <result property="sn"    column="sn"    />
        <result property="merNo"    column="mer_no"    />
        <result property="merName"    column="mer_name"    />
        <result property="storesNo"    column="stores_no"    />
        <result property="storesName"    column="stores_name"    />
        <result property="deptId"    column="dept_id"    />
        <result property="bindStatus" column="bind_status"/>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />

        <result property="activeTime" column="active_time"/>
        <result property="networkStatus"    column="network_status"    />
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="lastLogoutTime" column="last_logout_time"/>
        <result property="offlintTime" column="offlint_time"/>
        <result property="lac" column="lac"/>
        <result property="ci" column="ci"/>
        <result property="model" column="model"/>
    </resultMap>
    <sql id="selectMerTerminalBindVo">
        select id, sn, mer_no,mer_name, stores_no,stores_name, create_time, update_time, dept_id,create_by,update_by,
        remark,bind_status from t_mer_terminal_bind
    </sql>
    <sql id="ifDeptIdSql">
        <if test="deptId != null and deptId != ''">
            and (a.dept_id = #{deptId}
            or a.dept_id in (SELECT dept_id FROM sys_dept
            where FIND_IN_SET (#{deptId},ancestors)))
        </if>
    </sql>
    <!--设备列表-->
    <select id="selectList" resultType="com.centerm.basic.msgcenter.merTerminalBind.domain.MerTerminalBind">
        select a.id, a.sn,a.mer_no,a.mer_name,a.stores_no, a.stores_name,a.create_time, a.update_time, a.bind_status,
        b.dept_name,c.network_status,
        c.last_login_time,c.last_logout_time,
        c.model,c.lac,c.ci
        from t_mer_terminal_bind a
        left join t_device c on a.sn = c.sn
        left join sys_dept b on a.dept_id = b.dept_id
        where 1=1
        <if test="sn != null and sn != ''">
            and a.sn = #{sn}
        </if>
        <if test="merNo != null and merNo != ''">
            and a.mer_no = #{merNo}
        </if>
        <if test="bindStatus != null and bindStatus != ''">
            and a.bind_status = #{bindStatus}
        </if>

        <include refid="ifDeptIdSql"/>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by a.create_time desc
    </select>
    <update id="unBind" parameterType="com.centerm.basic.msgcenter.merTerminalBind.domain.MerTerminalBind">
        update t_mer_terminal_bind set bind_status = '0',update_time = CURRENT_TIME
        where sn = #{sn} and mer_no = #{merNo} and stores_no = #{storesNo}
    </update>

    <update id="UpdateByBindInfo" parameterType="com.centerm.basic.msgcenter.merTerminalBind.domain.MerTerminalBind">
        update t_mer_terminal_bind set bind_status = '1',create_time = CURRENT_TIME,
        update_time = CURRENT_TIME,mer_no = #{merNo},stores_no = #{storesNo},dept_id = #{deptId}
        <if test="merName != null and merName != ''">
            ,mer_name = #{merName}
        </if>
        <if test="storesName != null and storesName != ''">
            ,stores_name = #{storesName}
        </if>
        where sn = #{sn}
    </update>


    <update id="updateByMerAndStores" parameterType="com.centerm.basic.msgcenter.merTerminalBind.domain.MerTerminalBind">
        update t_mer_terminal_bind set bind_status = '1',create_time = CURRENT_TIME,
        update_time = CURRENT_TIME,sn = #{sn},dept_id = #{deptId}
        <if test="merName != null and merName != ''">
            ,mer_name = #{merName}
        </if>
        <if test="storesName != null and storesName != ''">
            ,stores_name = #{storesName}
        </if>
        where mer_no = #{merNo} and stores_no = #{storesNo}
    </update>

    <select id="selectBySn" resultType="com.centerm.basic.msgcenter.merTerminalBind.domain.MerTerminalBind">
        <include refid="selectMerTerminalBindVo"/>
        where sn = #{sn}
    </select>


    <select id="selectByMerAndStoresAndBind" resultType="com.centerm.basic.msgcenter.merTerminalBind.domain.MerTerminalBind">
        <include refid="selectMerTerminalBindVo"/>
        where mer_no = #{merNo} and stores_no = #{storesNo} and bind_status = '1'
    </select>

    <!--设备详情-->
    <select id="getDetailById" resultType="com.centerm.basic.msgcenter.merTerminalBind.domain.MerTerminalBind">
        select a.id, a.sn,a.mer_no,a.mer_name,a.stores_no, a.stores_name,a.create_time, a.update_time,
        case when a.bind_status = '0' then 'Unbound' else 'Bound' end as bind_status,
        b.dept_name,c.model,c.lac,c.ci,
        case when c.network_status = '1' then 'Online' else 'Offline' end as network_status,
        DATE_FORMAT(c.active_time,'%Y-%m-%d %H:%i:%s') as active_time,
        DATE_FORMAT(c.last_login_time,'%Y-%m-%d %H:%i:%s') as last_login_time,
        DATE_FORMAT(c.last_logout_time,'%Y-%m-%d %H:%i:%s') as last_logout_time,
        case when c.network_status = '1' then '0' else datediff(CURRENT_TIME,c.last_logout_time) end as offline_time
        from t_mer_terminal_bind a
        left join t_device c on a.sn = c.sn
        left join sys_dept b on a.dept_id = b.dept_id
        where a.id = #{id}
    </select>

    <select id="selectExportList" resultType="com.centerm.basic.msgcenter.merTerminalBind.domain.MerTerminalBind">
        select a.id, a.sn,a.mer_no,a.mer_name,a.stores_no, a.stores_name,a.create_time, a.update_time, a.bind_status,
        b.dept_name,
        case when c.network_status = '1' then 'Online' else 'Offline' end as network_status,c.active_time,
        c.last_login_time,c.last_logout_time,
        case when c.network_status = '1' then '0' else datediff(CURRENT_TIME,c.last_logout_time) end as offline_time,
        c.model,c.lac,c.ci
        from t_mer_terminal_bind a
        left join t_device c on a.sn = c.sn
        left join sys_dept b on a.dept_id = b.dept_id
        where 1=1
        <if test="sn != null and sn != ''">
            and a.sn = #{sn}
        </if>
        <if test="merNo != null and merNo != ''">
            and a.mer_no = #{merNo}
        </if>
        <if test="bindStatus != null and bindStatus != ''">
            and a.bind_status = #{bindStatus}
        </if>

        <include refid="ifDeptIdSql"/>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by a.create_time desc
    </select>

</mapper>