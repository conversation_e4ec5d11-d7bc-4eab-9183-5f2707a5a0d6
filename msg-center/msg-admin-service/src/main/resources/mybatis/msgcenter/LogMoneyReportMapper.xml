<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.logMoneyReport.mapper.LogMoneyReportMapper">
    
    <resultMap type="LogMoneyReport" id="LogMoneyReportResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="sn"    column="sn"    />
        <result property="paidTime"    column="paid_time"    />
        <result property="channel"    column="channel"    />
        <result property="paidCount"    column="paid_count"    />
        <result property="money"    column="money"    />
        <result property="paidType"    column="paid_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
    </resultMap>
	
	<sql id="selectLogMoneyReportVo">
        select a.id, a.dept_id, a.sn, a.paid_time, a.channel, a.paid_count, a.money, a.paid_type, a.create_by, a.create_time, a.update_by, a.update_time, a.remark from t_log_money_report a
    </sql>
    <sql id="ifDeptIdSql">
        <if test="deptId != null and deptId != ''">
            and (a.dept_id = #{deptId}
            or a.dept_id in (SELECT dept_id FROM sys_dept
            where FIND_IN_SET (#{deptId},ancestors)))
        </if>
    </sql>
    <sql id="tableChoose">
        <choose>
            <when test="dateType == 'lastMonth' or dateType == 'month'">
                t_log_money_report_month a
            </when>
            <when test="dateType == 'yesterday' or dateType == 'hour'">
                t_log_money_report_hour a
            </when>
            <otherwise >
                t_log_money_report_day a
            </otherwise>
        </choose>
    </sql>
    <sql id="ifCommon">
        <if test="startDate != null and startDate != ''">and a.paid_time <![CDATA[ >= ]]> #{startDate}</if>
        <if test="endDate != null and endDate != ''">and a.paid_time <![CDATA[ <= DATE_ADD(date(#{endDate}), INTERVAL 1 DAY) ]]> </if>
        <if test="dateType != null and dateType == 'yesterday'">and TO_DAYS(NOW())- TO_DAYS(a.paid_time) <![CDATA[ = ]]> 1</if>
        <if test="dateType != null and dateType == 'lastWeek'">and YEARWEEK(date_format(a.paid_time,'%Y-%m-%d'),7) = YEARWEEK(now(),7)-1</if>
        <if test="dateType != null and dateType == 'lastMonth'">and PERIOD_DIFF(date_format(now(),'%Y%m'), date_format(a.paid_time,'%Y%m'))=1</if>
    </sql>
    <select id="selectList" resultMap="LogMoneyReportResult" parameterType="LogMoneyReport">
        <include refid="selectLogMoneyReportVo"/>
        where 1=1
        <include refid="ifCommon"/>
        ${params.dataScope}
    </select>

    <select id="selectListNoChannel" resultMap="LogMoneyReportResult" parameterType="LogMoneyReport">
        SELECT
        	a.paid_time,
        	sum( a.paid_count ) AS paid_count,
        	sum( a.sn_count ) AS sn_count,
        	sum( a.success_count ) AS success_count,
        	sum( a.money ) AS money
        FROM
        	<include refid="tableChoose"/>
        WHERE
            a.paid_type = 0
            <include refid="ifDeptIdSql"/>
            <include refid="ifCommon"/>
            ${params.dataScope}
        GROUP BY
        	a.paid_time
    </select>

    <select id="selectListOnlyChannel" resultMap="LogMoneyReportResult" parameterType="LogMoneyReport">
        SELECT
            a.channel,
            sum( a.paid_count ) AS paid_count,
            sum( a.sn_count ) AS sn_count,
            sum( a.success_count ) AS success_count,
            sum( a.money ) AS money
        FROM
            <include refid="tableChoose"/>
        WHERE
            a.paid_type = 0
            <include refid="ifDeptIdSql"/>
            <include refid="ifCommon"/>
            ${params.dataScope}
        GROUP BY
            a.channel
    </select>

    <select id="selectCountHistory" resultType="java.lang.Long" parameterType="LogMoneyReport">
        SELECT
        IFNULL(sum( a.paid_count), 0) AS paid_count
        FROM
        <include refid="tableChoose"/>
        WHERE
        a.paid_type = 0
        <include refid="ifDeptIdSql"/>
        <include refid="ifCommon"/>
        ${params.dataScope}
    </select>
    <select id="selectCountIncrease" resultType="java.lang.Long" parameterType="LogMoneyReport">
        SELECT
        IFNULL(sum( a.paid_count), 0)  AS paid_count
        FROM
        <include refid="tableChoose"/>
        WHERE
        a.paid_type = 0
        <include refid="ifDeptIdSql"/>
        and a.paid_time <![CDATA[ >= ]]> date_sub(curdate(),interval day(curdate()) - 1 day)
        and a.paid_time <![CDATA[ <= DATE_ADD(NOW(), INTERVAL 1 DAY) ]]>
        ${params.dataScope}
    </select>
</mapper>