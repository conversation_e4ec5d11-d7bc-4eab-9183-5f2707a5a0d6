<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.adverlog.mapper.AdverlogMapper">
    
    <resultMap type="Adverlog" id="AdverlogResult">
        <result property="id"    column="id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="sn"    column="sn"    />
        <result property="content"    column="content"    />
        <result property="type"    column="type"    />
        <result property="broadcastType"    column="broadcast_type"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>
    <resultMap type="com.centerm.basic.msgcenter.adverlog.dto.AdvertStatistic" id="AdvertStatisticResult">
        <result property="id"    column="id"    />
        <result property="advertTopic"    column="advert_topic"    />
        <result property="content"    column="content"    />
        <result property="channel"    column="channel"    />
        <result property="broadcastCount"    column="broadcast_count"    />
    </resultMap>
	
	<sql id="selectAdverlogVo">
        select id, advert_id, sn, content, type, broadcast_type, status, create_time, remark from t_advert_log
    </sql>
    <select id="countAdSToday" resultType="com.centerm.basic.msgcenter.adverlog.dto.AdvertStatistic">
        select a.advert_topic, a.content,a.status,a.channel,b.broadcast_count from t_advert a
        LEFT JOIN (select COUNT(advert_id) as broadcast_count, advert_id from t_advert_log GROUP BY advert_id) b
        on a.id = b.advert_id
        where 1=1
        <if test="content != null and content != ''"> and a.content like concat('%', #{content}, '%')</if>
         <if test="status != null and status != ''"> and a.status = #{status} </if>
          <if test="type != null and type != ''"> and a.type = #{type}</if>
    </select>


</mapper>