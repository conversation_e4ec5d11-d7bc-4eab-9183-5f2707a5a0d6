<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.topic.mapper.TopicMapper">
    
    <resultMap type="com.centerm.basic.msgcenter.topic.domain.Topic" id="TopicResult">
        <result property="id"    column="id"    />
        <result property="topicName"    column="topic_name"    />
        <result property="type"    column="type"    />
        <result property="description"    column="description"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
	
	<sql id="selectTopicVo">
        select id, topic_name, type, description, create_by, create_time, update_by, update_time from t_topic
    </sql>
	
    
</mapper>