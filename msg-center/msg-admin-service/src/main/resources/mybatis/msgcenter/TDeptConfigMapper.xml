<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.deptConfig.mapper.TDeptConfigMapper">
    
    <resultMap type="TDeptConfig" id="TDeptConfigResult">
        <result property="deptId"    column="dept_id"    />
        <result property="printTemplateId"    column="print_template_id"    />
    </resultMap>

    <sql id="selectTDeptConfigVo">
        select t.dept_id, t.print_template_id from t_dept_config t
    </sql>

    <select id="selectTDeptConfigList" parameterType="TDeptConfig" resultMap="TDeptConfigResult">
        <include refid="selectTDeptConfigVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectTDeptConfigById" parameterType="Long" resultMap="TDeptConfigResult">
        <include refid="selectTDeptConfigVo"/>
        where dept_id = #{deptId}
    </select>

    
</mapper>