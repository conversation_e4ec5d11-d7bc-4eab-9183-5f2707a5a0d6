<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.stores.mapper.StoresMapper">
    
    <resultMap type="Mer" id="MerResult">
        <result property="id"    column="id"    />
        <result property="storesNo"    column="stores_no"    />
        <result property="storesName"    column="stores_name"    />
        <result property="merNo"    column="mer_no"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
    <sql id="selectStoresVo">
        select id, stores_no,stores_name,mer_no, dept_id,create_time, update_time
        from t_stores
    </sql>
    <sql id="ifDeptIdSql">
        <if test="deptId != null and deptId != ''">
            and (a.dept_id = #{deptId}
            or a.dept_id in (SELECT dept_id FROM sys_dept
            where FIND_IN_SET (#{deptId},ancestors)))
        </if>
    </sql>
    <!--设备列表-->
    <select id="selectList" resultType="com.centerm.basic.msgcenter.stores.domain.Stores">
        select a.id, a.mer_no,m.mer_name,a.stores_no, a.stores_name,a.create_time, a.update_time,
        b.dept_name,a.dept_id
        from t_stores a
        left join t_mer m on a.mer_no = m.mer_no
        left join sys_dept b on a.dept_id = b.dept_id
        where 1=1
        and a.stores_no not in(select tb.stores_no from t_mer_terminal_bind tb where tb.stores_no = a.stores_no and a.mer_no = tb.mer_no and tb.bind_status = '1')
        <if test="merNo != null and merNo != ''">
            and a.mer_no = #{merNo}
        </if>
        <include refid="ifDeptIdSql"/>
        order by a.create_time desc
    </select>

    <select id="selectByNo" resultType="com.centerm.basic.msgcenter.stores.domain.Stores">
        <include refid="selectStoresVo"/>
        where stores_no = #{storesNo}
    </select>

    <update id="updateByNo" parameterType="com.centerm.basic.msgcenter.stores.domain.Stores">
        update t_stores
        set stores_name = #{storesName}
        where stores_no = #{storesNo}
    </update>

</mapper>