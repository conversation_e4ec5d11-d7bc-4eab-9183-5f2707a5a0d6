<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.logReceive.mapper.LogReceiveMapper">
    
    <resultMap type="LogReceive" id="LogReceiveResult">
        <result property="id"    column="id"    />
        <result property="sn"    column="sn"    />
        <result property="createTime"    column="create_time"    />
        <result property="status"    column="status"    />
        <result property="deptId"    column="dept_id"    />
        <result property="message"    column="message"    />
        <result property="result"    column="result"    />
        <result property="reqNumber"    column="req_number"    />
    </resultMap>

	
	<sql id="selectLogMoneyVo">
        select id, sn, create_time, status, dept_id, message, result, req_number from t_log_receive
    </sql>

    <select id="selectList" resultType="com.centerm.basic.msgcenter.logReceive.domain.LogReceive">
        select a.* from t_log_receive a
        where 1=1
        <if test="sn != null and sn != ''">and a.sn = #{sn}</if>
        <if test="status != null and status != ''">and a.status = #{status}</if>
        <if test="startTime != null and startTime != ''">and a.create_time >= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and a.create_time <![CDATA[ < ]]> #{endTime}</if>
        <if test="deptId != null">and a.dept_id = #{deptId}</if>
        <if test="reqNumber != null and reqNumber != ''">and a.req_number = #{reqNumber}</if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by a.create_time desc
    </select>

    <select id="selectList_COUNT" resultType="long">
        select count(*) from t_log_receive a
        where 1=1
        <if test="sn != null and sn != ''">and a.sn = #{sn}</if>
        <if test="status != null and status != ''">and a.status = #{status}</if>
        <if test="startTime != null and startTime != ''">and a.create_time >= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and a.create_time <![CDATA[ < ]]> #{endTime}</if>
        <if test="deptId != null">and a.dept_id = #{deptId}</if>
        <if test="reqNumber != null and reqNumber != ''">and a.req_number = #{reqNumber}</if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectCountTotal" resultType="long">
        select count(*) from t_log_receive a
        where 1=1
        <if test="sn != null and sn != ''">and a.sn = #{sn}</if>
        <if test="status != null and status != ''">and a.status = #{status}</if>
        <if test="startTime != null and startTime != ''">and a.create_time >= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and a.create_time <![CDATA[ < ]]> #{endTime}</if>
        <if test="deptId != null">and a.dept_id = #{deptId}</if>
        <if test="reqNumber != null and reqNumber != ''">and a.req_number = #{reqNumber}</if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>


</mapper>