<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.logMoney.mapper.LogMoneyMapper">
    
    <resultMap type="LogMoney" id="LogMoneyResult">
        <result property="id"    column="id"    />
        <result property="sn"    column="sn"    />
        <result property="nonce"    column="nonce"    />
        <result property="paidTime"    column="paid_time"    />
        <result property="paidType"    column="paid_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="broadcastTime"    column="broadcast_time"    />
        <result property="channel"    column="channel"    />
        <result property="money"    column="money"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

	
	<sql id="selectLogMoneyVo">
        select id, sn, nonce, paid_time, create_time, broadcast_time, channel, money, status, remark from t_log_money
    </sql>
    <select id="summary" resultType="com.centerm.common.dto.transaction.LogMoneySummary">
        select a.moneySum,a.paidType, a.channel,t.channelName from
        (select SUM(m.money) as moneySum,m.paid_type as paidType, m.channel from t_log_money m where m.sn = #{sn}
          and <![CDATA[ m.paid_time >= CURRENT_DATE ]]> GROUP BY m.paid_type, m.channel order by m.paid_type,m.channel
        <!-- 数据范围过滤 -->
          ${params.dataScope}
          ) a
         left join (select dict_value,dict_label as channelName from sys_dict_data where dict_type = 'voice_channel') t
         on t.dict_value = a.channel;
    </select>
    <sql id="ifDeptIdSql">
        <if test="deptId != null and deptId != ''">
            and (a.dept_id = #{deptId}
            or a.dept_id in (SELECT dept_id FROM sys_dept
            where FIND_IN_SET (#{deptId},ancestors)))
        </if>
    </sql>

    <select id="selectList" resultType="com.centerm.basic.msgcenter.logMoney.domain.LogMoney">
        select a.* from t_log_money a
        where 1=1
        <if test="sn != null and sn != ''">and a.sn = #{sn}</if>
        <if test="status != null and status != ''">and a.status = #{status}</if>
        <if test="startTime != null and startTime != ''">and a.paid_time >= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and a.paid_time <![CDATA[ < ]]> #{endTime}</if>
        <include refid="ifDeptIdSql"/>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by a.create_time desc

    </select>

    <select id="selectList_COUNT" resultType="long">
        select count(*) from t_log_money a
        where 1=1

        <if test="sn != null and sn != ''">and a.sn = #{sn}</if>
        <if test="status != null and status != ''">and a.status = #{status}</if>
        <if test="startTime != null and startTime != ''">and a.create_time <![CDATA[ >= ]]> #{startTime}</if>
        <if test="endTime != null and endTime != ''">and a.create_time <![CDATA[ <= ]]> #{endTime}</if>
        <include refid="ifDeptIdSql"/>
        <!-- 数据范围过滤 -->
        ${params.dataScope}

    </select>

    <select id="selectCountTotal" resultType="long">
        select count(*) from t_log_money a
        where 1=1

        <if test="sn != null and sn != ''">and a.sn = #{sn}</if>
        <if test="status != null and status != ''">and a.status = #{status}</if>
        <if test="startTime != null and startTime != ''">and a.paid_time >= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and a.paid_time <![CDATA[ < ]]> #{endTime}</if>
        <if test="startTime = null or startTime = ''">
            <if test="curDate != null and curDate != ''">and a.paid_time >= #{curDate}</if>
        </if>
<!--        <if test="deptId != null">and a.dept_id = #{deptId}</if>-->
        <include refid="ifDeptIdSql"/>
        <!-- 数据范围过滤 -->
        ${params.dataScope}

    </select>


    <select id="selectListBySn" resultType="com.centerm.basic.msgcenter.logMoney.domain.LogMoney">
        select a.* from t_log_money a
        where  a.sn = #{sn}
        order by a.create_time desc
    </select>
</mapper>