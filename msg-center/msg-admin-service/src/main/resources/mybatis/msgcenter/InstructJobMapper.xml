<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.instructJob.mapper.InstructJobMapper">
    
    <resultMap type="InstructJob" id="InstructJobResult">
        <result property="id"    column="id"    />
        <result property="jobName"    column="job_name"    />
        <result property="command"    column="command"    />
        <result property="releaseStatus"    column="release_status"    />
        <result property="collection"    column="collection"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>
	
	<sql id="selectInstructJobVo">
        select id, job_name, command, release_status, collection, create_by, create_time, update_by, update_time, remark from t_instruct_job
    </sql>
    <sql id="ifDeptIdSql">
        <if test="deptId != null and deptId != ''">
            and (a.dept_id = #{deptId}
            or a.dept_id in (SELECT dept_id FROM sys_dept
            where FIND_IN_SET (#{deptId},ancestors)))
        </if>
    </sql>

    <select id="selectList" resultType="com.centerm.basic.msgcenter.instructJob.domain.InstructJob">
        select m.* from (select t.*,
        case
        when t.release_status_tmp != 2 then t.release_status_tmp
        when t.number = (t.success + t.failure + t.pause) then 4
        else t.release_status_tmp end as release_status from
        (select id, job_name, command, release_status AS release_status_tmp, collection,
               create_by, create_time, update_by, update_time, remark,
               (SELECT count(*) FROM t_instruct_task b WHERE b.JOB_ID = a.id ) AS number,
               (SELECT count(*) FROM t_instruct_task b WHERE b.JOB_ID = a.id and b.dl_flag = '4') AS success,
               (SELECT count(*) FROM t_instruct_task b WHERE b.JOB_ID = a.id and b.dl_flag = '3') AS failure,
               (SELECT count(*) FROM t_instruct_task b WHERE b.JOB_ID = a.id and b.dl_flag = '5') AS pause,
               (SELECT count(*) FROM t_instruct_task b WHERE b.JOB_ID = a.id and b.dl_flag = '1') AS pending,
               (SELECT count(*) FROM t_instruct_task b WHERE b.JOB_ID = a.id and b.dl_flag = '2') AS onGoing
        from t_instruct_job a
        where 1=1
            <if test="jobName != null and jobName != ''">
                and a.job_name like concat('%', #{jobName}, '%')
            </if>
            <include refid="ifDeptIdSql"/>
        ${params.dataScope}) t ) m
        order by m.create_time desc
    </select>
</mapper>