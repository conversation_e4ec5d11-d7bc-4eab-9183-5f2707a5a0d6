<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.basic.msgcenter.changeIns.mapper.ChangeInsJobMapper">

    <resultMap type="ChangeInsJob" id="ChangeInsJobResult">
        <result property="id" column="id"/>
        <result property="jobName" column="job_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="groupId" column="group_id"/>
        <result property="recordCreateTime" column="record_create_time"/>
        <result property="recordUpdateTime" column="record_update_time"/>
        <result property="deptName" column="dept_name"/>
        <result property="groupName" column="group_name"/>
    </resultMap>

    <select id="selectJobList" parameterType="ChangeInsJob" resultMap="ChangeInsJobResult">
        select j.id,
        j.job_name,
        j.dept_id,
        j.group_id,
        j.record_create_time,
        j.record_update_time,
        b.dept_name,
        d.group_name
        from coms_change_ins_job j
        LEFT JOIN sys_dept b ON b.dept_id = j.dept_id
        LEFT JOIN t_group d ON d.id = j.group_id
        <where>
            <if test="jobName != null  and jobName != '' ">and j.job_name like concat('%',#{jobName},"%")</if>
        </where>
        order by j.id desc
    </select>


</mapper>