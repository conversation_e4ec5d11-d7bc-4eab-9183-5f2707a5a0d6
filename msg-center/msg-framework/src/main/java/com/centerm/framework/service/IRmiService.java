package com.centerm.framework.service;

import com.centerm.common.dto.Instruct;
import com.centerm.common.dto.params.ParamsDTO;

/**
 *
 * @program: msg-center
 * @description: RMI service 用于管理平台调用 api接口
 * @author: <PERSON>
 * @create: 2019/6/19 15:53
 *
 **/
public interface IRmiService {
	/**
	 * 参数下发
	 * @param paramsDTO
	 * @return
	 */
	boolean paramPub(ParamsDTO paramsDTO);
	/**
	 * 远程运维
	 * @param instruct
	 * @return
	 */
	boolean instruct(Instruct instruct);
}
