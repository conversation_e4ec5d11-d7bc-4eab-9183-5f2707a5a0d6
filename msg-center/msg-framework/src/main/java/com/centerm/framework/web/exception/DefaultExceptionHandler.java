package com.centerm.framework.web.exception;

import com.centerm.common.base.ResponseResult;
import com.centerm.common.constant.ErrorConstants;
import com.centerm.common.exception.BusinessException;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.ServletUtils;
import com.centerm.common.utils.StringUtils;
import com.centerm.framework.util.PermissionUtils;
import org.apache.shiro.ShiroException;
import org.apache.shiro.authz.AuthorizationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataAccessException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Arrays;
import java.util.List;

/**
 * 自定义异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@Order(-1)
public class DefaultExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(DefaultExceptionHandler.class);

    /**
     * 业务异常
     **/
    @ExceptionHandler(BusinessException.class)
    public ResponseResult handleBusinessException(BusinessException e) {
        StringBuilder errorMsg = new StringBuilder();
        errorMsg.append("业务异常,错误码=【{}】");
        if (!CommonUtils.isEmpty(e.getArgs())) {
            errorMsg.append(StringUtils.format("错误参数=【{}】", e.getArgs()));
        }
        log.error(errorMsg.toString(), e.getCode(), e.getArgs());
        return ResponseResult.fail(e.getCode(), e.getArgs());
    }

    /**
     * 校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseResult handleNotValidException(MethodArgumentNotValidException e) {
        return validException(e.getBindingResult().getFieldErrors());
    }

    @ExceptionHandler(BindException.class)
    public ResponseResult handleValidationException(BindException e) {
        return validException(e.getBindingResult().getFieldErrors());
    }

    private static ResponseResult validException(List<FieldError> errors) {
        StringBuffer errorMsg = new StringBuffer();
        errors.stream().forEach(x ->
                errorMsg.append(x.getField() + x.getDefaultMessage()).append(";"));
        log.error("\n参数校验不通过【{}】", errorMsg.toString());
        return ResponseResult.fail("request.valid.fail", errorMsg.toString());
    }

    /**
     * 权限校验失败
     */
    @ExceptionHandler(AuthorizationException.class)
    public ResponseResult handleAuthorizationException(AuthorizationException e) {
        log.error("权限校验不通过：", e.getMessage());
        String permission = StringUtils.substringBetween(e.getMessage(), "[", "]");
        String code = PermissionUtils.getCode(permission);
        return ResponseResult.fail(code, permission);
    }

    @ExceptionHandler(ShiroException.class)
    public ResponseResult handleAuthenticationException(ShiroException e) {
        log.error("登陆权限校验失败：", e.getMessage());
        if (e.getCause() instanceof BusinessException) {
            return handleBusinessException((BusinessException) e.getCause());
        }
        return ResponseResult.fail(ErrorConstants.RESPONSE_LOGIN);
    }

    /**
     * 数据库操作异常
     */
    @ExceptionHandler(DataAccessException.class)
    public ResponseResult handleException(DataAccessException e) {
        log.error("数据库操作错误：", e);
        return ResponseResult.fail(ErrorConstants.DATABASE_EXCEPTION);
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler({HttpRequestMethodNotSupportedException.class})
    public ResponseResult handleException(HttpRequestMethodNotSupportedException e) {
        log.error("请求【{}】不支持[{}]，只支持【{}】", ServletUtils.getRequest().getRequestURI()
                , e.getMethod(), Arrays.toString(e.getSupportedMethods()));
        return ResponseResult.fail("request.not.support", ServletUtils.getRequest().getRequestURI()
                , e.getMethod(), Arrays.toString(e.getSupportedMethods()));
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseResult notFount(RuntimeException e) {
        log.error("运行时异常：", e);
        return ResponseResult.fail("response.runException");
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseResult handleException(Exception e) {
        log.error("系统异常：", e);
        return ResponseResult.fail("response.exception");
    }


}
