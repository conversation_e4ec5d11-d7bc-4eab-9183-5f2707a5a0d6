package com.centerm.framework.shiro.realm;

import com.centerm.common.constant.Constants;
import com.centerm.common.exception.BusinessException;
import com.centerm.common.utils.MessageUtils;
import com.centerm.framework.manager.AsyncManager;
import com.centerm.framework.manager.factory.AsyncFactory;
import com.centerm.framework.shiro.token.JwtToken;
import com.centerm.framework.util.JwtUtils;
import com.centerm.framework.util.ShiroUtils;
import com.centerm.system.domain.SysUser;
import com.centerm.system.service.ISysMenuService;
import com.centerm.system.service.ISysRoleService;
import com.centerm.system.service.ISysUserService;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.Set;

/**
 * jwt用户登录权限  TODO 缓存的实现
 *
 * <AUTHOR>
 * @date 2018/11/15 17:20
 **/
public class JwtUserRealm extends AuthorizingRealm {


    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysUserService userService;

    @Override
    public boolean supports(AuthenticationToken token) {
        //表示此Realm只支持JwtToken类型
        return false;
        //return token instanceof JwtToken;
    }

    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        SysUser user = ShiroUtils.getSysUser();
        // 角色列表
        Set<String> roles = new HashSet<String>();
        // 功能列表
        Set<String> menus = new HashSet<String>();
        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        roles = roleService.selectRoleKeys(user.getUserId());
        menus = menuService.selectPermsByUserId(user.getUserId());
        // 角色加入AuthorizationInfo认证对象
        info.setRoles(roles);
        // 权限加入AuthorizationInfo认证对象
        info.setStringPermissions(menus);
        return info;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
        String token = (String) authenticationToken.getCredentials();
        String username = JwtUtils.getUsername(token);
        // 查询用户信息
        SysUser user = userService.selectUserByLoginName(username);
        if (user == null) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.not.exists")));
            throw new BusinessException("user.not.exists");
        }
        if (!JwtUtils.verify(token, username, user.getPassword())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.unauthorized")));
            throw new BusinessException("user.token.error");
        }
        SimpleAuthenticationInfo info = new SimpleAuthenticationInfo(user, token, getName());
        return info;
    }
}
