package com.centerm.common.dto.psbc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/9/6 17:30
 **/
@Data
public class PsbcBroadcastRequest {

    @ApiModelProperty("访问密钥ID")
    private String access_key_id;

    @ApiModelProperty("请求流水编号")
    private String request_id;

    @ApiModelProperty("设备编号")
    private String device_id;

    @ApiModelProperty("请求消息")
    private String request_data;

    @ApiModelProperty("推送语音模板")
    private String push_template;

    @ApiModelProperty("签名唯一随机数")
    private String nonce;

    @ApiModelProperty("签名结果串")
    private String sign;

}
