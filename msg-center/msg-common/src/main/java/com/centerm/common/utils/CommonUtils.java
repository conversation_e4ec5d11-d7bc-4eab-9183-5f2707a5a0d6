package com.centerm.common.utils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 系统常用工具类
 *
 * <AUTHOR>
 * @date 2018/10/16 13:52
 **/
public class CommonUtils {
    /**
     *  创建UUID zc
     * @return
     */
    public static String createUUID(){
        return UUID.randomUUID().toString().replace("-","");
    }
    /**
     * 判断对象是否为空
     *
     * @param pObj
     * @return
     */
    public static boolean isEmpty(Object pObj) {
        if (pObj == null) {
            return true;
        }
        if (pObj == "") {
            return true;
        }
        if (pObj instanceof String) {
            if (((String) pObj).trim().length() == 0) {
                return true;
            }
        } else if (pObj instanceof Collection) {
            if (((Collection) pObj).size() == 0) {
                return true;
            }
        } else if ((pObj instanceof Map) && ((Map) pObj).size() == 0) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否为数字
     *
     * @param s
     * @return
     */
    public static boolean isNumber(String s) {
        try {
            Long.parseLong(s);
        } catch (Exception ex) {
            return false;
        }
        return true;
    }

    /**
     * 计算某个字符在字符串中出现的次数
     */
    public static int count(String s, char c) {
        int count = 0;
        byte[] sBytes = s.getBytes();
        for (int i = 0; i < sBytes.length; i++) {
            if (sBytes[i] == c) {
                count++;
            }
        }
        return count;
    }

    /**
     * 匹配正则表达式
     */
    public static boolean match(String value, String regx) {
        Pattern pattern = Pattern.compile(regx);
        return pattern.matcher(value).matches();
    }

    /**
     * 获取uuid
     *
     * @return
     */
    public static synchronized String uuid() {
        String uuid = UUID.randomUUID().toString();
        return uuid.replace("-", "");
    }

    public static synchronized String getAppId() {
        String CurrentTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        return CurrentTime + getRandom(6);
    }

    public static String getRandom(int length) {
        // 随机产生的范围字符串
        String randString = "123456789";
        Random random = new Random();
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < length; i++) {
            String randStr = String.valueOf(randString.charAt(random.nextInt(randString.length())));
            buffer.append(randStr);
        }
        return buffer.toString();
    }


    /**
     * 获取对象类型
     *
     * @param obj
     * @return
     */
    public static int getType(Object obj) {
        if (obj == null) {
            obj = "";
        }
        if (obj instanceof String) {
            return Types.VARCHAR;
        } else if (obj instanceof Integer) {
            return Types.INTEGER;
        } else if (obj instanceof BigDecimal) {
            return Types.BIGINT;
        } else if (obj instanceof Date) {
            return Types.DATE;
        } else if (obj instanceof Double) {
            return Types.DOUBLE;
        } else if (obj instanceof Float) {
            return Types.FLOAT;
        } else if (obj instanceof Long) {
            return Types.BIGINT;
        } else {
            return -999;
        }
    }

    /**
     * 根据数据数组获取类型数组
     *
     * @param obj
     * @return
     */
    public static int[] getObjTypes(Object[] obj) {
        List list = new ArrayList();
        for (int i = 0; i < obj.length; i++) {
            list.add(getType(obj[i]));
        }
        return objectToIntArr(list.toArray());
    }

    /**
     * 对象数组转化成Int数组
     *
     * @param type
     * @return
     */
    public static int[] objectToIntArr(Object[] type) {
        int[] tmp_type = new int[type.length];
        for (int i = 0; i < type.length; i++) {
            tmp_type[i] = (Integer) type[i];
        }
        return tmp_type;
    }

    /**
     * 将对象的string属性取trim
     *
     * @param obj
     */
    public static void trimObject(Object obj) {
        if (obj instanceof Map) {
            Map lvf = (Map) obj;
            Iterator keyIte = lvf.keySet().iterator();
            for (; keyIte.hasNext(); ) {
                String key = keyIte.next().toString();
                Object value = lvf.get(key);
                if (value instanceof String) {
                    value = value.toString().trim();
                    lvf.put(key, value);
                }
            }
            return;
        }
        Class className = obj.getClass();
        Field[] fileds = className.getDeclaredFields();
        for (int i = 0; i < fileds.length; i++) {
            Field filed = fileds[i];
            if (filed.getType() == String.class) {
                String fieldName = filed.getName();
                try {
                    Method getMethod = className.getMethod("get"
                            + fieldName.substring(0, 1).toUpperCase()
                            + fieldName.substring(1));
                    Method setMethod = className.getMethod("set"
                            + fieldName.substring(0, 1).toUpperCase()
                            + fieldName.substring(1), String.class);
                    Object value = getMethod.invoke(obj);
                    if (value != null) {
                        setMethod.invoke(obj, value.toString().trim());
                    }
                } catch (SecurityException e) {
                } catch (NoSuchMethodException e) {
                } catch (IllegalArgumentException e) {
                } catch (IllegalAccessException e) {
                } catch (InvocationTargetException e) {
                }
            }
        }
    }

    /**
     * 获取对象的数据，以|拼成字符串
     *
     * @param obj
     * @return
     */
    public static String getObjct(Object obj) {
        String result = "";
        Class className = obj.getClass();
        Field[] fileds = className.getDeclaredFields();
        for (int i = 0; i < fileds.length; i++) {
            Field filed = fileds[i];
            String fieldName = filed.getName();
            try {
                Method method = className.getMethod("get"
                        + fieldName.substring(0, 1).toUpperCase()
                        + fieldName.substring(1));
                if (method != null) {
                    Object filedValue = method.invoke(obj);
                    if (filedValue instanceof String) {
                        result += fieldName + ":"
                                + String.valueOf(filedValue).trim() + "|";
                    } else {
                        result += fieldName + ":" + filedValue + "|";
                    }

                }
            } catch (SecurityException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                continue;
            } catch (IllegalArgumentException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            }
        }
        if (result != null && result.length() != 0) {
            result = result.length() > 1024 ? result.substring(0, 1024)
                    : result;
        }
        return result;
    }

    /**
     * 比较2个对象的差异，以|拼成字符串,最大长度1024
     *
     * @param old_obj
     * @param new_obj
     * @return
     */
    public static String getCompareObject(Object old_obj, Object new_obj) {
        // 比较结果
        String result = "";
        Class objClass = new_obj.getClass();
        // 如果两个对象不同类,不比较,返回
        if (objClass != old_obj.getClass()) {
            return null;
        }
        // 取声明的属性列表
        Field[] fileds = objClass.getDeclaredFields();
        for (int i = 0; i < fileds.length; i++) {
            Field filed = fileds[i];
            String fieldName = filed.getName();
            try {
                // 取属性的get方法
                Method method = objClass.getMethod("get"
                        + fieldName.substring(0, 1).toUpperCase()
                        + fieldName.substring(1));
                if (method != null) {
                    // 取新旧属性值
                    Object oldValue = method.invoke(old_obj);
                    Object newValue = method.invoke(new_obj);
                    if (oldValue != newValue
                            && ((oldValue == null && newValue != null) || !oldValue
                            .equals(newValue))) {
                        // 如果是时间
                        if (oldValue instanceof Date
                                || newValue instanceof Date) {
                            SimpleDateFormat sdf = new SimpleDateFormat(
                                    "yyyy-MM-dd");
                            String oldValueStr = oldValue == null ? "null"
                                    : sdf.format((Date) oldValue);
                            String newValueStr = newValue == null ? "null"
                                    : sdf.format((Date) newValue);
                            result += fieldName + ":" + oldValueStr + "-"
                                    + newValueStr + "|";
                        } else if (oldValue instanceof String
                                || newValue instanceof String) {
                            if (!String.valueOf(oldValue).trim().equals(
                                    String.valueOf(newValue).trim())) {
                                result += fieldName + ":"
                                        + String.valueOf(oldValue).trim() + "-"
                                        + String.valueOf(newValue).trim() + "|";
                            }
                        } else {
                            result += fieldName + ":" + oldValue + "-"
                                    + newValue + "|";
                        }
                    }
                }
            } catch (SecurityException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                continue;
            } catch (IllegalArgumentException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            }
        }
        if (result != null && result.length() != 0) {
            result = result.length() > 1024 ? result.substring(0, 1024)
                    : result;
        }
        return result;
    }

    /**
     * object转整型
     *
     * @param obj
     * @param defValue
     * @return
     */
    public static int getInt(Object obj, int defValue) {

        try {
            if (obj instanceof Integer) {
                defValue = (Integer) obj;
            } else {
                defValue = Integer.valueOf(obj + "");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return defValue;
    }

    /**
     * object转字符串
     *
     * @param obj
     * @return
     */
    public static String getString(Object obj) {
        String defValue = "";
        try {
            if (obj == null) {
                defValue = "";
            } else {
                defValue = obj.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return defValue;
    }

    /**
     * 比较版本号的大小,前者大则返回一个正数,后者大返回一个负数,相等则返回0
     *
     * @param version1
     * @param version2
     * @return
     * <AUTHOR>
     */
    public static int compareVersion(String version1, String version2) throws Exception {
        if (version1 == null || version2 == null) {
            throw new Exception();
        }
        //注意此处为正则匹配，不能用.；
        String[] versionArray1 = version1.split("\\.");
        String[] versionArray2 = version2.split("\\.");
        int idx = 0;
        //取最小长度值
        int minLength = Math.min(versionArray1.length, versionArray2.length);
        int diff = 0;
        //先比较长度
        while (idx < minLength
                && (diff = versionArray1[idx].length() - versionArray2[idx].length()) == 0
                //再比较字符
                && (diff = versionArray1[idx].compareTo(versionArray2[idx])) == 0) {
            ++idx;
        }
        //如果已经分出大小，则直接返回，如果未分出大小，则再比较位数，有子版本的为大；
        diff = (diff != 0) ? diff : versionArray1.length - versionArray2.length;
        return diff;
    }

    /**
     * 数组转set,去重复
     *
     * @param tArray 数组
     * @param <T>    范型
     * @return set
     */
    public static <T extends Object> Set<T> Array2Set(T[] tArray) {
        return new HashSet<T>(Arrays.asList(tArray));
    }
    /**
     * 身份证号替换，保留前四位和后四位
     *
     * 如果身份证号为空 或者 null ,返回null ；否则，返回替换后的字符串；
     * @param idCard 身份证号
     * @return
     */
    public static String idCardReplaceWithStar(String idCard) {

        if (idCard == null || idCard.isEmpty()) {
            return null;
        } else {
            return replaceAction(idCard, "(?<=\\d{4})\\d(?=\\d{4})");
        }
    }

    /**
     * 银行卡替换，保留后四位
     *
     * 如果银行卡号为空 或者 null ,返回null ；否则，返回替换后的字符串；
     * @param bankCard 银行卡号
     * @return
     */
    public static String bankCardReplaceWithStar(String bankCard) {

        if (bankCard.isEmpty() || bankCard == null) {
            return null;
        } else {
            return replaceAction(bankCard, "(?<=\\d{0})\\d(?=\\d{4})");
        }
    }

    /**
     * 手机号替换，保留后四位
     *
     * 如果手机号为空 或者 null ,返回null ；否则，返回替换后的字符串；
     *
     * @param mobileNo 手机号
     * @return
     */
    public static String mobileNoReplaceWithStar(String mobileNo) {

        if (mobileNo == null || mobileNo.isEmpty()) {
            return null;
        } else {
            return replaceAction(mobileNo, "(?<=\\d{3})\\d(?=\\d{4})");
        }
    }

    /**
     * 对String字符串进行替换
     * 实际替换动作
     *
     * @param  str 字符串
     * @param regular  正则
     * @return
     */
    private static String replaceAction(String str, String regular) {
        return str.replaceAll(regular, "*");
    }
}
