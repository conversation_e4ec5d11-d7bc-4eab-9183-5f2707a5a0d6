package com.centerm.common.dto.common;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/6/20 11:18
 **/
public enum CommonResponseEnums {
	BROADCAST_SUCCESS("0000","Broadcast Successfully"),
	PARAM_FAILED("0001","Invalid Parameter"),
	SIGN_FAILED("0002","Sign Verification Failed"),
	UPDATE_SUCCESS("0003", "Update Successfully"),
	DEVICE_OFFLINE("1001","Device Offline"),
	DEVICE_INEXISTENCE("1002","Device Does Not Exist"),
	KEY_FAILED("1003","Key Failed"),
	BROADCAST_FAILED("1004","Broadcast Failed"),
    DEVICE_STATUE_ERROR("1005","Device Status Error"),
	UPDATE_FAILED("1006", "Update Failed"),

	//云打印模块
    PRINT_TEMPLATE_NULL("2001", "打印模版不能为空"),
    PRINT_CONTENT_ERROR("2002", "打印数据生成失败，请检查打印数据是否完整！");

	private String code;
	private String msg;

	public String getCode() {
		return code;
	}
	public String getMsg() {
		return msg;
	}

	CommonResponseEnums(String code, String msg){
		this.code = code;
		this.msg = msg;
	}
}