package com.centerm.common.utils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;

/**
 * jaxb工具用户解析和封装xml消息体
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2016/12/15
 */
public class JaxbUtil
{
    
    /**
     * JavaBean转换成xml 默认编码UTF-8
     * 
     * @param obj
     * @param
     * @return
     */
    public static String convertToXml(Object obj)
    {
        return convertToXml(obj, "UTF-8");
    }
    
    /**
     * JavaBean转换成xml
     * 
     * @param obj
     * @param encoding
     * @return
     */
    public static String convertToXml(Object obj, String encoding)
    {
        String result = null;
        try
        {
            JAXBContext context = JAXBContext.newInstance(obj.getClass());
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, false);
            marshaller.setProperty(Marshaller.JAXB_ENCODING, encoding);
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, true);
            
            StringWriter writer = new StringWriter();
            marshaller.marshal(obj, writer);
            result = writer.toString();

        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return result;
    }
    
    /**
     * xml转换成JavaBean
     * 
     * @param xml
     * @param c
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T converyToJavaBean(String xml, Class<T> c)
    {
        T t = null;
        try
        {
            JAXBContext context = JAXBContext.newInstance(c);
            Unmarshaller unmarshaller = context.createUnmarshaller();
            t = (T)unmarshaller.unmarshal(new StringReader(xml));
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return t;
    }
    
    public static <T> String toXml(T xmlBean,Class<T> xmlClass, String encode) throws JAXBException{
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        JAXBContext jc = JAXBContext.newInstance(xmlClass);
        Marshaller marshaller = jc.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_ENCODING, encode);
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, false);
        marshaller.marshal(xmlBean, output);
        String xml = output.toString();
        try {
            output.close();
        } catch (IOException e) {
            e.printStackTrace();
            return xml;
        }
        return xml;
    }

}