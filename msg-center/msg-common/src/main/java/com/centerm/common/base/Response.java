package com.centerm.common.base;

import com.centerm.common.utils.MessageUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class Response<T> implements Serializable {
	private static String SUCCESS_CODE = "0000";
	
	@ApiModelProperty("返回码  0000 为交易成功")
	private String respCode;
	@ApiModelProperty("返回信息")
	private String respMsg;

	private T data;

	public Response(String successCode, String fetchSuccessfully, T data) {
		this.respCode = successCode;
		this.respMsg = fetchSuccessfully;
		this.data = data;
	}

	public static <T> Response<T> success(T data) {
		return new Response<T>(SUCCESS_CODE, "Fetch Successfully", data);
	}

}
