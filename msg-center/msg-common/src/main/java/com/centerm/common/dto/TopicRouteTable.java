package com.centerm.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @program: msg-push
 * @description: 
 * @author: <PERSON> pojo自动生成
 * @create: 2019-02-21 16:47
 **/
@Data
@ApiModel
public class TopicRouteTable extends BaseDTO{
    @ApiModelProperty(value = "目标Topic列表，即从SrcTopic订阅消息的Topic列表。", required = true)
	private List<String> dstTopics;
    @ApiModelProperty(value = "源Topic，即被订阅的Topic。", required = true)
	private String srcTopic;
}
