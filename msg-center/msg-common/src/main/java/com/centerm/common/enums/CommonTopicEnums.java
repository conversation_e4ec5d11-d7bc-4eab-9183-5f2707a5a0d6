package com.centerm.common.enums;

public enum CommonTopicEnums {

    MSG_BROADCAST("/${productKey}/${sn}/message", "消息推送",0),
    TRANSACTION_BROADCAST("/${productKey}/${sn}/message", "交易",1),
    VOICE_BROADCAST("/${productKey}/${sn}/message", "播报",2),
    DADALARM_BROADCAST("/${productKey}/${sn}/message", "告警",3),
    INSTRUCT("/${productKey}/${sn}/message", "远程运维",4),
    ADVERT_BROADCAST("/${productKey}/${sn}/message", "广告播报",2),
    PARAMS_UPGRADE("/${productKey}/${sn}/message", "参数下发",5),
    TEMPLATE("/${productKey}/${sn}/message", "模板下发",11),
    MSG_RECEIVED("$queue//${productKey}/cmn/received","消息回执",6),
    QRCODE_UPGRADE("/${productKey}/${sn}/message","二维码升级",7),
    FIRMWARE_UPGRADE("/${productKey}/cmn/upgrade","远程运维下发",8),
    QRCODE_UPDATE("/${productKey}/${sn}/message","二维码更新",9),
    API_COMMAND("/${productKey}/${sn}/message","api指令",99),
    ;


    private String topicName;
    private String desc;
    private Integer type;

    CommonTopicEnums(String topicName, String desc,Integer type) {
        this.topicName = topicName;
        this.desc = desc;
        this.type = type;
    }

    public String getTopicName() {
        return topicName;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getType() {
        return type;
    }
    public static CommonTopicEnums getTopicEnum(Integer type){
        switch (type){
            case 1:
                return CommonTopicEnums.TRANSACTION_BROADCAST;
            case 2:
                return CommonTopicEnums.VOICE_BROADCAST;
            case 3:
                return CommonTopicEnums.DADALARM_BROADCAST;
            case 4:
                return CommonTopicEnums.INSTRUCT;
            case 5:
                return CommonTopicEnums.PARAMS_UPGRADE;
            case 6:
                return CommonTopicEnums.MSG_RECEIVED;
            case 7:
                return CommonTopicEnums.QRCODE_UPGRADE;
            case 8:
                return CommonTopicEnums.FIRMWARE_UPGRADE;
            case 9:
                return CommonTopicEnums.QRCODE_UPDATE;
            case 11:
                return CommonTopicEnums.TEMPLATE;
            default:
                return null;
        }
    }
}