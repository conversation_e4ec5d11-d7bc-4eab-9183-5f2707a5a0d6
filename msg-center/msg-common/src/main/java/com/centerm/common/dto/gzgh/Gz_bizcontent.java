package com.centerm.common.dto.gzgh;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class Gz_bizcontent {
    @ApiModelProperty("消息ID号")
    private String msgid;

    @ApiModelProperty("产品KEY")
    private String productKey;

    @ApiModelProperty("设备名称")
    private String deviceSn;

    @ApiModelProperty("收款方式")
    private String broadcastType;

    @ApiModelProperty("金额（单位元，两位小数）")
    private String money;

    @ApiModelProperty("时间戳")
    private String timestap;

}
