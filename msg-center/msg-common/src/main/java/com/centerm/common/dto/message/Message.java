package com.centerm.common.dto.message;

import com.centerm.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/3/31 9:43
 **/
@Data
@ApiModel
public class Message extends BaseDTO {

    /*@ApiModelProperty(value = "消息类型", required = true)
    private Integer type;*/

    @ApiModelProperty(value = "模板编号", required = false)
    private Integer templetId;

    //@ApiModelProperty(value = "支付金额", required = false)
    //private String money; //todo 金额类型

    @ApiModelProperty(value = "文本内容", required = false)
    private String content;

    //@ApiModelProperty(value = "指令编号", required = false)
    //private String command;

    @ApiModelProperty(value = "终端sn号", required = false)
    private String sn;
}
