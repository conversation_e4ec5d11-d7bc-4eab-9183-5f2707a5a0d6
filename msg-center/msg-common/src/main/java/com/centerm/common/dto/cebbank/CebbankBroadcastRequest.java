package com.centerm.common.dto.cebbank;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: msg-center
 * @description: 光大播报请求
 * @author: <PERSON>
 * @create: 2019/7/18 14:00
 **/
@Data
public class CebbankBroadcastRequest {
	@ApiModelProperty(value = "平台分配给应用方唯一编号",required = true)
	private String appId;
	@ApiModelProperty(value = "消息ID",required = true)
	private String msgId;
	@ApiModelProperty(value = "设备SN号",required = true)
	private String sn;
	@ApiModelProperty(value = "威富通单号",required = true)
	private String transactionId;
	@ApiModelProperty(value = "支付总金额",required = true)
	private long totalFee;
	@ApiModelProperty(value = "播报内容 如： 收款成功，0.01 元",required = true)
	private String content;
	@ApiModelProperty(value = "附加参数 有值时格式为 JSON 字 符串",required = false)
	private String attach;
	@ApiModelProperty(value = "语言类型 默认缺省值:zh",required = false)
	private String lang;
	@ApiModelProperty(value = "随机数",required = true)
	private String nonce;
	@ApiModelProperty(value = "时间戳 请求时间戳,格式： yyyyMMddHHmmss",required = true)
	private String timestamp;
	@ApiModelProperty(value = "签名",required = true)
	private String sign;
}
