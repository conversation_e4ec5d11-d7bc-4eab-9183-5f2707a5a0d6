package com.centerm.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: basic
 * @description: 指令回执
 * @author: <PERSON>
 * @create: 2019-03-02 15:29
 **/
@Data
@ApiModel
public class InstructRecived extends BaseDTO{
    @ApiModelProperty(value = "消息ID", required = true)
    private String msgId;
    @ApiModelProperty(value = "设备sn号", required = true)
    private String sn;
    @ApiModelProperty(value = "执行状态",required = true)
    private String status;
    @ApiModelProperty(value = "指令执行失败的原因", required = false)
    private String reason;
}
