package com.centerm.common.dto.psbc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/9/6 17:30
 **/
@Data
public class PsbcBroadcastStatusResponse {
	@ApiModelProperty("返回响应码")
	private String code;

	@ApiModelProperty("结果描述")
	private String msg_desc;

	@ApiModelProperty("签名唯一随机数")
	private String nonce;

	@ApiModelProperty("签名结果串")
	private String sign;

	@ApiModelProperty("播报列表")
	private List<BroadcastListForBroadcastStatus> broadcast_list;

}
