package com.centerm.common.utils.xmlUtils;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "item")
@Data
public class Item {

	@XmlAttribute(name = "type",required=true)
	String type;
	@XmlAttribute(name = "version",required=true)
    String version;
	@XmlAttribute(name = "filename",required=true)
    String filename;
	@XmlAttribute(name = "md5",required=true)
    String md5;
}
