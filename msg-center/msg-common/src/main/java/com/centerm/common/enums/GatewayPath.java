package com.centerm.common.enums;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/3/26 14:35
 **/
public enum GatewayPath {

    UPGRADE("/v1/qrcode/upgrade","语音播报接口"),
    INSTRUCT("/v1/command/instruct","远程运维接口"),
    FILE_PATH("/v1/file/?filepath=","文件下载"),
    FILE_PATH2("/download/","文件下载2"),
    PARAMS_UPDATE("/v1/device/paramPub","参数下发");

    private String url;
    private String desc;

    public String getUrl() {
        return url;
    }

    public String getDesc() {
        return desc;
    }

    GatewayPath(String url, String desc) {
        this.url = url;
        this.desc = desc;
    }}
