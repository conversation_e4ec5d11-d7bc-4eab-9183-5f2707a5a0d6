package com.centerm.common.dto.common;

import com.centerm.common.utils.MessageUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GateResponse {
    @ApiModelProperty("返回码  0000 为交易成功")
    private String respCode;
    @ApiModelProperty("返回信息")
    private String respMsg;

    @ApiModelProperty("时间戳")
    private Long timestamp;

    private String sign;

    private String data;

    public static GateResponse fail(String code, Object... args) {
        return new GateResponse(code, MessageUtils.message(code, args));
    }

    public static GateResponse fail1(String code, String respMsg) {
        return new GateResponse(code, respMsg);
    }

    public static GateResponse success() {
        return new GateResponse(CommonResponseEnums.UPDATE_SUCCESS.getCode(), CommonResponseEnums.UPDATE_SUCCESS.getMsg());
    }

    public static GateResponse success(String code, String msg) {
        return new GateResponse(CommonResponseEnums.UPDATE_SUCCESS.getCode(), msg);
    }

    public static GateResponse success(String data) {
        return new GateResponse(CommonResponseEnums.UPDATE_SUCCESS.getCode(), CommonResponseEnums.UPDATE_SUCCESS.getMsg(), data);
    }

    public GateResponse(String respCode, String respMsg) {
        this.respCode = respCode;
        this.respMsg = respMsg;
        this.timestamp = System.currentTimeMillis();
    }

    public GateResponse(String respCode, String respMsg, String data) {
        this.respCode = respCode;
        this.respMsg = respMsg;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
    }

    public GateResponse() {
    }
}
