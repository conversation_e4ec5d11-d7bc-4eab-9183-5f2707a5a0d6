package com.centerm.common.dto.ynnx;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class YnnxConfigRequest {

    @ApiModelProperty(value = "机构标识")
    private String appkey;

    @ApiModelProperty(value = "配置的模板名称")
    private String config_name;

    @ApiModelProperty(value = "模板内容")
    private String config_content;

    @ApiModelProperty(value = "设置类型( 1:开机模板 2:关机模板 3:广告模板 4:播报模板)")
    private Integer config_type;

    @ApiModelProperty(value = "生效范围。(1:企业全局生效  2：部分机构生效 3：部分设备生效)")
    private Integer config_scope;

    @ApiModelProperty(value = "生效的机构编号/设备编号列表。使用”,”（半角英文逗号）分隔")
    private String config_target;

    @ApiModelProperty(value = "播报规则")
    private Integer config_rules;

    @ApiModelProperty(value = "播报规则设置值，当config_rules为2或3时有效。 指定广告播报的间隔时间或间隔次数")
    private String config_value;

    @ApiModelProperty(value = "推送的时间")
    private String pushTime;

    @ApiModelProperty(value = "时间戳")
    private String timestamp;

    @ApiModelProperty(value = "随机数")
    private String nonce;

    @ApiModelProperty(value = "签名")
    private String sign;

}
