package com.centerm.common.utils.file;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;


public class ZipUtil {

	/**
	 * 对压缩文件进行解压处理
	 * 
	 * @param zipPath  待解压的压缩包路径，例如压缩包G:/abc.zip
	 * @param unzipDir 所有解压出来的文件的存放路径，例如G:/bde
	 */
	public static List<String> unzip(String zipPath, String unzipDir) {
		if (zipPath == null || "".equals(zipPath) || unzipDir == null || "".equals(unzipDir)) {
			return null;
		}
		File unzipDirFile = new File(unzipDir);
		if(!unzipDirFile.exists() && !unzipDirFile.isDirectory()){
			unzipDirFile.mkdir();
		}
		File file = new File(zipPath);
		if (file == null || !file.exists() || !file.isFile() || !file.canRead()) {
			return null;
		}
		ZipFile zipFile = null;
		try {
			List<String> list = new ArrayList<String>();
			zipFile = new ZipFile(zipPath);
			Enumeration<?> enumeration = zipFile.entries();
			while (enumeration.hasMoreElements()) {
				ZipEntry entry = (ZipEntry) enumeration.nextElement();
				String filepath = unzipDir + "/" + entry.getName();
				boolean result = writeToFile(zipFile, entry, new File(filepath));
				if (result == false) {
					return null;
				}
				list.add(filepath);
			}
			zipFile.close();
			return list;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (null != zipFile) {
					zipFile.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	private static boolean writeToFile(ZipFile zip, ZipEntry entry, File toFile) {
		if (zip == null || entry == null || toFile == null) {
			return false;
		}
		InputStream is = null;
		FileOutputStream fos = null;
		try {
			if (!entry.isDirectory()) {
				if (!toFile.exists()) {
					toFile.getParentFile().mkdirs();
					toFile.createNewFile();
				}
				is = zip.getInputStream(entry);
				fos = new FileOutputStream(toFile);
				int len = 0;
				long size = 0;
				byte[] buffer = new byte[1024];
				while ((len = is.read(buffer)) != -1) {
					fos.write(buffer, 0, len);
					size += len;
				}
				fos.flush();
				fos.close();
				is.close();
				if (entry.getSize() == size) {
					return true;
				}
			} else {
				if (!toFile.exists()) {
					return toFile.mkdirs();
				}
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (null != is) {
					is.close();
				}
				if (null != fos) {
					fos.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return false;
	}
}
