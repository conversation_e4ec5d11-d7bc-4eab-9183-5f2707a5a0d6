package com.centerm.common.printDto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class LineData {
    @ApiModelProperty(value = "行数据类型 " +
            "0：base64 编码的 logo 数据 " +
            "1：交易或通知数据" +
            "2：二维码链接" +
            "3：空行" +
            "4：分割横线" +
            "5：广告数据")
    private String tp;

    @ApiModelProperty(value = "对齐方式")
    private String al;

    @ApiModelProperty(value = "行距")
    private String ls;

    @ApiModelProperty(value = "字间距")
    private String ws;

    @ApiModelProperty(value = "列数据")
    private List<ColData> cols;
}
