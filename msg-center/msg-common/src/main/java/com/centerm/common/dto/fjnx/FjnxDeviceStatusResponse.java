package com.centerm.common.dto.fjnx;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FjnxDeviceStatusResponse {
    @ApiModelProperty("返回码  000000：成功\n" +
            "100000：设备下线\n" +
            "100001：签名错误\n" +
            "100002：设备不存在\n" +
            "100003：访问密钥信息错误\n" +
            "100004：缺少必输参数\n" +
            "100005：API调用超限\n" +
            "100006：其他错误\n")
    private String code;

    @ApiModelProperty("返回信息")
    private String error_message;

    @ApiModelProperty("请求唯一编号")
    private String request_id;

    @ApiModelProperty("请求的时间戳")
    private String request_timestamp;

    @ApiModelProperty("签名唯一随机数")
    private String nonce;

    @ApiModelProperty("签名")
    private String sign;

    @ApiModelProperty("响应唯一编号")
    private String response_id;

    @ApiModelProperty("响应的时间戳")
    private String response_timestamp;

    @ApiModelProperty("设备状态")
    private String device_sta;

    @ApiModelProperty("设备最近一次上线时的ip地址")
    private String client_ip;

    @ApiModelProperty("设备创建时间")
    private String create_time;

    @ApiModelProperty("设备激活时间")
    private String active_time;

    @ApiModelProperty("设备最近一次登录时间")
    private String last_login_time;


    @ApiModelProperty("设备最近一次登出时间")
    private String last_logout_time;

}
