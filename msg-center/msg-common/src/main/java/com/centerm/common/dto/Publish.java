package com.centerm.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: basic
 * @description: 消息发布实体类
 * @author: <PERSON>
 * @create: 2019-02-26 15:45
 **/
@Data
@ApiModel
public class Publish extends BaseDTO {

    @ApiModelProperty(value = "主题", required = true)
    private String topicName;

    @ApiModelProperty(value = "指定消息的发送方式。0：最多发送一次。1：最少发送一次。默认值0。", required = false)
    private Integer qos = 2;

    @ApiModelProperty(value = "要发送的消息主体。", required = true)
    private String messageContent;

    @ApiModelProperty(value = "")
    private Boolean retain = false;

    @ApiModelProperty(value = "是否接阿里云物联网")
    private Integer iotType;

    @ApiModelProperty(value = "接入阿里云物联网需要用到的产品密钥")
    private String productKey;

    @ApiModelProperty(value = "设备SN")
    private String sn;
}