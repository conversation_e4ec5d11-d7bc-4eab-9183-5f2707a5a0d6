package com.centerm.common.utils.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.centerm.common.config.ParamConfig;
import com.centerm.common.constant.MsgCenterConstants;
import com.centerm.common.dto.Instruct;
import com.centerm.common.enums.GatewayPath;
import com.centerm.common.utils.CommonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @program: demo
 * @description: emq api客户端
 * @author: <PERSON>
 * @create: 2019-02-27 15:04
 **/
@Slf4j
public class HttpClient {

    public static JSONObject sendPost(String url, Object object){
        // 获取连接客户端工具
        //1.获得一个httpclient对象
        CloseableHttpClient httpClient = HttpRequestUtils.getHttpClient(3000);
        String result = null;
        CloseableHttpResponse res = null;
        try {
            //post传入值为对象
            String postParams = JSON.toJSONString(object);
            HttpPost requestPost = new HttpPost(url);
            StringEntity s = new StringEntity(postParams,"UTF-8");
            s.setContentEncoding("UTF-8");
            //发送json数据需要设置contentType
            s.setContentType("application/json;charset=UTF-8");
            requestPost.setEntity(s);
            res = httpClient.execute(requestPost);
            //返回json格式
            result = EntityUtils.toString(res.getEntity());
        } catch (IOException e) {
           log.error("通讯异常：{}", e.getStackTrace());
           log.error("通讯异常：{}", e);
        }
        return JSON.parseObject(result);
    }
    public static boolean sendPostToBool(String url, Object object){
        JSONObject object1 =  sendPost(url, object);
        return (boolean) object1.get("data");
    }

    public static void main(String[] args) {
        Instruct instruct = new Instruct();
        instruct.setCommand(MsgCenterConstants.COMMAND_INSTRUCT);

        instruct.setSn("01");
        boolean a = sendPostToBool("http://127.0.0.1:18081/" + GatewayPath.INSTRUCT.getUrl(), instruct);
        System.out.println(a);
        }
}
