package com.centerm.common.utils.xmlUtils;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name="config")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class Config {

    @XmlElement(name = "code",required=true)
    private String code;
	@XmlElement(name = "name",required=true)
    private String name;
	@XmlElement(name = "version",required=true)
    private String version;
	@XmlElement(name = "versionCode",required=true)
    private String versionCode;

	@XmlElement(name = "items",required=true)
    private items items;

}