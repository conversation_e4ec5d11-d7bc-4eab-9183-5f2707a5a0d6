package com.centerm.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: msg-push
 * @description: dto基类
 * @author: <PERSON>
 * @create: 2019-02-25 10:37
 **/
@Data
@ApiModel
public abstract class BaseDTO {
    @ApiModelProperty(value = "随机数",required = true)
    private String nonce;
    @ApiModelProperty(value = "时间戳",required = true)
    private String ts;
    @ApiModelProperty(value = "签名值",required = true)
    private String sign;
    @ApiModelProperty(value = "通道编号",required = true)
    private String cid;


}
