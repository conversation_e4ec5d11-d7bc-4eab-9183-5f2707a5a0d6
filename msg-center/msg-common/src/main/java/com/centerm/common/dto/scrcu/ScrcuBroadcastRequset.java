package com.centerm.common.dto.scrcu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/6/19 17:30
 **/
@Data
public class ScrcuBroadcastRequset {
	@ApiModelProperty("平台分配给应用方唯一编号")
	private String appId;
	@ApiModelProperty("云喇叭设备编号")
	private String deviceId;
	@ApiModelProperty("请求流水号，同一流水号只播报一次")
	private String reqId;
	@ApiModelProperty("请求时间，时间戳")
	private String reqTime;
	@ApiModelProperty("播报金额，单位元")
	private String txnAmt;
	@ApiModelProperty("交易类型：01 收款 02 退款，缺省值01")
	private String transType;
	@ApiModelProperty("支付类型：\n" +
			"00 惠支付\n" +
			"01微信支付\n" +
			"02支付宝\n" +
			"08 银联\n" +
			"支付类型支持在线升级，例如：00升级为XX支付")
	private String payType;
	@ApiModelProperty("签名值")
	private String sign;
}
