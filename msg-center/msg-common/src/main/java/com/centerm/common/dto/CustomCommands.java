package com.centerm.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

/**
 *【更新】二维码请求参数
 * <AUTHOR>
 * @date 2025/3/14 09:55
 */
@Data
@ApiModel
public class CustomCommands {

    @ApiModelProperty("通道编号")
    private String cid;

    @ApiModelProperty("签名值")
    private String sign;

    @ApiModelProperty("UUID")
    private String nonce;

    @ApiModelProperty("时间戳")
    private String timestamp;

    /**
     * {@link com.centerm.mqtt.enums.CustomCommandEnum#getCommand()}
     */
    @ApiModelProperty("指令集")
    private Integer command;

    @ApiModelProperty("指令内容JSON字符串")
    private String content;
}