package com.centerm.common.dto.weChatPay;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: msg-center
 * @description: 中小商家系统下行消息响应
 * @author: <PERSON>
 * @create: 2019/7/18 14:00
 **/
@Data
public class SmartCloudResponse {
    @ApiModelProperty(value = "业务结果状态码 0：成功，非0：表示失败")
    private int retcode;
    @ApiModelProperty(value = "返回信息，错误原因描述")
    private String msg;
    @ApiModelProperty(value = "设备SN号")
    private String device_sn;
    @ApiModelProperty(value = "在线状态 ONLINE:在线，OFFLINE:离线")
    private OnlineState state;

}
