package com.centerm.common.utils.emq;

import com.centerm.common.enums.CommonStatusEnums;
import com.centerm.common.enums.CommonTopicEnums;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.UUID;

/**
 * @program: msg-center
 * @description: emq 服务MQTT工具类
 * @author: <PERSON>
 * @create: 2019/3/22 17:10
 **/
public class MqttUtils {
    /**
     * 创建客户端id的后缀
     * @return
     */
    public static String createClientIdSuffix(){
        return RandomStringUtils.randomAlphanumeric(8);
    }

    /**
     * 创建消息msgid 第一位为功能type标识
     * @param commonTopicEnums
     * @return
     */
    public static String createMsgId(CommonTopicEnums commonTopicEnums){
        return commonTopicEnums.getType() + RandomStringUtils.randomAlphanumeric(31);
    }
    public static String createMsgId(){
        return RandomStringUtils.randomAlphanumeric(32);
    }

    public static CommonTopicEnums getTopicEnum(String msgId){
        return CommonTopicEnums.getTopicEnum(msgId.charAt(0) - 48);
    }

    public static String statusRecConvert(String status){
        switch (status){
            case "1":
                return CommonStatusEnums.BROADCAST_SUCCESS.getStatus();
            case "0":
                return CommonStatusEnums.BROADCAST_FAILED.getStatus();
            default:
                return status;
        }
    }
}
