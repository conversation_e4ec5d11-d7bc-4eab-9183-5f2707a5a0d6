package com.centerm.common.dto.gznx;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/7/18 14:57
 **/
@Data
public class GznxBroadcastResponse {

	@ApiModelProperty("应答码,0000成功，非0000则失败")
	private String respCode;

	@ApiModelProperty("应答信息，应答码对应的描述")
	private String respMsg;

	@ApiModelProperty("请求标识，用于唯一标识当前请求")
	private String reqId;
}
