package com.centerm.common.json;

import com.centerm.common.exception.BusinessException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * JSON解析处理
 *
 * <AUTHOR>
 */
public class JsonUtils {
    public static final String DEFAULT_FAIL = "\"Parse failed\"";
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final ObjectWriter objectWriter = objectMapper.writerWithDefaultPrettyPrinter();

    public static void marshal(File file, Object value) throws Exception {
        objectWriter.writeValue(file, value);
    }

    public static void marshal(OutputStream os, Object value) throws Exception {
        objectWriter.writeValue(os, value);
    }

    /**
     * 对象转字符串
     *
     * @param value 对象
     * @return string
     */
    public static String objectToJson(Object value) {
        try {
            return objectWriter.writeValueAsString(value);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("json.parse.exception",value);
        }
    }

    /**
     * json转mapper
     *
     * @param json json数据
     * @return map
     */
    public static Map<String, Object> jsonToMap(String json) {
        Map<String, Object> map = null;
        try {
            map = objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("json.parse.exception",json);
        }
        return map;
    }

    /**
     * json转list
     *
     * @param json     json数据
     * @param beanType 转换的实体类型
     * @param <T>      类型
     * @return list
     */
    public static <T> List<T> jsonToList(String json, Class<T> beanType) {
        List<T> list = null;
        try {
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, beanType);
            list = objectMapper.readValue(json, javaType);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("json.parse.exception",json);
        }
        return list;
    }

    /**
     * json字符串转换为对象
     *
     * @param str       字符串
     * @param valueType 对象类型
     * @param <T>       类型
     * @return 对象
     */
    public static <T> T jsonToObject(String str, Class<T> valueType) {
        try {
            return objectMapper.readValue(str, valueType);
        } catch (Exception e) {
            throw new BusinessException("json.parse.exception",str);
        }
    }

    /**
     * 获取json对象数据的属性
     *
     * @param resData 请求的数据
     * @param resPro  - 请求的属性
     * @return 返回String类型数据
     */
    public static String findValue(String resData, String resPro) {
        String result = null;
        try {
            JsonNode node = objectMapper.readTree(resData);
            JsonNode resProNode = node.get(resPro);
            result = JsonUtils.objectToJson(resProNode);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("json.parse.exception");
        }
        return result;
    }

    public static byte[] marshalBytes(Object value) throws Exception {
        return objectWriter.writeValueAsBytes(value);
    }

    public static <T> T unmarshal(File file, Class<T> valueType) throws Exception {
        return objectMapper.readValue(file, valueType);
    }

    public static <T> T unmarshal(InputStream is, Class<T> valueType) throws Exception {
        return objectMapper.readValue(is, valueType);
    }


    public static <T> T unmarshal(byte[] bytes, Class<T> valueType) throws Exception {
        if (bytes == null) {
            bytes = new byte[0];
        }
        return objectMapper.readValue(bytes, 0, bytes.length, valueType);
    }
}
