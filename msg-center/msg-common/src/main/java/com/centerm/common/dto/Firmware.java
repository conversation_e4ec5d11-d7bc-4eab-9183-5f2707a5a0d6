package com.centerm.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: basic
 * @description: 固件信息
 * @author: <PERSON>
 * @create: 2019-03-02 14:36
 **/
@Data
@ApiModel
public class Firmware{
    @ApiModelProperty(value = "版本码",required = true)
    private String version;
    @ApiModelProperty(value = "升级镜像文件Md5 值",required = true)
    private String md5Chk;
    @ApiModelProperty(value = "固件镜像下载地址",required = true)
    private String downloadUrl;
}
