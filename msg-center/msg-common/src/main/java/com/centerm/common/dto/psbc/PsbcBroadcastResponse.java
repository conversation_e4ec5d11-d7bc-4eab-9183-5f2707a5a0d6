package com.centerm.common.dto.psbc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/9/6 17:30
 **/
@Data
public class PsbcBroadcastResponse {
	@ApiModelProperty("返回响应码")
	private String code;

	@ApiModelProperty("结果描述")
	private String msg_desc;

	@ApiModelProperty("签名唯一随机数")
	private String nonce;

	@ApiModelProperty("签名结果串")
	private String sign;



}
