package com.centerm.common.utils.emq;

import com.centerm.common.config.ParamConfig;
import com.centerm.common.enums.CommonStatusEnums;
import com.centerm.common.enums.CommonTopicEnums;

/**
 * @program: msg-center
 * @description: topic
 * @author: <PERSON>
 * @create: 2019/4/4 10:13
 **/
public class CommonTopicUtils {
    private static final String INIT_PRODUCTKEY = ParamConfig.getProductKey();

    public static String topicConvert(CommonTopicEnums commonTopicEnums){
        return commonTopicEnums.getTopicName()
                .replace("${productKey}", INIT_PRODUCTKEY);
    }
    public static String topicConvert(CommonTopicEnums commonTopicEnums, String sn){
        return topicConvert(commonTopicEnums, INIT_PRODUCTKEY, sn);
    }
    //20190708 体现产品key和 cid（通道编号）对应
    public static String topicConvert(CommonTopicEnums commonTopicEnums, String productKey, String sn){
        return commonTopicEnums.getTopicName()
                .replace("${productKey}", productKey)
                .replace("${sn}", sn);
    }

}
