package com.centerm.common.printDto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> yuwen
 */
@Data
public class ColData {
    @ApiModelProperty(value = "打印值")
    private String cv;
    @ApiModelProperty(value = "对齐方式")
    private String al;
    @ApiModelProperty(value = "打印内容颜色")
    private String fc;
    @ApiModelProperty(value = "打印字体大小")
    private String fs;
    @ApiModelProperty(value = "打印字体样式")
    private String tf;
    @ApiModelProperty(value = "文本下划线")
    private String tu;
    @ApiModelProperty(value = "加粗")
    private String tb;
}
