<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.quartz.mapper.LogDeviceTaskMapper">

    <select id="callLogDeviceReportMonth">
        call log_device_report_month();
    </select>

    <select id="callReport">
        <choose>
            <when test="dateType == 1">
                call log_device_report_hour();
            </when>
            <when test="dateType == 2">
                call log_device_report_day();
            </when>
            <otherwise>
                call log_device_report_month();
            </otherwise>
        </choose>
    </select>
</mapper>