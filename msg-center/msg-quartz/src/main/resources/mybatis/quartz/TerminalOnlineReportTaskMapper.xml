<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.quartz.mapper.TerminalOnlineReportTaskMapper">
    
    <resultMap type="TerminalOnlineReportTask" id="TerminalOnlineReportResult">
        <result property="id"    column="id"    />
        <result property="onlineTotal"    column="online_total"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>


    <insert id="insert" parameterType="TerminalOnlineReportTask" >
        insert into t_terminal_online_report(
        <if test="onlineTotal != null and onlineTotal != 0">online_total,</if>
        create_time
        )values(
        <if test="onlineTotal != null and onlineTotal != 0">#{onlineTotal},</if>
        sysdate()
        )
    </insert>


    <select id="selectOnlineTotal" resultType="long">
        select count(*) from t_device
        where network_status = '1'
    </select>
</mapper>