package com.centerm.quartz.service.impl;

import com.centerm.quartz.mapper.LogMoneyTaskMapper;
import com.centerm.quartz.service.ILogMoneyTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/6/6 15:02
 **/
@Service
public class LogMoneyTaskServiceImpl implements ILogMoneyTaskService {
	@Autowired
	private LogMoneyTaskMapper logMoneyMapper;

	@Override
	public void logMoneyReportYesterday() {
		logMoneyMapper.callLogMoneyReport();
	}

	@Override
	public void countReportByHour() {
		logMoneyMapper.callReport(1);
	}

	@Override
	public void countReportByDay() {
		logMoneyMapper.callReport(2);
	}

	@Override
	public void countReportByMonth() {
		logMoneyMapper.callReport(3);
	}

	@Override
	public void logMoneyTrans() {
		logMoneyMapper.callLogMoneyTrans();
	}

}
