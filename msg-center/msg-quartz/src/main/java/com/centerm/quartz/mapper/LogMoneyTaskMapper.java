package com.centerm.quartz.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/6/6 15:03
 **/
@Repository
public interface LogMoneyTaskMapper {
	/**
	 * 将昨日的交易记录生成报表
	 */
	void callLogMoneyReport();

	/**
	 * 将昨日的交易转储到历史表
	 */
	void callLogMoneyTrans();

	void callReport(@Param("dateType") int dateType);

}
