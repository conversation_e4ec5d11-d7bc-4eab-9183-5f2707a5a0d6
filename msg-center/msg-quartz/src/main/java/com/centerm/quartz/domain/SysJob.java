package com.centerm.quartz.domain;

import com.centerm.common.annotation.Excel;
import com.centerm.common.base.BaseEntity;
import com.centerm.common.constant.ScheduleConstants;
import com.centerm.common.utils.StringUtils;
import com.centerm.quartz.util.CronUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 定时任务调度表 sys_job
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysJob extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @Excel(name = "Job ID")
    private Long jobId;

    /**
     * 任务名称
     */
    @Excel(name = "Job Name")
    private String jobName;

    /**
     * 任务组名
     */
    @Excel(name = "Job Group")
    private String jobGroup;

    /**
     * 任务方法
     */
    @Excel(name = "Method Name")
    private String methodName;

    /**
     * 方法参数
     */
    @Excel(name = "Method Params")
    private String methodParams;

    /**
     * cron执行表达式
     */
    @Excel(name = "Cron Expression")
    private String cronExpression;

    /**
     * cron计划策略
     */
    @Excel(name = "Strategy")
    private String misfirePolicy = ScheduleConstants.MISFIRE_DEFAULT;

    /**
     * 任务状态（0正常 1暂停）
     */
    @Excel(name = "Status", readConverterExp = "0=Normal,1=Paused")
    private String status;

    public Date getNextValidTime() {
        if (StringUtils.isNotEmpty(cronExpression)) {
            return CronUtils.getNextExecution(cronExpression);
        }
        return null;
    }

}