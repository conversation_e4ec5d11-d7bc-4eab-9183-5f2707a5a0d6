<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.mqtt.msg.printTemplate.mapper.PrintTemplateMapper">
    
    <resultMap type="PrintTemplate" id="PrintTemplateResult">
        <result property="id"    column="id"    />
        <result property="templateName"    column="template_name"    />
        <result property="templateValue"    column="template_value"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectPrintTemplateVo">
        select t.id, t.template_name, t.template_value from t_print_template t
    </sql>

    <select id="selectPrintTemplateList" parameterType="PrintTemplate" resultMap="PrintTemplateResult">
        <include refid="selectPrintTemplateVo"/>
        <where>  
        </where>
    </select>

    
    <select id="selectPrintTemplateById" parameterType="Long" resultMap="PrintTemplateResult">
        <include refid="selectPrintTemplateVo"/>
        where id = #{id}
    </select>

    <select id="selectPrintTemplateByName" parameterType="String" resultMap="PrintTemplateResult">
        <include refid="selectPrintTemplateVo"/>
        where t.template_name = #{templateName}
    </select>

    <select id="selectPrintTemplateByCid" parameterType="String" resultMap="PrintTemplateResult">
        select t.id, t.template_name, t.template_value
        from t_print_template t
        where id =
        (select dc.print_template_id from sys_dept sd
        LEFT JOIN t_dept_config dc
        ON sd.dept_id =dc.dept_id
        where sd.cid
        =#{cid})
    </select>

    
</mapper>