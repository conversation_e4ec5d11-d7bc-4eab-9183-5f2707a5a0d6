<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.mqtt.msg.sim.mapper.SimAlertMapper">

    <resultMap id="SimAlert" type="SimAlert">
        <result property="id" column="id"/>
        <result property="iccid" column="iccid"/>
        <result property="message" column="message"/>
        <result property="expires" column="expires"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="listSimAlertsByIccid" parameterType="String" resultType="SimAlert">
        select t.id, t.iccid, t.message from t_sim_alert t
        where t.iccid = #{iccid}
        limit 1
    </select>

</mapper>