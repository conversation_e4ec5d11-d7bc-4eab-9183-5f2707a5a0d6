<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.mqtt.msg.instructsRecived.mapper.InstructsRecivedMapper">
    
    <resultMap type="InstructsRecived" id="InstructsRecivedResult">
        <result property="id"    column="id"    />
        <result property="msgId"    column="msg_id"    />
        <result property="sn"    column="sn"    />
        <result property="status"    column="status"    />
        <result property="reason"    column="reason"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>
	
	<sql id="selectInstructsRecivedVo">
        select id, msg_id, sn, status, reason, create_time from t_instructs_recived
    </sql>
	
    
</mapper>