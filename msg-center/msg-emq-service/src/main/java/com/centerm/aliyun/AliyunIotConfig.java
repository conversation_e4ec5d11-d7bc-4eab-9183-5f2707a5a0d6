package com.centerm.aliyun;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @created 2022/3/29 上午10:59
 */
@Configuration
public class AliyunIotConfig {

    @Value("${aliyun.client.accessKeyId}")
    public static String accessKeyId ; //密钥ID

    @Value("${aliyun.client.accessKeySecret}")
    public static String accessKeySecret ; //密钥

    @Value("${aliyun.client.iotInstanceId}")
    public static String iotInstanceId ;

    @Value("${aliyun.client.regionId}")
    public static String regionId ; //区域ID

    @Autowired
    public AliyunIotConfig(@Value("${aliyun.client.accessKeyId}") String accessKeyId,
                           @Value("${aliyun.client.accessKeySecret}") String accessKeySecret,
                           @Value("${aliyun.client.iotInstanceId}") String iotInstanceId,
                           @Value("${aliyun.client.regionId}") String regionId,
                           @Value("${env.profile}") String profile) {
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.iotInstanceId = iotInstanceId;
        this.regionId = regionId;

    }

}
