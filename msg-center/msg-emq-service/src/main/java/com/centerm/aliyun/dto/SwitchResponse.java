package com.centerm.aliyun.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @created 2022/4/26 上午11:48
 */
@Data
public class SwitchResponse {

    @ApiModelProperty(value = "应答码，0：成功，-1：失败")
    private String code;

    @ApiModelProperty(value = "应答信息")
    private String msg;

    public void success() {
        this.code = "0";
        this.msg = "成功";
    }

    public void failure() {
        this.code = "-1";
        this.msg = "失败";
    }



}
