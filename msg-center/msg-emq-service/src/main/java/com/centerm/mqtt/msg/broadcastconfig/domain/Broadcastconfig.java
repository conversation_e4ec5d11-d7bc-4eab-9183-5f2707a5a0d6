package com.centerm.mqtt.msg.broadcastconfig.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 设备播报配置表 t_broadcast_config
 * 
 * <AUTHOR> auto
 * @date 2019-03-15
 */
@ApiModel(value = "设备播报配置")
@Data
@ToString
@TableName("t_broadcast_config")
@Accessors(chain = true)
public class Broadcastconfig{
private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "编号")
	@TableId(value = "id", type = IdType.INPUT)
	private String id;

	@ApiModelProperty(value = "设备序列号")
	private String sn;

	@ApiModelProperty(value = "广告开关")
	private String advertSwitch;

	@ApiModelProperty(value = "播报开关")
	private String broadcastSwitch;

	@ApiModelProperty(value = "播报策略")
	private String broadcastStrategy;

	@ApiModelProperty(value = "防逃单提醒")
	private String broadcastAlarm;

	@ApiModelProperty(value = "个性语音")
	private String broadcastIndividuality;

	@ApiModelProperty(value = "播报门店")
	private String broadcastStore;

}
