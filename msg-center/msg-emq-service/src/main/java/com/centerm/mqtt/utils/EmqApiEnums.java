package com.centerm.mqtt.utils;

/**
 * @program: basic
 * @description: emq API接口常量类
 * @author: <PERSON>
 * @create: 2019-02-28 17:47
 **/
public enum EmqApiEnums {
    GET_MANAGEMENT_NODES("GET","api/v2/management/nodes","获取全部节点的基本信息"),
    GET_MANAGEMENT_NODE("GET","api/v2/management/nodes/emq@127.0.0.1","获取指定节点的基本信息"),
    GET_MONITORING_NODES("GET","api/v2/monitoring/nodes","获取全部节点的监控数据"),
    GET_MONITORING_NODE("GET","api/v2/monitoring/nodes/emq@127.0.0.1","获取指定节点的监控数据"),
    GET_NODES_CLIENTS("GET","api/v2/nodes/emq@127.0.0.1/clients","获取指定节点的客户端连接列表"),
    GET_NODES_CLIENT_INFO("GET","api/v2/nodes/emq@127.0.0.1/clients/{clientid}","获取节点指定客户端连接的信息"),
    GET_CLIENT_INFO("GET","api/v2/clients/{clientid}","获取集群内指定客户端的信息"),
    DELETE_CLIENT("DELETE","api/v2/clients/{clientid}","断开集群内指定客户端连接"),
    GET_NODES_SESSIONS("GET","API/V2/nodes/emq@127.0.0.1/SESSIONS","获取指定节点的会话列表"),
    GET_NODES_SESSION("GET","api/v2/nodes/emq@127.0.0.1/sessions/{clientid}","获取节点上指定客户端的会话信息"),
    GET_SESSIONS_CLIENT("GET","api/v2/sessions/{clientid}","获取集群内指定客户端的会话信息"),
    GET_NODES_SUBSCRIPTIONS("GET","api/v2/nodes/emq@127.0.0.1/subscriptions","获取某个节点上的订阅列表"),
    GET_NODES_SUBSCRIPTIONS_CLIENT("GET","api/v2/nodes/emq@127.0.0.1/subscriptions/{clientid}","获取节点上指定客户端的订阅信息"),
    GET_SUBSCRIPTIONS_CLIENT("GET","api/v2/subscriptions/{clientid}","获取集群内指定客户端的订阅信息"),
    GET_ROUTES_TOPICS("GET","api/v2/routes","获取集群主题信息"),
    GET_ROUTES_TOPIC("GET","api/v2/routes/{topic}","获取集群内指定主题的信息"),
    POST_MQTT_PUBLISH("POST","api/v2/mqtt/publish","发布消息"),
    POST_MQTT_SUBSCRIBE("POST","api/v2/mqtt/subscribe","创建订阅"),
    POST_MQTT_UNSUBSCRIBE("POST","api/v2/mqtt/unsubscribe","取消订阅"),
    GET_NODES_PLUGINS("GET","api/v2/nodes/emq@127.0.0.1/plugins","获取节点的插件列表"),
    PUT_NODES_PLUGIN("PUT","api/v2/nodes/emq@127.0.0.1/plugins/{name}","开启/关闭节点的指定插件"),
    GET_MONITORING_LISTENERS("GET","api/v2/monitoring/listeners","获取集群的监听器列表"),
    GET_MONITORING_LISTENER("GET","api/v2/monitoring/listeners/emq@127.0.0.1","获取指定节点的监听器列表"),
    GET_MONITORING_METRICS("GET","api/v2/monitoring/metrics","获取全部节点的度量指标"),
    GET_MONITORING_METRIC("GET","api/v2/monitoring/metrics/emq@127.0.0.1","获取指定节点的度量指标"),
    GET_MONITORING_STATS("GET","api/v2/monitoring/stats","获取全部节点的运行统计"),
    GET_MONITORING_STAT("GET","api/v2/monitoring/stats/emq@127.0.0.1","获取指定节点的运行统计"),

   // GET_NODES_CLIENTS_("GET","api/v2/nodes/{nodeName}/clients","获取指定节点的客户端连接列表"),
   // GET_NODES_SUBSCRIPTIONS_CLIENT_("GET","api/v2/nodes/{nodeName}/subscriptions/{clientid}","获取节点上指定客户端的订阅信息"),


    GET_NODES_CLIENTS_("GET","api/v4/nodes/{nodeName}/clients","获取指定节点的客户端连接列表"),
    GET_NODES_SUBSCRIPTIONS_CLIENT_("GET","api/v4/nodes/{nodeName}/subscriptions/{clientid}","获取节点上指定客户端的订阅信息"),
    POST_MQTT_SUBSCRIBE_V4("POST","api/v4/mqtt/subscribe","创建订阅");

    /**
     * 请求方法
     */
    private String method;
    /**
     * 路径
     */
    private String path;
    /**
     * 描述
     */
    private String desc;

    EmqApiEnums(String method, String path, String desc) {
        this.method = method;
        this.path = path;
        this.desc = desc;
    }

    public String getMethod() {
        return method;
    }

    public String getPath() {
        return path;
    }

    public String getDesc() {
        return desc;
    }
}
