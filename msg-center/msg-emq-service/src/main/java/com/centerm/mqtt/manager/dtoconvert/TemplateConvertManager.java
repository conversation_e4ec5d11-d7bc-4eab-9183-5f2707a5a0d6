package com.centerm.mqtt.manager.dtoconvert;

import com.centerm.common.dto.ynnx.YnnxConfigRequest;
import com.centerm.common.dto.ynnx.YnnxTTSRequest;
import com.centerm.common.enums.TemplateEnums;
import com.centerm.mqtt.msg.template.domain.Template;
import org.springframework.stereotype.Component;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/6/20 9:28
 **/
@Component
public class TemplateConvertManager {


    public static TemplateConvertManager templateConvertManager;

    public static synchronized TemplateConvertManager getInstance() {
        if (templateConvertManager == null) {
            synchronized (TemplateConvertManager.class) {
                if (templateConvertManager == null) {
                    templateConvertManager = new TemplateConvertManager();

                }
            }
        }
        return templateConvertManager;
    }

    public void changeYnnxTemplate(YnnxConfigRequest request, Template template){
        template.setAppkey(request.getAppkey());
        template.setConfigName(request.getConfig_name());
        template.setConfigContent(request.getConfig_content());
        template.setConfigType(request.getConfig_type());
        template.setConfigScope(request.getConfig_scope());
        template.setConfigRules(request.getConfig_rules());
        template.setConfigTarget(request.getConfig_target());
        template.setConfigValue(request.getConfig_value());
        template.setNonce(request.getNonce());
        template.setTimestamp(request.getTimestamp());
    }
    public void changeYnnxPushTemplate(YnnxTTSRequest request, Template template){
        template.setAppkey(request.getAppkey());
        template.setConfigContent(request.getMessage());
        template.setConfigType(TemplateEnums.TEMP.getType());
        template.setConfigScope(request.getConfig_scope());
        template.setConfigTarget(request.getDevicesn());
        template.setConfigValue(request.getPushTime());
        template.setNonce(request.getNonce());
        template.setTimestamp(request.getTimestamp());
    }

}
