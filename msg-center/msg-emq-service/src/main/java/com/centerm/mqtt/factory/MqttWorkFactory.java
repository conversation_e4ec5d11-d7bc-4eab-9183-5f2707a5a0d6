package com.centerm.mqtt.factory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.centerm.common.constant.EmqConstants;
import com.centerm.common.constant.JobConstants;
import com.centerm.common.constant.MsgCenterConstants;
import com.centerm.common.constant.RedisConstants;
import com.centerm.common.dto.Instruct;
import com.centerm.common.dto.params.ParamsDTO;
import com.centerm.common.dto.received.TaskDetail;
import com.centerm.common.dto.scrcu.ScrcuBroadcastRequset;
import com.centerm.common.dto.transaction.Broadcast;
import com.centerm.common.enums.TemplateEnums;
import com.centerm.common.enums.TemplateScopeEnums;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.DateUtils;
import com.centerm.common.utils.StringUtils;
import com.centerm.common.utils.spring.SpringUtils;
import com.centerm.framework.service.RedisService;
import com.centerm.framework.web.service.ConfigService;
import com.centerm.mqtt.dto.http.ClientSubscribe;
import com.centerm.mqtt.manager.dtoconvert.DtoConvertManager;
import com.centerm.mqtt.manager.mqtt.EmqHttpClient;
import com.centerm.mqtt.msg.device.domain.Device;
import com.centerm.mqtt.msg.device.mapper.DeviceMapper;
import com.centerm.mqtt.msg.downloadTask.domain.DownloadTask;
import com.centerm.mqtt.msg.downloadTask.mapper.DownloadTaskMapper;
import com.centerm.mqtt.msg.logOnline.domain.LogOnline;
import com.centerm.mqtt.msg.logOnline.mapper.LogOnlineMapper;
import com.centerm.mqtt.msg.paramTask.domain.ParamTask;
import com.centerm.mqtt.msg.paramTask.mapper.ParamTaskMapper;
import com.centerm.mqtt.msg.syncMsg.service.ISyncMsgArrivedService;
import com.centerm.mqtt.msg.template.domain.Template;
import com.centerm.mqtt.msg.template.mapper.TemplateMapper;
import com.centerm.mqtt.msg.templateResult.domain.TemplateResult;
import com.centerm.mqtt.msg.templateResult.mapper.TemplateResultMapper;
import com.centerm.mqtt.service.IDeviceService;
import com.centerm.mqtt.service.IInstructService;
import com.centerm.mqtt.service.ITransactionService;
import com.centerm.mqtt.utils.EmqApiEnums;
import com.centerm.mqtt.utils.EmqUtils;
import com.centerm.system.domain.SysDept;
import com.centerm.system.service.ISysConfigService;
import com.centerm.system.service.ISysDeptService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.util.Date;
import java.util.Set;

/**
 * @program: msg-center
 * @description: mqtt 终端相关逻辑生成
 * @author: Zhang Chong
 * @create: 2019/6/21 9:06
 **/
@Slf4j
public class MqttWorkFactory {

    private static DeviceMapper deviceMapper = SpringUtils.getBean(DeviceMapper.class);
    private static LogOnlineMapper logOnlineMapper = SpringUtils.getBean(LogOnlineMapper.class);
    private static ITransactionService transactionService = SpringUtils.getBean(ITransactionService.class);

    private static ISysConfigService sysConfigService = SpringUtils.getBean(ISysConfigService.class);
    private static ISysDeptService sysDeptService = SpringUtils.getBean(ISysDeptService.class);
    private static RedisService<String> redisService = SpringUtils.getBean(RedisService.class);

    private static ISyncMsgArrivedService syncMsgArrivedService = SpringUtils.getBean(ISyncMsgArrivedService.class);

    /**
     * 客户端默认订阅主题
     *
     * @return
     */
    public static boolean clientSubDefault(String sn) {

        //客户端默认订阅的主题数组
        //TODO [EMQ产品项目key] 产品key区别不同的项目
        String[] topics = EmqUtils.getClientTopics(sn);

        //请求对象构造
        ClientSubscribe clientSubscribe = new ClientSubscribe();
        clientSubscribe.setQos(MsgCenterConstants.QOS);
        clientSubscribe.setClientid(sn);
        boolean result = true;
        //循环EMQ 创建订阅接口
        for (String topic : topics) {
            clientSubscribe.setTopic(topic);
            // FIXME 目前就一个主题订阅，未区分主题进行返回状态判断。
            JSONObject resultJson = EmqHttpClient.execute(EmqApiEnums.POST_MQTT_SUBSCRIBE_V4, clientSubscribe);
            if ((Integer) resultJson.get("code") != 0) {
                result = false;
            }
        }
        return result;
    }

    /**
     * 客户端固件任务检测并发送
     *
     * @return
     */
    public static void firmwareJobCheck(String sn) {

        TaskDetail task = SpringUtils.getBean(DownloadTaskMapper.class).selectBySn(sn);
        if (CommonUtils.isEmpty(task)) {
            //该部分内容仅是web展示，状态和次数的限制在查询任务时已经过滤了
            //重试到一定次数后修改任务为失败
            DownloadTask downloadTask1 = new DownloadTask();
            downloadTask1.setDlFlag(JobConstants.DL_STATUS_FAILED);
            downloadTask1.setResultMessage("重试次数达到上限");
            int retryCount = Integer.parseInt(SpringUtils.getBean(ConfigService.class).getKey("job_retry_count"));
            SpringUtils.getBean(DownloadTaskMapper.class).update(downloadTask1, new LambdaQueryWrapper<DownloadTask>()
                    .eq(DownloadTask::getTermSeq, sn)
                    .ge(DownloadTask::getRetryCount, retryCount)
            );
            //没有时，直接返回
            return;
        } else {
            //有任务时
            //下发时间过短时
            if (!StringUtils.isEmpty(redisService.get(RedisConstants.INSTRUCT_JOB_TASK + task.getTaskId()))) {
                log.info("当前固件升级指令下发时间间隔过短，拒绝任务发送sn: {}", sn);
                return;
            }
            boolean result = false;
            //获取redis中固件升级任务数量
            Set keys = redisService.keys(RedisConstants.INSTRUCT_JOB_TASK + "*");

            Integer jobMaxCount = Integer.parseInt(sysConfigService.selectConfigByKey(MsgCenterConstants.JOB_MAX_LIMIT_COUNT));
            if (keys.size() < jobMaxCount) {
                log.debug("缓存中已下发任务数量:{}", keys.size());
                //正常下发指令，并存储在redis中
                Long expireTime = Long.parseLong(sysConfigService.selectConfigByKey(MsgCenterConstants.JOB_MAX_EXPIRETIME));
                redisService.set(RedisConstants.INSTRUCT_JOB_TASK + task.getTaskId(), sn, expireTime);
                // 1.指令下发
                Instruct instruct = new Instruct();
                instruct.setCommand(MsgCenterConstants.COMMAND_INSTRUCT);
                instruct.setSn(sn);
                instruct.setTaskId(task.getTaskId());

                result = SpringUtils.getBean(IInstructService.class).instruct(instruct);

                //2.任务状态修改 改为指令下发成功、重试次数加1
                DownloadTask downloadTask = new DownloadTask();
                downloadTask.setTaskId(task.getTaskId());
                if (result) {
                    downloadTask.setDlFlag(JobConstants.DL_STATUS_NORMAL);
                    downloadTask.setRetryCount(task.getRetryCount() + 1);
                    log.info("任务指令发送成功。taskId{}", downloadTask.getTaskId());
                }
                downloadTask.setUpdateTime(DateUtils.getNowDate());
                SpringUtils.getBean(DownloadTaskMapper.class).updateDownloadTask(downloadTask);

            } else {
                log.info("下发固件升级任务数量已达上线，拒绝任务发送sn: {}", sn);
            }
        }

    }

    /**
     * 客户端参数任务检测并发送
     *
     * @return
     */
    public static void paramJobCheck(String sn) {
        //检测是否有任务，没有时，直接返回
        ParamTask paramTask = SpringUtils.getBean(ParamTaskMapper.class).selectBySn(sn);
        if (CommonUtils.isEmpty(paramTask)) {
            return;
        } else {
            //有任务时
            // 1.参数下发

            ParamsDTO paramsDTO = new ParamsDTO();
            paramsDTO.setParamContent(paramTask.getParamContent());
            paramsDTO.setSn(paramTask.getTermSeq());
            paramsDTO.setTaskId(paramTask.getTaskId());
            boolean result = SpringUtils.getBean(IDeviceService.class).paramPub(paramsDTO);

            if (result) {
                paramTask.setDlFlag(JobConstants.DL_STATUS_NORMAL);
            }
            //2.任务状态修改
            paramTask.setUpdateTime(DateUtils.getNowDate());
            paramTask.setRetryCount(paramTask.getRetryCount() + 1);
            SpringUtils.getBean(ParamTaskMapper.class).updateParamTask(paramTask);

        }

    }




    /**
     * 设备状态修改 包括 基本状态，网络状态
     *
     * @return
     */
    public static Runnable deviceStatusUpdate(String topic, MqttMessage message) {
        return () -> {

            try {

                log.info("设备上下线主题：{}", topic);

                //不是上线主题
                if (topic.indexOf(EmqConstants.DISCONNECTED) > 0) {
                    return;
                }

                //消息处理，获取消息中的sn号
                JSONObject deviceJson = JSON.parseObject(message.toString());

                //String ts = deviceJson.getString(EmqConstants.TS);
                Date receiveDate = new Date();

                //目前平台的sn即为emq集群的客户端id
                String sn = deviceJson.getString(EmqConstants.CLIENTID);
                if (sn == null) {
                    log.info("设备上下线：sn为空");
                    return;
                }

                //排除平台系统的设备
                if (sn.contains(EmqConstants.EMQ_PUBLISH_CLIENTID_PREFIX) || sn.contains(EmqConstants.EMQ_SUBSCRIBE_CLIENTID_PREFIX)) {
                    return;
                }

                Device device = deviceMapper.selectBySn(sn);
                if(device == null ) {
                    log.info("设备上下线：未找到设备，sn:{}", sn);
                    return;
                }

                LogOnline online = new LogOnline();
                online.setDeptId(device.getDeptId());
                online.setTime(receiveDate);
                online.setSn(device.getSn());

                //2-1. 设备在线处理
                online.setStatus(MsgCenterConstants.DEVICE_NETWORK_ONLINE);
                device.setNetworkStatus(MsgCenterConstants.DEVICE_NETWORK_CONNECT);
                if (device.getActiveTime() == null) {
                    device.setActiveTime(receiveDate);
                }

                device.setLastLoginTime(receiveDate);
                device.setLastLogoutTime(null); //不更新

                log.info("设备已上线：sn = " + device.getSn());

                /** del 2022-12-09 由终端自主订阅
                //2-2. 自动订阅主题
                boolean clientSubResult = clientSubDefault(device.getSn());
                if(!clientSubResult) {
                    log.error("主题订阅失败，重试订阅，sn:{}", device.getSn());
                    clientSubResult = clientSubDefault(device.getSn());
                }

                log.info("主题订阅{}：sn = {}", clientSubResult ? "成功" : "失败", device.getSn());
                 **/
                deviceMapper.updateBySn(device);
                logOnlineMapper.insert(online);
                log.info("更新设备状态：sn = " + device.getSn() + "：" + device.getNetworkStatus());

               // if (clientSubResult) {
                    //2-3. 任务检测 固件更新，参数下发
                    firmwareJobCheck(device.getSn());
                    paramJobCheck(device.getSn());
//                    templateJobCheck(device);
              //  }

            }
            catch (Exception e) {
                log.error("设备上下线：deviceStatusUpdate：", e);
            }
        };
    }

    public static Runnable deviceOffStatusUpdate(String topic, MqttMessage message) {
        return () -> {

            try {

                log.info("设备上下线主题：{}", topic);

                //不是下线主题
                if (!(topic.indexOf(EmqConstants.DISCONNECTED) > 0)) {
                    return;
                }

                //消息处理，获取消息中的sn号
                JSONObject deviceJson = JSON.parseObject(message.toString());

                //下线时间
                //String ts = deviceJson.getString(EmqConstants.TS);
                //Date receiveDate = new Date(Long.valueOf(ts + "000"));
                Date receiveDate = new Date();
                //目前平台的sn即为emq集群的客户端id
                String sn = deviceJson.getString(EmqConstants.CLIENTID);
                if (sn == null) {
                    log.info("设备上下线：sn为空");
                    return;
                }

                //排除平台系统的设备
                if (sn.contains(EmqConstants.EMQ_PUBLISH_CLIENTID_PREFIX) || sn.contains(EmqConstants.EMQ_SUBSCRIBE_CLIENTID_PREFIX)) {
                    return;
                }

                Device device = deviceMapper.selectBySn(sn);
                if(device == null ) {
                    log.info("设备上下线：未找到设备，sn:{}", sn);
                    return;
                }

                LogOnline online = new LogOnline();
                online.setDeptId(device.getDeptId());
                online.setTime(receiveDate);
                online.setSn(device.getSn());

                //1. 设备离线处理
                online.setStatus(MsgCenterConstants.DEVICE_NETWORK_OUTLINE);

                device.setLastLogoutTime(receiveDate);
                device.setNetworkStatus(MsgCenterConstants.DEVICE_NETWORK_DISCONNECT);
                device.setLastLoginTime(null); //不更新

                log.info("设备已离线：sn = " + device.getSn());

                deviceMapper.updateBySn(device);
                logOnlineMapper.insert(online);

                log.info("更新设备状态：sn = " + device.getSn() + "：" + device.getNetworkStatus());

            }
            catch (Exception e) {
                log.error("设备上下线：deviceStatusUpdate：", e);
            }
        };
    }

}