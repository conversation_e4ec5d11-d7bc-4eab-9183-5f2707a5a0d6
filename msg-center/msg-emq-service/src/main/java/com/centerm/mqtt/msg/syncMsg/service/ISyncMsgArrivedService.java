package com.centerm.mqtt.msg.syncMsg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.mqtt.msg.syncMsg.domain.SyncMsgArrived;

/**
 * <AUTHOR>
 * @created 2021/8/12 上午11:39
 */
public interface ISyncMsgArrivedService extends IService<SyncMsgArrived> {

    /**
     * 判断是否已同步接收消息
     * @param msgId
     * @param sn
     * @return
     */
    public boolean isSyncMsgArrived(String msgId, String sn) ;
}
