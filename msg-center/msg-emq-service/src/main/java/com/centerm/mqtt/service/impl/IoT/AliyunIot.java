package com.centerm.mqtt.service.impl.IoT;

import com.centerm.aliyun.AliyunIotClient;
import com.centerm.mqtt.dto.PublishRequest;
import com.centerm.mqtt.service.IotClient;

/**
 *【阿里云IoT】推送实现
 * <AUTHOR>
 * @date 2024/4/7 15:20
 */
public class AliyunIot implements IotClient {

    @Override
    public void publish(PublishRequest request) {
        AliyunIotClient.getInstance().publish(request);
    }
}