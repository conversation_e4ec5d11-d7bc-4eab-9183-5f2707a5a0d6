package com.centerm.mqtt.msg.syncMsg.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.mqtt.msg.syncMsg.domain.SyncMsgArrived;
import com.centerm.mqtt.msg.syncMsg.mapper.SyncMsgArrivedMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @created 2021/8/12 上午11:40
 */
@Slf4j
@Service
public class SyncMsgArrivedServiceImpl extends ServiceImpl<SyncMsgArrivedMapper, SyncMsgArrived> implements ISyncMsgArrivedService {

    @Override
    public boolean isSyncMsgArrived(String msgId, String sn) {
        /*SyncMsgArrived syncMsgArrived = new SyncMsgArrived();
        syncMsgArrived.setMsgId(msgId);
        syncMsgArrived.setSn(sn);
        syncMsgArrived.setCreateTime(new Date());
        try {
            int sync = this.baseMapper.insert(syncMsgArrived);
            if (sync <=0 ) {
                return true;
            }
        }
        catch (Exception e) {
            //log.error("同步消息处理异常:", e);
            return true;
        }*/
        return false;
    }
}
