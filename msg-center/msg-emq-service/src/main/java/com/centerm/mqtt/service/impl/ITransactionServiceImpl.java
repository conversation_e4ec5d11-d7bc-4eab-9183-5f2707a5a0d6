package com.centerm.mqtt.service.impl;

import com.centerm.common.constant.Constants;
import com.centerm.common.dto.message.Message;
import com.centerm.common.dto.transaction.Broadcast;
import com.centerm.common.enums.CommonTopicEnums;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.emq.MqttUtils;
import com.centerm.common.utils.spring.SpringUtils;
import com.centerm.mqtt.dto.MessageRequest;
import com.centerm.mqtt.manager.message.MessageManager;
import com.centerm.mqtt.manager.message.MessagePubLishManager;
import com.centerm.mqtt.msg.adverlog.mapper.AdverlogMapper;
import com.centerm.mqtt.msg.advert.domain.Advert;
import com.centerm.mqtt.msg.broadcastconfig.domain.Broadcastconfig;
import com.centerm.mqtt.msg.commonStatus.ReceivedMqtt;
import com.centerm.mqtt.msg.commonStatus.ReceivedMqtts;
import com.centerm.mqtt.msg.commonStatus.mapper.CommonStatusMapper;
import com.centerm.mqtt.msg.device.domain.Device;
import com.centerm.mqtt.msg.device.mapper.DeviceMapper;
import com.centerm.mqtt.msg.logMoney.domain.LogMoney;
import com.centerm.mqtt.msg.logMoney.dto.LogMoneySummary;
import com.centerm.mqtt.msg.logMoney.mapper.LogMoneyMapper;
import com.centerm.mqtt.msg.paramTask.domain.ParamTask;
import com.centerm.mqtt.msg.paramTask.mapper.ParamTaskMapper;
import com.centerm.mqtt.msg.receivelog.domain.LogReceive;
import com.centerm.mqtt.msg.receivelog.mapper.ReceiveLogMapper;
import com.centerm.mqtt.service.ITransactionService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * @program: msg-center
 * @description:
 * @author: Zhang Chong
 * @create: 2019/3/31 15:31
 **/
@Service
@Slf4j
public class ITransactionServiceImpl implements ITransactionService {
    private static final Logger logger = LoggerFactory.getLogger(ITransactionServiceImpl.class);

    @Autowired
    private ReceiveLogMapper receiveLogMapper;
    @Autowired
    private LogMoneyMapper logMoneyMapper;
    @Autowired
    private AdverlogMapper adverlogMapper;
    @Autowired
    private CommonStatusMapper commonStatusMapper;
    @Autowired
    private ParamTaskMapper paramTaskMapper;

    private MessageManager messageManager = MessageManager.getInstance();


    @Override
    public boolean summary(String sn) {
        MessageRequest request = new MessageRequest();
        String msgId = MqttUtils.createMsgId(CommonTopicEnums.VOICE_BROADCAST);
        request.setMsgId(msgId);
        request.setType(CommonTopicEnums.VOICE_BROADCAST.getType());

        List<LogMoneySummary> list = logMoneyMapper.summaryToday(sn);


        StringBuilder content = new StringBuilder();

        /**
         * 收款为0 退款为1
         */
        BigDecimal sum0 = new BigDecimal(0);
        BigDecimal sum1 = new BigDecimal(0);
        for (LogMoneySummary summary : list) {

            if (summary.getPaidType() == 0) {
                content.append(summary.getChannelName());
                content.append(summary.getMoneySum() + "元");
                content.append(",");
                sum0 = sum0.add(summary.getMoneySum());
            } else {
                sum1 = sum1.add(summary.getMoneySum());
            }
        }

        String finalContent = "今日共计收款: " + sum0 + "元." + content.toString() + ".退款" + sum1 + "元.";

        logger.info("设备:{} {}", sn, finalContent);

        request.setContent(finalContent);

        return MessagePubLishManager.messagePub(request, sn);
    }

    @Override
    public boolean received(ReceivedMqtts receivedMqtts) {
        if (CommonUtils.isEmpty(receivedMqtts.getMsgs())) {
            return false;
        }
        for (ReceivedMqtt receivedMqtt : receivedMqtts.getMsgs()) {
            receivedMqtt.setCurrentTime(new Date());
            receivedMqtt.setStatus(MqttUtils.statusRecConvert(receivedMqtt.getStatus()));
            CommonTopicEnums commonTopicEnums = MqttUtils.getTopicEnum(receivedMqtt.getMsgId());
            if (commonTopicEnums == CommonTopicEnums.TRANSACTION_BROADCAST) {
                log.info("收到播报回执：" + receivedMqtt.getMsgId());
                commonStatusMapper.updateLogMoneyStatus(receivedMqtt);
            } else if (commonTopicEnums == CommonTopicEnums.PARAMS_UPGRADE) {
                log.info("收到参数回执：" + receivedMqtt.getMsgId());
                ParamTask task = new ParamTask();
                task.setDlFlag(receivedMqtt.getStatus());
                task.setTaskId(receivedMqtt.getMsgId());
                paramTaskMapper.updateParamTask(task);
            }

        }
        return true;
    }

    @Override
    public boolean received(ReceivedMqtts receivedMqtts, String ip) {
        if (CommonUtils.isEmpty(receivedMqtts.getMsgs())) {
            return false;
        }
        for (ReceivedMqtt receivedMqtt : receivedMqtts.getMsgs()) {
            receivedMqtt.setCurrentTime(new Date());
            receivedMqtt.setStatus(MqttUtils.statusRecConvert(receivedMqtt.getStatus()));
            CommonTopicEnums commonTopicEnums = MqttUtils.getTopicEnum(receivedMqtt.getMsgId());
            if (commonTopicEnums == CommonTopicEnums.TRANSACTION_BROADCAST) {
                log.info("收到播报回执：" + receivedMqtt.getMsgId());
                LogMoney logMoney = logMoneyMapper.selectByMsgId(receivedMqtt.getMsgId());
                log.info("logMoney：" + logMoney);
                if(logMoney!=null){
                    Device device = new Device();
                    device.setRecentIp(ip);
                    device.setSn(logMoney.getSn());
                    if(!CommonUtils.isEmpty(logMoney.getReceiveId())){
                        LogReceive receiveLog =receiveLogMapper.selectById(logMoney.getReceiveId());
                        receiveLog.setResult("");
                        receiveLog.setStatus(Constants.SUCCESS);
                        receiveLogMapper.updateById(receiveLog);
                    }
                    SpringUtils.getBean(DeviceMapper.class).updateBySn(device);
                }
                commonStatusMapper.updateLogMoneyStatus(receivedMqtt);
            } else if (commonTopicEnums == CommonTopicEnums.PARAMS_UPGRADE) {
                log.info("收到参数回执：" + receivedMqtt.getMsgId());
                ParamTask task = new ParamTask();
                task.setDlFlag(receivedMqtt.getStatus());
                task.setTaskId(receivedMqtt.getMsgId());
                paramTaskMapper.updateParamTask(task);
            }

        }
        return true;
    }

    @Override
    public List<LogMoney> record(String sn, String size) {
        List<LogMoney> logMoneyList = logMoneyMapper.selectBySn(sn);
        return logMoneyList;
    }
}