package com.centerm.mqtt.msg.downloadTask.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 任务下发表 t_download_task
 *
 * <AUTHOR> auto
 * @date 2019-04-08
 */
@ApiModel(value = "任务下发")
@Data
@ToString
@TableName("t_download_task")
@Accessors(chain = true)
public class DownloadTask{
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "编号")
	private String taskId;

	@ApiModelProperty(value = "作业编号")
	private Integer jobId;

	@ApiModelProperty(value = "终端序列号")
	private String termSeq;

	@ApiModelProperty(value = "厂商编号id")
	private Integer manufacturerId;

	@ApiModelProperty(value = "生产厂商")
	private String producer;

	@ApiModelProperty(value = "终端型号id")
	private Integer terminalTypeId;

	@ApiModelProperty(value = "设备型号")
	private String model;

	@ApiModelProperty(value = "应用编号")
	private String appCode;

	@ApiModelProperty(value = "应用类型")
	private String appType;

	@ApiModelProperty(value = "应用版本号")
	private String appVer;

	@ApiModelProperty(value = "url")
	private String url;

	@ApiModelProperty(value = "MD5")
	private String md5;

	@ApiModelProperty(value = "下载标识(0 下载成功 1 下载失败 2 等待下发 3 下发成功[平台自己更新]4 更新成功 5 更新失败)")
	private String dlFlag;

	@ApiModelProperty(value = "下载开始时间")
	private Date dlBeginDate;

	@ApiModelProperty(value = "下载结束时间")
	private Date dlEndDate;

	@ApiModelProperty(value = "发布时间")
	private Date releaseTime;

	@ApiModelProperty(value = "有效期")
	private Date validDate;

	@ApiModelProperty(value = "记录常见时间")
	private Date recordCreateTime;

	@ApiModelProperty(value = "结果说明")
	private String resultMessage;

	@ApiModelProperty(value = "文件大小")
	private Integer size;

	@ApiModelProperty(value = "任务最后一次下发的时间")
	private Date updateTime;

	@ApiModelProperty(value = "重发次数")
	private Integer retryCount;

	@ApiModelProperty(value = "一次重发任务个数")
	@TableField(exist = false)
	private Integer retryLimitCount;
}
