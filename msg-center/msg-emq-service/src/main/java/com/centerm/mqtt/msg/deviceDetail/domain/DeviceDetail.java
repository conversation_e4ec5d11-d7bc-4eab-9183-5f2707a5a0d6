package com.centerm.mqtt.msg.deviceDetail.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 设备版本表 t_device_detail
 *
 * <AUTHOR> auto
 * @date 2019-06-18
 */
@ApiModel(value = "设备版本")
@Data
@ToString
@TableName("t_device_detail")
@Accessors(chain = true)
public class DeviceDetail{
private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "设备序列号")
	private String sn;

	@ApiModelProperty(value = "固件版本")
	private String firmwareVersion;

	@ApiModelProperty(value = "参数版本")
	private String paramVersion;

	@ApiModelProperty(value = "语音包版本")
	private String voicePacketVersion;

	@ApiModelProperty(value = "创建者")
	private String createBy;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "更新者")
	private String updateBy;

	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	@ApiModelProperty(value = "备注信息")
	private String remark;

    //新增终端信息上送内容，Added by JianpingWang on 20210524
    @ApiModelProperty(value = "国际移动用户识别码")
    private String imsi;

    @ApiModelProperty(value = "集成电路卡识别码")
    private String iccid;

	@ApiModelProperty(value = "国际移动设备识别码")
	private String imei;

	@ApiModelProperty(value = "网络类型")
	private String netType;

	@ApiModelProperty(value = "信号值")
	private String signalLevel;

	@ApiModelProperty(value = "电池电量")
	private String battery;

	@ApiModelProperty(value = "设备型号")
	private String mode;

	@ApiModelProperty(value = "音量大小")
	private String volumeLevel;

	@ApiModelProperty(value = "WiFi MAC")
	private String wifiMac;

	@ApiModelProperty(value = "Bluetooth MAC")
	private String bluetoothMac;

	@ApiModelProperty(value = "Wi-Fi SSID")
	private String ssid;
}