package com.centerm.mqtt.enums;

import cn.hutool.core.util.ArrayUtil;
import com.centerm.mqtt.service.IotClient;
import com.centerm.mqtt.service.impl.IoT.AliyunIot;
import com.centerm.mqtt.service.impl.IoT.EmqxIot;
import com.centerm.mqtt.service.impl.IoT.HuaweiIot;
import com.centerm.mqtt.service.impl.IoT.TencentIot;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件存储器枚举
 */
@AllArgsConstructor
@Getter
public enum IotEnum {
    EMQX(0, EmqxIot.class),   //EMQX
    ALI_IOT(1, AliyunIot.class),  //阿里云
    HUAWEI_IOT(2, HuaweiIot.class),  //华为云
    TENCENT_IOT(3, TencentIot.class),  //腾讯云
    ;

    /**
     * 物联网类型
     */
    private final Integer type;

    /**
     * 客户端类
     */
    private final Class<? extends IotClient> iotClass;

    public static IotEnum getByType(Integer type) {
        return ArrayUtil.firstMatch(o -> o.getType().equals(type), values());
    }

}
