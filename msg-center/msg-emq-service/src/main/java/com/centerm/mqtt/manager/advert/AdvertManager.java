package com.centerm.mqtt.manager.advert;

import com.centerm.common.constant.MsgCenterConstants;
import com.centerm.common.constant.RedisConstants;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.spring.SpringUtils;
import com.centerm.framework.service.RedisService;
import com.centerm.mqtt.msg.advert.domain.Advert;
import com.centerm.mqtt.msg.advert.mapper.AdvertMapper;
import com.centerm.mqtt.msg.device.domain.Device;
import com.centerm.mqtt.msg.device.mapper.DeviceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * @program: msg-center
 * @description: 广告池
 * @author: <PERSON>
 * @create: 2019/3/31 20:31
 **/
@Component
public class AdvertManager {
    //TODO
    @Resource
    private AdvertMapper advertMapper;
    @Autowired
    private RedisService<Object> redisService;


    private static AdvertManager advertManager;
    private List<Advert> adverts;
    private Integer size;
    private Integer sign = 0;
    @PostConstruct
    public void init() {
        advertManager = this;
        advertManager.advertMapper = this.advertMapper;
        advertManager.adverts = advertManager.advertMapper.selectAdvertActive();
        advertManager.redisService = this.redisService;
        advertManager.redisService.set(RedisConstants.ADVERTS, advertManager.adverts);
    }
    /* 已上架、未过期广告
     * 1.按照 机构、分组、支付渠道进行过滤区分
     * 2.单个设备维度判断是否下发
     * 3.广告轮播 ？是否添加权重
     * 4.广告的过期监控
     * 5.智能化广告分发？
     */

    public static Advert getAdvertDefault(){
        //TODO 数据实时变化 缓存
        advertManager.adverts = advertManager.advertMapper.selectAdvertActive();
        advertManager.size = advertManager.adverts.size();
        if(advertManager.size == 0){
            return null;
        }
        //todo
        if(advertManager.sign >= advertManager.size){
            advertManager.sign = 0;
        }
        Advert advert = advertManager.adverts.get(advertManager.sign);
        advertManager.sign++;
        return advert;
    }



    public static Advert getAdvert(String sn, String channel,Integer deptId){
        //获取广告当前状态
        String snStatus = (String) advertManager.redisService.get(RedisConstants.ADVERT_SN_STATUS+sn);
        if(!CommonUtils.isEmpty(snStatus) && MsgCenterConstants.SWITCH_ON_BOOL.equals(snStatus)){
            return null;
        }
        Advert advert = new Advert();
        advert.setChannel(channel);
        advert.setSn(sn);
        Device device = SpringUtils.getBean(DeviceMapper.class).selectBySn(sn);
        advertManager.adverts = advertManager.advertMapper.selectAdvertByAdvert(advert);
        if(advertManager.adverts.size()==0){
            advert.setSn(null);
            if(!"0".equals(device.getGroupId())){
                advert.setGroupId(device.getGroupId().toString());
            }
            advert.setDeptId(null);
            advertManager.adverts = advertManager.advertMapper.selectAdvertByAdvert(advert);
        }
        if(advertManager.adverts.size()==0){
            advert.setSn(null);
            advert.setGroupId(null);
            advert.setDeptId(deptId);
            advertManager.adverts = advertManager.advertMapper.selectAdvertByAdvert(advert);
        }
        Integer size = advertManager.adverts.size();
        if(size == 0){
            return null;
        }
        //获取随机一个广告
        return advertManager.adverts.get((int) (Math.random()*size));
    }
}
