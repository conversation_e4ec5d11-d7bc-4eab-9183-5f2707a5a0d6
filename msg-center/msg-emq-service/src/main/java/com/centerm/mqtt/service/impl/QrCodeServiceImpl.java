package com.centerm.mqtt.service.impl;

import com.alibaba.fastjson.JSON;
import com.centerm.common.dto.Publish;
import com.centerm.common.dto.QrCodeUpdate;
import com.centerm.common.dto.QrCodes;
import com.centerm.common.utils.DescConstants;
import com.centerm.common.utils.emq.CommonTopicUtils;
import com.centerm.common.utils.emq.MqttUtils;
import com.centerm.mqtt.dto.QrCodeRequest;
import com.centerm.mqtt.service.IQrCodeService;
import com.centerm.common.enums.CommonTopicEnums;
import com.centerm.mqtt.utils.EmqUtils;
import com.centerm.mqtt.msg.logMsgPush.domain.LogMsgPush;
import com.centerm.mqtt.msg.logMsgPush.mapper.LogMsgPushMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/3/12 10:04
 **/
@Service
public class QrCodeServiceImpl implements IQrCodeService {
    @Autowired
    private LogMsgPushMapper logMsgPushMapper;

    @Override
    public Integer upgrade(QrCodes qrCodes) {
        LogMsgPush logMsgPush = new LogMsgPush();
        //请求参数构造
        QrCodeRequest request = new QrCodeRequest();
        String msgId = MqttUtils.createMsgId(CommonTopicEnums.QRCODE_UPGRADE);
        request.setMsgId(msgId);
        request.setQrCodes(qrCodes.getQrCodes());

        //消息发送实体类构造
        Publish publish = new Publish();
        String topicName = CommonTopicUtils.topicConvert(CommonTopicEnums.QRCODE_UPGRADE, qrCodes.getSn());
        publish.setTopicName(topicName);
        String messageContent = JSON.toJSONString(request);
        publish.setMessageContent(messageContent);
        //静态二维码默认为保留消息
        publish.setRetain(true);
        //日志记录
        logMsgPush.setFunc(DescConstants.QRCODE_UPGRADE);
        logMsgPush.setTarget(topicName);
        logMsgPush.setContent(messageContent);
        logMsgPush.setId(msgId);
        logMsgPush.setCreateTime(new Date());
        logMsgPushMapper.insert(logMsgPush);
        //发送消息
        EmqUtils.pub(publish);return 1;
    }

    @Override
    public Boolean update(QrCodeUpdate qrCode) {
        //【请求】参数构造
        QrCodeRequest request = new QrCodeRequest();
        String msgId = MqttUtils.createMsgId(CommonTopicEnums.QRCODE_UPDATE);
        request.setMsgId(msgId);
        request.setType(CommonTopicEnums.QRCODE_UPDATE.getType());

        //【构造】消息发送实体类
        Publish publish = new Publish();
        String messageContent = JSON.toJSONString(request);
        publish.setMessageContent(messageContent);
        publish.setRetain(true);
        publish.setSn(qrCode.getSn());
        publish.setIotType(qrCode.getIotType());
        publish.setProductKey(qrCode.getProductKey());

        //【记录】日志
        LogMsgPush logMsgPush = new LogMsgPush();
        logMsgPush.setFunc(DescConstants.QRCODE_UPDATE);
        logMsgPush.setTarget(qrCode.getSn());
        logMsgPush.setContent(messageContent);
        logMsgPush.setId(msgId);
        logMsgPush.setCreateTime(new Date());
        logMsgPushMapper.insert(logMsgPush);

        //【发送消息】
        return EmqUtils.pub(publish);
    }
}