package com.centerm.mqtt.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.centerm.common.config.Global;
import com.centerm.common.config.ParamConfig;
import com.centerm.common.constant.Constants;
import com.centerm.common.constant.SysConfigConstants;
import com.centerm.common.dto.*;
import com.centerm.common.dto.common.CommonBroadcastRequest;
import com.centerm.common.dto.common.CommonBroadcastResponse;
import com.centerm.common.dto.common.CommonResponseEnums;
import com.centerm.common.dto.device.*;
import com.centerm.common.dto.params.ParamsDTO;
import com.centerm.common.dto.transaction.Broadcast;
import com.centerm.common.enums.DeviceStatus;
import com.centerm.common.exception.BusinessException;
import com.centerm.common.utils.CommonUtils;
import com.centerm.framework.manager.RedisCacheManager;
import com.centerm.mqtt.exception.ValidateDeviceException;
import com.centerm.mqtt.manager.ThreadManager;
import com.centerm.mqtt.factory.LogFactory;
import com.centerm.mqtt.manager.message.MessageManager;
import com.centerm.mqtt.manager.mqtt.EmqHttpClient;
import com.centerm.mqtt.service.IDeviceService;
import com.centerm.mqtt.utils.EmqApiEnums;
import com.centerm.mqtt.msg.device.domain.Device;
import com.centerm.mqtt.msg.device.mapper.DeviceMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static com.baomidou.mybatisplus.extension.toolkit.SqlHelper.retBool;

/**
 * @program: msg-push
 * @description:
 * @author: Zhang Chong
 * @create: 2019-02-20 16:38
 **/

@Service
public class DeviceServiceImpl implements IDeviceService {
    protected Logger logger = LoggerFactory.getLogger(TopicServiceImpl.class);
    @Autowired
    private DeviceMapper deviceMapper;
    private MessageManager messageManager = MessageManager.getInstance();

    private static final String addressUrl = Global.getConfig("lbs.location.url");
    @Autowired
    private RestTemplate restTemplate;

    //【日志】记录成功
    public static String LOG_RESULT = "Request Accepted";

    //【标志位】验证终端机构开关
    public static String VALIDATE_DEVICE_DEPT_SWITCH_ON = "ON";

    @Override
    public DeviceSecret getSecret(String sn) {
        //TODO  密钥派发
        Device device = deviceMapper.selectBySn(sn);
        DeviceSecret deviceSecret = new DeviceSecret();
        BeanUtils.copyProperties(device, deviceSecret);
        deviceSecret.setProductKey(ParamConfig.getProductKey());
        return deviceSecret;
    }

    @Override
    public void validateDevice(CommonBroadcastRequest request, Broadcast broadcast) {
        Device device = deviceMapper.selectBySn(request.getSn());
        String logResult = LOG_RESULT;
        try {

            //【判断】设备不存在时：不进行播报
            if (CommonUtils.isEmpty(device)) {
                logResult = CommonResponseEnums.DEVICE_INEXISTENCE.getMsg();
                throw new ValidateDeviceException(CommonResponseEnums.DEVICE_INEXISTENCE.getCode(), CommonResponseEnums.DEVICE_INEXISTENCE.getMsg());
            }

            //【校验】设备所属机构
            String switchValue = RedisCacheManager.getInstance().getSysConfig(SysConfigConstants.VALIDATE_DEVICE_DEPT_SWITCH);
            if (Objects.equals(VALIDATE_DEVICE_DEPT_SWITCH_ON, switchValue)) {
                String deptId = RedisCacheManager.getInstance().getDeptIdByCid(request.getCid());
                String deviceDeptId = String.valueOf(device.getDeptId());
                if (!Objects.equals(deptId, deviceDeptId)) {
                    logResult = CommonResponseEnums.DEVICE_INEXISTENCE.getMsg();
                    throw new ValidateDeviceException(CommonResponseEnums.DEVICE_INEXISTENCE.getCode(), CommonResponseEnums.DEVICE_INEXISTENCE.getMsg());
                }
            }

            //【判断】仅正常状态的设备才进行播报
            if (!DeviceStatus.ONLINE.getStatus().equals(device.getStatus())) {
                logResult = CommonResponseEnums.DEVICE_STATUE_ERROR.getMsg();
                throw new ValidateDeviceException(CommonResponseEnums.DEVICE_STATUE_ERROR.getCode(), CommonResponseEnums.DEVICE_STATUE_ERROR.getMsg());
            }

            //【配置】返回参数
            broadcast.setDeptId(device.getDeptId());
            broadcast.setIotType(device.getIotType());
            broadcast.setProductKey(device.getProductKey());
        } catch (ValidateDeviceException e) {
            throw new BusinessException("validate.device.exception");
        } finally {
            Integer deptId = device == null ? 0 : device.getDeptId();
            ThreadManager.execute(LogFactory.saveLogReceive(request.getSn(), deptId,request.toString(), request.getNonce(), logResult, Constants.FAIL));
        }
    }


    @Override
    public Device validateDevice(CustomCommands commands, String sn) {
        Device device = deviceMapper.selectBySn(sn);
        try {

            //【判断】设备不存在时：不进行播报
            if (CommonUtils.isEmpty(device)) {
                throw new ValidateDeviceException(CommonResponseEnums.DEVICE_INEXISTENCE.getCode(), CommonResponseEnums.DEVICE_INEXISTENCE.getMsg());
            }

            //【校验】设备所属机构
            String switchValue = RedisCacheManager.getInstance().getSysConfig(SysConfigConstants.VALIDATE_DEVICE_DEPT_SWITCH);
            if (Objects.equals(VALIDATE_DEVICE_DEPT_SWITCH_ON, switchValue)) {
                String deptId = RedisCacheManager.getInstance().getDeptIdByCid(commands.getCid());
                String deviceDeptId = String.valueOf(device.getDeptId());
                if (!Objects.equals(deptId, deviceDeptId)) {
                    throw new ValidateDeviceException(CommonResponseEnums.DEVICE_INEXISTENCE.getCode(), CommonResponseEnums.DEVICE_INEXISTENCE.getMsg());
                }
            }

            //【判断】仅正常状态的设备才进行播报
            if (!DeviceStatus.ONLINE.getStatus().equals(device.getStatus())) {
                throw new ValidateDeviceException(CommonResponseEnums.DEVICE_STATUE_ERROR.getCode(), CommonResponseEnums.DEVICE_STATUE_ERROR.getMsg());
            }

            return device;
        } catch (ValidateDeviceException e) {
            throw new BusinessException("validate.device.exception");
        }
    }

    @Override
    public boolean paramPub(ParamsDTO paramsDTO) {
        return messageManager.paramPub(paramsDTO);
    }

    @Override
    public Integer register(RegisterDevice registerDevice) {
        //TODO 完善设备注册信息
        Device newDevice = new Device();
        newDevice.setId(UUID.randomUUID().toString().replace("-", ""));
        newDevice.setSn(registerDevice.getSn());
        return deviceMapper.insert(newDevice);
    }

    @Override
    public Device queryDeviceDetail(String sn) {
        return deviceMapper.selectBySn(sn);
    }

    @Override
    public Integer deleteDevice(String sn) {
        return deviceMapper.deleteBySn(sn);
    }

    @Override
    public String getDeviceStatus(String sn) {

        //FIXME 添加设备表状态检查,未注册+禁用
        Map<String, String> params = new HashMap<>(1);
        params.put("{clientid}", sn);
        JSONObject responseJson = EmqHttpClient.execute(EmqApiEnums.GET_CLIENT_INFO, params);
        String status = "";
        JSONObject objects = responseJson.getJSONObject("result");
        if (objects.getJSONArray("objects").size() > 0) {
            status = DeviceStatus.ONLINE.getStatus();
        } else {
            status = DeviceStatus.DISABLED.getStatus();
        }
        return status;
    }

    @Override
    public List<DeviceDetail> queryDevice(QueryList queryList) {
        return null;
    }

    @Override
    public Integer saveDeviceProp(DeviceProp deviceProp) {
        return null;
    }

    @Override
    public Integer deleteDeviceProp(DeviceProp deviceProp) {
        return null;
    }

    @Override
    public String queryDeviceProp(String sn) {
        return null;
    }

    @Override
    public List<DeviceProp> queryDeviceByTags(QueryDeviceByTags queryDeviceByTags) {
        return null;
    }

    @Override
    public Integer enableThing(String deviceSn) {
        return null;
    }

    @Override
    public Integer disableThing(String deviceSn) {
        return null;
    }

    @Override
    public boolean updatePosition(Device device) {
        //反向地址定位更新，免费api接口次数限制，暂时不更新
        /*try{
            StringBuffer sb = new StringBuffer();
            sb.append(addressUrl);
            sb.append("&mnc=").append(device.getMnc());
            sb.append("&mcc=").append(device.getMcc());
            sb.append("&lac=").append(device.getLac());
            sb.append("&ci=").append(device.getCid());
            if(!CommonUtils.isEmpty(device.getCoord())){
                sb.append("&coord=").append(device.getCoord());
            }
            String jsonResult = restTemplate.getForEntity(sb.toString(), String.class).getBody();
            Map mapType = JSON.parseObject(jsonResult,Map.class);
            if(mapType.get("errcode").toString().equals("0")){
                device.setAddress(mapType.get("address").toString());
            }
        }catch(Exception e){

        }*/
        return retBool(deviceMapper.updatePositionBySn(device));
    }
}