package com.centerm.mqtt.msg.device.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 设备表 t_device
 *
 * <AUTHOR>
 * @date 2019-03-02
 */

@ApiModel(value = "设备")
@Data
@ToString
@TableName("t_device")
@Accessors(chain = true)
public class Device{
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "编号")
    @TableId(value = "id",type = IdType.INPUT)
	private String id;

	@ApiModelProperty(value = "设备序列号")
	private String sn;

	@ApiModelProperty(value = "设备名称")
	private String name;

	@ApiModelProperty(value = "机构")
	private String orgId;

	@ApiModelProperty(value = "类型")
	private String type;

	@ApiModelProperty(value = "设备状态")
	private String status;

	@ApiModelProperty(value = "生产厂商")
	private String producer;

	@ApiModelProperty(value = "设备型号")
	private String model;

	@ApiModelProperty(value = "操作系统")
	private String os;

	@ApiModelProperty(value = "软件版本")
	private String version;


	@ApiModelProperty(value = "密钥")
	private String secret;

	@ApiModelProperty(value = "柜体数量")
	private Integer cabinetNums;

	@ApiModelProperty(value = "经度")
	private Double longitude;

	@ApiModelProperty(value = "纬度")
	private Double latitude;

	@ApiModelProperty(value = "网络状态")
	private String networkStatus;

	@ApiModelProperty(value = "创建者")
	private String createBy;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "更新者")
	private String updateBy;

	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	@ApiModelProperty(value = "备注信息")
	private String remark;

	@ApiModelProperty(value = "储备字段1")
	private String reserve1;

	@ApiModelProperty(value = "储备字段2")
	private String reserve2;

	@ApiModelProperty(value = "储备字段3")
	private String reserve3;

	@ApiModelProperty(value = "机构id")
	private Integer deptId;

	@ApiModelProperty(value = "组别id")
	private Integer groupId;

	@ApiModelProperty(value = "最近一次登录IP")
	private String recentIp;

	@ApiModelProperty(value = "激活时间")
	private Date activeTime;
	@ApiModelProperty(value = "最后一次登录时间")
	private Date lastLoginTime;
	@ApiModelProperty(value = "最后一次离线时间")
	private Date lastLogoutTime;


	@ApiModelProperty(value = "移动国家代码")
	private String mcc;
	@ApiModelProperty(value = "移动网络号码")
	private String mnc;
	@ApiModelProperty(value = "位置区域码")
	private String lac;
	@ApiModelProperty(value = "基站编号")
	private String cid;
	@ApiModelProperty(value = "坐标类型")
	private String coord;
	@ApiModelProperty(value = "地址")
	private String address;

    //是否接阿里云物联网 Added by JianpingWang on 20220328
    @ApiModelProperty(value = "是否接阿里云物联网")
	private Integer iotType;

    //接入阿里云物联网需要用到的产品密钥 Added by JianpingWang on 20220328
    @ApiModelProperty(value = "接入阿里云物联网需要用到的产品密钥")
	private String productKey;
}
