package com.centerm.mqtt.msg.paramTask.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 参数下发表 t_param_task
 * 
 * <AUTHOR> auto
 * @date 2019-06-13
 */
@ApiModel(value = "参数下发")
@Data
@ToString
@TableName("t_param_task")
@Accessors(chain = true)
public class ParamTask{
private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "编号")
	@TableId(value = "taskId", type = IdType.AUTO)
	private String taskId;

	@ApiModelProperty(value = "作业编号")
	private Integer jobId;

	@ApiModelProperty(value = "终端序列号")
	private String termSeq;

	@ApiModelProperty(value = "下载标识")
	private String dlFlag;

	@ApiModelProperty(value = "下载开始时间")
	private Date dlBeginDate;

	@ApiModelProperty(value = "下载结束时间")
	private Date dlEndDate;

	@ApiModelProperty(value = "发布时间")
	private Date releaseTime;

	@ApiModelProperty(value = "有效期")
	private Date validDate;

	@ApiModelProperty(value = "记录创建时间")
	private Date recordCreateTime;

	@ApiModelProperty(value = "结果说明")
	private String resultMessage;

	@ApiModelProperty(value = "文件大小")
	private Integer size;

	@ApiModelProperty(value = "任务最后一次下发的时间")
	private Date updateTime;

	@ApiModelProperty(value = "重发次数")
	private Integer retryCount;

	@ApiModelProperty(value = "一次重发任务个数")
	@TableField(exist = false)
	private Integer retryLimitCount;
	@ApiModelProperty(value = "参数文本")
	@TableField(exist = false)
	private String paramContent;
}
