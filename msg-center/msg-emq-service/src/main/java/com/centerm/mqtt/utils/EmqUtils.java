package com.centerm.mqtt.utils;

import com.centerm.common.constant.MsgCenterConstants;
import com.centerm.common.dto.Publish;
import com.centerm.common.enums.CommonTopicEnums;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.emq.CommonTopicUtils;
import com.centerm.mqtt.dto.PublishRequest;
import com.centerm.mqtt.manager.message.MessagePubLishManager;
import org.springframework.stereotype.Component;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: basic
 * @description: 公共方法提取
 * @author: <PERSON>
 * @create: 2019-03-02 15:17
 **/
@Component
public class EmqUtils {

    public static Integer getQueueSign(String sn){
        byte[] bytes = sn.getBytes();
        int sum = 0;
        for(byte b :bytes){
            sum += b;
        }
        return sum % MsgCenterConstants.CLIENTS_MAX;
    }

    public static String[] getTopics(String sn){
        CommonTopicEnums[] commonTopicEnums = CommonTopicEnums.values();
        String[] topics = new String[commonTopicEnums.length];
        for(int i = 0; i < commonTopicEnums.length; i++){
            topics[i] = CommonTopicUtils.topicConvert(commonTopicEnums[i], sn);
        }
        return topics;
    }

    /**
     * 获取sn对应该订阅的主题
     * @param sn
     * @return
     */
    public static String[] getClientTopics(String sn){
        CommonTopicEnums[] clientTopics = {CommonTopicEnums.MSG_BROADCAST};
        String[] topics = new String[clientTopics.length];
        for(int i = 0; i < clientTopics.length; i++){
            topics[i] = CommonTopicUtils.topicConvert(clientTopics[i], sn);
        }
        return topics;
    }

    /**
     * 发布主题topic
     * @param publish
     * @return
     */
    public static boolean pub(Publish publish) {
        PublishRequest request = new PublishRequest();
        request.setSn(publish.getSn());
        request.setPayload(publish.getMessageContent());
        request.setQos(MsgCenterConstants.QOS);
        if (!CommonUtils.isEmpty(publish.getRetain())) {
            request.setRetain(publish.getRetain());
        }
        request.setIotType(publish.getIotType());
        request.setProductKey(publish.getProductKey());
        return MessagePubLishManager.publish(request);
    }

    /**
     * 创建http basic认证用户字符串
     * @param username
     * @param password
     * @return
     */
    public static String createAuthorization(String username, String password){
        String base64Str = Base64.getEncoder()
                .encodeToString((username+":"+password).getBytes());
        return "Basic " + base64Str;
    }

    /**
     * 主题字符串替换加拼接
     * @param topicEnums
     * @param params
     * @return
     */
    public static String createTopicName(CommonTopicEnums topicEnums, Map<String,String> params){
        String topicName = topicEnums.getTopicName();
        for(Map.Entry<String, String> entry : params.entrySet()){
            topicName = topicName.replace(entry.getKey(), entry.getValue());
        }
        return topicName;
    }

    public static void main(String[] args) {
        Map<String,String> params = new HashMap<>();
        params.put("${sn}","123");
        System.out.println(createTopicName(CommonTopicEnums.QRCODE_UPGRADE, params));
    }
}