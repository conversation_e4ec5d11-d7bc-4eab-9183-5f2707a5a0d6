package com.centerm.mqtt.msg.instructsRecived.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 消息推送日志表 t_instructs_recived
 * 
 * <AUTHOR> auto
 * @date 2019-03-17
 */
@ApiModel(value = "消息推送日志")
@Data
@ToString
@TableName("t_instructs_recived")
@Accessors(chain = true)
public class InstructsRecived{
private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "编号",required = false,hidden = true)
	@TableId(value = "id",type = IdType.UUID)
	private String id;

	@ApiModelProperty(value = "消息ID")
	private String msgId;

	@ApiModelProperty(value = "设备sn号")
	private String sn;

	@ApiModelProperty(value = "执行状态")
	private String status;

	@ApiModelProperty(value = "指令执行描述")
	private String reason;

	@ApiModelProperty(value = "创建时间",required = false,hidden = true)
	private Date createTime;

}
