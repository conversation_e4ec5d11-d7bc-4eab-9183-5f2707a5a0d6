package com.centerm.mqtt.manager.callback;

import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;

/**
 * @program: msg-center
 * @description: 客户端连接回调
 * @author: <PERSON>
 * @create: 2019/3/12 19:26
 **/
public abstract class BaseCallback implements MqttCallback {

    @Override
    public void connectionLost(Throwable cause) {

    }

    /**
     * 消息处理
     * @param topic
     * @param message
     * @throws Exception
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {

    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        System.out.println(token.isComplete());
    }
}
