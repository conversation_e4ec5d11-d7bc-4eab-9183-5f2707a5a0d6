package com.centerm.mqtt.msg.syncMsg.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @created 2021/8/12 上午11:31
 */
@ApiModel(value = "同步emq消息接收")
@Data
@ToString
@TableName("t_sync_emq_message_arrived")
@Accessors(chain = true)
public class SyncMsgArrived {

    @ApiModelProperty(value = "消息ID")
    private String msgId;

    @ApiModelProperty(value = "设备SN")
    private  String sn;

    @ApiModelProperty(value = "时间")
    private Date createTime;
}

