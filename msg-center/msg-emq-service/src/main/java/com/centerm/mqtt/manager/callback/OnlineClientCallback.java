package com.centerm.mqtt.manager.callback;

import com.centerm.mqtt.manager.ThreadManager;
import com.centerm.mqtt.factory.MqttWorkFactory;
import com.centerm.mqtt.manager.mqtt.ClientFactory;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @created 2022/3/30 下午4:06
 */
@Slf4j
public class OnlineClientCallback extends BaseCallback {

    private static Logger logger = LoggerFactory.getLogger(ClientsConnCallback.class);


    @Override
    public void connectionLost(Throwable cause) {
        log.info("上线客户端已断连，尝试重连中...");
        ClientFactory.createOnlineClient();
    }
    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        ThreadManager.execute(MqttWorkFactory.deviceStatusUpdate(topic, message));
    }

}
