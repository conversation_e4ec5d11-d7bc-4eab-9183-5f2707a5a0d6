package com.centerm.mqtt.msg.templateResult.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 设备自定义配置表 t_template_result
 * 
 * <AUTHOR> auto
 * @date 2020-05-29
 */
@ApiModel(value = "设备自定义配置")
@Data
@ToString
@TableName("t_template_result")
@Accessors(chain = true)
public class TemplateResult{
private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "编号")
	@TableId(type = IdType.UUID)
	private String id;

	@ApiModelProperty(value = "设备序列号")
	private String sn;

	@ApiModelProperty(value = "机构模板编号")
	private String templateId;

	@ApiModelProperty(value = "设置类型(1:开机模板 2:关机模板 3:广告模板 4:播报模板)")
	private Integer configType;

	@ApiModelProperty(value = "播报规则")
	private Integer configRules;

	@ApiModelProperty(value = "状态")
	private String status;

	@ApiModelProperty(value = "重试次数")
	private Integer retryCount;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;
}
