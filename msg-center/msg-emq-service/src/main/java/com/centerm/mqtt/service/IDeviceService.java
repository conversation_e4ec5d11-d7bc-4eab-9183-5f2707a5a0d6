package com.centerm.mqtt.service;



import com.centerm.common.dto.*;
import com.centerm.common.dto.common.CommonBroadcastRequest;
import com.centerm.common.dto.common.CommonBroadcastResponse;
import com.centerm.common.dto.device.*;
import com.centerm.common.dto.params.ParamsDTO;
import com.centerm.common.dto.transaction.Broadcast;
import com.centerm.mqtt.msg.device.domain.Device;

import java.util.List;

/**
 * @program: msg-push
 * @description:
 * @author: <PERSON>
 * @create: 2019-02-20 16:37
 **/
public interface IDeviceService {
    /**
     * 密钥派发
     * @param sn
     * @return
     */
    DeviceSecret getSecret(String sn);

    /**
     *【验证】设备信息
     * @param request 请求参数
     * @param broadcast 播报信息
     * @return
     */
    void validateDevice(CommonBroadcastRequest request, Broadcast broadcast);


    Device validateDevice(CustomCommands commands, String sn);

    boolean paramPub(ParamsDTO paramsDTO);
    /**
     * 在指定产品下注册设备。
     * @param registerDevice
     * @return
     */
    Integer register(RegisterDevice registerDevice);

    /**
     * 删除指定设备。
     * @param sn
     * @return
     */
    Integer deleteDevice(String sn);

    /**
     * 查询指定设备的详细信息。
     * @param sn
     * @return
     */
    Device queryDeviceDetail(String sn);

    /**
     * 获取设备的运行状态
     * @param sn
     * @return
     */
    String getDeviceStatus(String sn);


    /**
     * 查询指定产品下的所有设备列表。
     * @param queryList
     * @return
     */
    List<DeviceDetail> queryDevice(QueryList queryList);



    /**
     * 设置设备标签
     * @param deviceProp
     * @return
     */
    Integer saveDeviceProp(DeviceProp deviceProp);

    /**
     * 删除设备标签
     * @param deviceProp
     * @return
     */
    Integer deleteDeviceProp(DeviceProp deviceProp);

    /**
     * 查询设备标签列表
     * @param sn
     * @return
     */
    String queryDeviceProp(String sn);

    /**
     * 根据标签查询设备
     * @param queryDeviceByTags
     * @return
     */
    List<DeviceProp> queryDeviceByTags(QueryDeviceByTags queryDeviceByTags);


    /**
     * 解禁设备
     * @param deviceSn
     * @return
     */
    Integer enableThing(String deviceSn);

    /**
     * 禁用设备
     * @param deviceSn
     * @return
     */
    Integer disableThing(String deviceSn);

    /**
     * 禁用设备
     * @param device
     * @return
     */
    boolean updatePosition(Device device);
}