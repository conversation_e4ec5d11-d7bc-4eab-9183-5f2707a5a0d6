package com.centerm.mqtt.service;

import com.centerm.common.dto.QrCode;
import com.centerm.common.dto.QrCodeUpdate;
import com.centerm.common.dto.QrCodes;

/**
 * @program: msg-center
 * @description: 二维码相关接口
 * @author: <PERSON>
 * @create: 2019/3/12 9:57
 **/
public interface IQrCodeService {

    /**
     * 二维码升级
     * @param qrCodes
     * @return
     */
    Integer upgrade(QrCodes qrCodes);

    /**
     *【二维码】更新
     * @param qrCode 更新参数
     * @return 更新结果
     */
    Boolean update(QrCodeUpdate qrCode);
}