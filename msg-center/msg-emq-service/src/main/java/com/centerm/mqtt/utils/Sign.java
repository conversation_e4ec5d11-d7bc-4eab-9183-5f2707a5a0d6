package com.centerm.mqtt.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.centerm.common.config.Global;
import com.centerm.common.utils.Base64Util;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.MyRSAUtils;
import com.centerm.framework.manager.RedisCacheManager;
import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/5/23 15:43
 **/
@Slf4j
public class Sign {
    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";
    private static final String ALGORITHM_SHA256WITHRSA = "sha256withRSA";
    private static final String ALGORITHM_SHA1WITHRSA = "sha1withRSA";
    private static final String ALGORITHM_AES = "AES";
    private static final String PUBLIC_KEY_RSA = Global.getConfig("icbc.signature.pubkey");
    private static final String PRIVATE_KEY_RSA = Global.getConfig("icbc.signature.prikey");
    //根据cid（机构渠道id或者通道编号） 获取对应的key值
    protected static String Key = Global.getConfig("msgcenter.signature.key");
    //protected static String Key = "PSDCIOTACCOUNTTESTDEVSECRET_XXX";
    private static final String SIGNATURE = "sign";

    /**
     * 验签
     *
     * @param parameters 待验证数据
     * @param signature  待验证签名
     * @return
     */
    public static boolean verifySignURL_SAFE(SortedMap<Object, Object> parameters, String signature, String cid) {
        String signData = getSignData(parameters);
        log.debug(signData);
        String key = RedisCacheManager.getInstance().getDeptKey(cid);
        if (CommonUtils.isEmpty(key)) {
            return false;
        }
        String veritySign = encryptSign(signData, key);
        veritySign = veritySign.replace('+', '-');
        veritySign = veritySign.replace('/', '_');
        log.info("veritySign:" + veritySign);
        return veritySign.equals(signature);
    }

    /**
     * 验签
     *
     * @param parameters 待验证数据
     * @return
     */
    public static String getSign(SortedMap<Object, Object> parameters, String cid) {
        String signData = getSignData(parameters);
        log.debug("signData:" + signData);
        String key = RedisCacheManager.getInstance().getDeptKey(cid);
        if (CommonUtils.isEmpty(key)) {
            return null;
        }
        String veritySign = encryptSign(signData, key);
        log.info("veritySign:" + veritySign);
        return veritySign;
    }

    /**
     * 验签名
     * @param parameters
     * @param signature
     * @param key 密钥
     * @return
     */
    public static boolean verifySignByKey(SortedMap<Object, Object> parameters, String signature, String key) {
        String signData = getSignData(parameters);

        log.debug("signData:" + signData);
        if (CommonUtils.isEmpty(key)) {
            return false;
        }
        String veritySign = encryptSign(signData, key);

        log.info("veritySign:" + veritySign);
        return veritySign.equals(signature);
    }

    /**
     * a. 签名采用 SHA256withRSA 方式。
     * b. 格式sign=BASE64(SHA256withRSA(key1=value1&key2=value2…))
     * c.验签
     *
     * @param parameters 待验证数据
     * @param signature  待验证签名
     * @return
     */
    public static boolean verifySignRSA(SortedMap<Object, Object> parameters, String signature, String cid) {
        String signData = getSignData(parameters);
        log.debug("SIGN: " + signData);
        String publicKey = RedisCacheManager.getInstance().getDeptKey(cid);
        if (CommonUtils.isEmpty(publicKey)) {
            return false;
        }
        try {
            byte[] sign = Base64Util.decode(signature);
            PublicKey publicKey1 = MyRSAUtils.getPublicKey(publicKey);
            boolean isRight = MyRSAUtils.verifyByPublicKey(publicKey1, signData.getBytes(), sign, ALGORITHM_SHA256WITHRSA);
            return isRight;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 非空参数按照参数名ascii码从小到大排序，使用URL键值对的格式，拼接成待签名字符串，即
     * key1=value1&key2=value2&key3=value3
     *
     * @return
     */


    public static String getSignData(SortedMap<Object, Object> parameters) {
        Set parametersSet = parameters.entrySet();
        Iterator it = parametersSet.iterator();
        StringBuffer signData = new StringBuffer();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            Object v = entry.getValue();

            //sign字段不参与签名组串
            if (!SIGNATURE.equals(k) && !CommonUtils.isEmpty(v)) {
                signData.append(k + "=" + v + "&");
            }

        }
        //return signData.toString();
        return CommonUtils.isEmpty(signData) ? "" : signData.toString().substring(0, signData.toString().length() - 1);
    }

    /**
     * 加签
     *
     * @param params
     * @return
     */
    public static String encryptSign(JSONObject params) {

        //JSONObject 转为Map
        Map map = JSON.toJavaObject(params, Map.class);
        //map转为SortedMap
        SortedMap<Object, Object> parameters = new TreeMap<Object, Object>(map);
        //待加签内容
        String signData = getSignData(parameters);
        //返回签名字符串 sign
        return encryptSign(signData, Key);
    }
    /**
     * 第二步：使用云喇叭平台分配的签名密钥key，对待签名字符串进行签名(示例使用HmacSHA256)，得到签名字符串;
     */
    /**
     * sha256_HMAC加密
     *
     * @param signData 消息
     * @param secret   秘钥
     * @return 加密后字符串
     */
    public static String encryptSign(String signData, String secret) {
        String sign = "";
        try {
            SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(), HMAC_SHA256_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            mac.init(keySpec);
            sign = Base64Util.encode(mac.doFinal(signData.getBytes()));
        } catch (Exception e) {
            log.error("Error HmacSHA256 ===========" + e.getMessage());
        }
        return sign;
    }

    public static String encryptSignURL_SAFE(String signData, String secret) {
        String sign = "";
        try {
            SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(), HMAC_SHA256_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            mac.init(keySpec);
            sign = Base64Util.encode(mac.doFinal(signData.getBytes()));
            sign = sign.replace('-', '+');
            sign = sign.replace('_', '/');
        } catch (Exception e) {
            log.error("Error HmacSHA256 ===========" + e.getMessage());
        }
        return sign;
    }

    /**
     * sha256_HMAC加密
     *
     * @param response 消息
     * @param key      秘钥
     * @return 加密后字符串
     */
    public static String signEncryptResponse(Object response, String key) {
        String params = JSONObject.toJSONString(response);
        JSONObject jsonObject = JSON.parseObject(params);
        Map map = JSON.toJavaObject(jsonObject, Map.class);
        SortedMap<Object, Object> parameters = new TreeMap<Object, Object>(map);
        String signData = Sign.getSignData(parameters);
        return Sign.encryptSignURL_SAFE(signData, key);
    }

    /**
     * 从字符串中加载公钥
     *
     * @param publicKeyStr 公钥数据字符串
     * @throws Exception 加载公钥时产生的异常
     */
    public static RSAPublicKey loadPublicKeyByStr(String publicKeyStr)
            throws Exception {
        try {
            byte[] buffer = Base64.getDecoder().decode(publicKeyStr);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(buffer);
            return (RSAPublicKey) keyFactory.generatePublic(keySpec);
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此算法");
        } catch (InvalidKeySpecException e) {
            throw new Exception("公钥非法");
        } catch (NullPointerException e) {
            throw new Exception("公钥数据为空");
        }
    }

    public static RSAPrivateKey loadPrivateKeyByStr(String privateKeyStr)
            throws Exception {
        try {
            byte[] buffer = Base64.getDecoder().decode(privateKeyStr);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(buffer);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此算法");
        } catch (InvalidKeySpecException e) {
            throw new Exception("私钥非法");
        } catch (NullPointerException e) {
            throw new Exception("私钥数据为空");
        }
    }

    /**
     * 从文件中输入流中加载公钥
     *
     * @param path 公钥输入流
     * @throws Exception 加载公钥时产生的异常
     */
    public static String loadPublicKeyByFile(String path) throws Exception {
        try {
            BufferedReader br = new BufferedReader(new FileReader(path));
            String readLine = null;
            StringBuilder sb = new StringBuilder();
            while ((readLine = br.readLine()) != null) {
                sb.append(readLine);
            }
            br.close();
            return sb.toString();
        } catch (IOException e) {
            throw new Exception("公钥数据流读取错误");
        } catch (NullPointerException e) {
            throw new Exception("公钥输入流为空");
        }
    }

    /**
     * 从文件中加载私钥
     *
     * @param path 私钥文件名
     * @return 是否成功
     * @throws Exception
     */
    public static String loadPrivateKeyByFile(String path) throws Exception {
        try {
            BufferedReader br = new BufferedReader(new FileReader(path));
            String readLine = null;
            StringBuilder sb = new StringBuilder();
            while ((readLine = br.readLine()) != null) {
                sb.append(readLine);
            }
            br.close();
            return sb.toString();
        } catch (IOException e) {
            throw new Exception("私钥数据读取错误");
        } catch (NullPointerException e) {
            throw new Exception("私钥输入流为空");
        }
    }

    public static String byte2Base64(byte[] bytes) {
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(bytes);
    }

    //Base64编码转字节数组
    public static byte[] base642Byte(String base64Key) throws IOException {
        BASE64Decoder decoder = new BASE64Decoder();
        return decoder.decodeBuffer(base64Key);
    }

    /**
     * 签名
     *
     * @param data       待签名数据
     * @param privateKey 私钥
     * @return 签名
     */
    public static String signRSA(String data, PrivateKey privateKey) throws Exception {
        byte[] keyBytes = privateKey.getEncoded();
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey key = keyFactory.generatePrivate(keySpec);
        Signature signature = Signature.getInstance(ALGORITHM_SHA1WITHRSA);
        signature.initSign(key);
        signature.update(data.getBytes());
        return new String(byte2Base64(signature.sign())).replaceAll("\\r|\\n", "");
    }

    /**
     * ICBC RSA 加签
     *
     * @param data
     * @return
     */
    public static String signRSAICBC(String data) {
        String result = "";
        try {
            result = signRSA(data, loadPrivateKeyByStr(getSignCertByJarFile(PRIVATE_KEY_RSA)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 验签
     *
     * @param srcData   原始字符串
     * @param publicKey 公钥
     * @param sign      签名
     * @return 是否验签通过
     */
    public static boolean verifyRSA(String srcData, PublicKey publicKey, String sign) throws Exception {
        byte[] keyBytes = publicKey.getEncoded();
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey key = keyFactory.generatePublic(keySpec);
        Signature signature = Signature.getInstance(ALGORITHM_SHA1WITHRSA);
        signature.initVerify(key);
        signature.update(srcData.getBytes());
        return signature.verify(base642Byte(sign));
    }

    /**
     * ICBC RSA 验签
     *
     * @param srcData
     * @return
     */
    public static boolean verifyRSAICBC(String srcData, String sign) {
        boolean result = false;
        try {
            result = verifyRSA(srcData, loadPublicKeyByStr(getSignCertByJarFile(PUBLIC_KEY_RSA)), sign);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static boolean verifyRSA(String srcData, String sign, String keypath) {
        boolean result = false;
        try {
            result = verifyRSA(srcData, loadPublicKeyByStr(getSignCertByJarFile(keypath)), sign);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static String getSignCertByJarFile(String enterpriseCerFilePath) throws IOException {
        InputStreamReader inputStreamReader = null;
        BufferedReader bufferedReader = null;
        try {
            InputStream stream = Sign.class.getClassLoader().getResourceAsStream(enterpriseCerFilePath);
            inputStreamReader = new InputStreamReader(stream); // 建立一个输入流对象reader
            bufferedReader = new BufferedReader(inputStreamReader); // 建立一个对象，它把文件内容转成计算机能读懂的语言
            StringBuilder buff = new StringBuilder();
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                buff.append(line);
                System.out.println(line);
            }
            return buff.toString();
        } catch (IOException ioe) {
            ioe.printStackTrace();
            throw ioe;
        } finally {
            if (null != bufferedReader) bufferedReader.close();
            if (null != inputStreamReader) inputStreamReader.close();
        }
    }

    /**
     * AES加密
     *
     * @param content 明文
     * @return 密文
     */
    public static String encryptAES(String content, String password) {

        try {
            SecretKeySpec skeySpec = new SecretKeySpec(password.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");//"算法/模式/补码方式"
            IvParameterSpec iv = new IvParameterSpec("0000000000000000".getBytes());//使用CBC模式，需要一个向量iv，可增加加密算法的强度
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
            byte[] byteContent = content.getBytes("utf-8");
            byte[] result = cipher.doFinal(byteContent);
            return byte2Base64(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * AES解密
     *
     * @param encryptResultStr 密文
     * @return 明文
     */
    public static String decryptAES(String encryptResultStr, String password) {
        try {

            SecretKeySpec skeySpec = new SecretKeySpec(password.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");//"算法/模式/补码方式"
            IvParameterSpec iv = new IvParameterSpec("0000000000000000".getBytes());//使用CBC模式，需要一个向量iv，可增加加密算法的强度
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            byte[] result = cipher.doFinal(base642Byte(encryptResultStr));
            return new String(result); // 加密
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String generateSignatureAES(String data, String appsecret, String signType) throws Exception {
        SecretKeySpec skeySpec = new SecretKeySpec(appsecret.getBytes("UTF-8"), "AES");
        Cipher cipher = Cipher.getInstance(signType);//"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] byteContent = data.getBytes("UTF-8");
        byte[] result = cipher.doFinal(byteContent);
        return Base64.getEncoder().encodeToString(result);
    }

    public static String decrptSignatureAES(String data, String appsecret, String signType) throws Exception {
        SecretKeySpec skeySpec = new SecretKeySpec(appsecret.getBytes("UTF-8"), "AES");
        Cipher cipher = Cipher.getInstance(signType);//"算法/模式/补码方式"
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        byte[] result = cipher.doFinal(Base64.getDecoder().decode(data));
        return new String(result); // 加密
    }

    public static void main(String[] args) throws Exception {
		/*//私钥加密
		PrivateKey privateKey = loadPrivateKeyByStr("MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDAdhf1c2l7RAQhjXmSn9e25kK1SRwWzfyUEjE2Yhokcu+JfCuRtM52jkQfKow4edFBLK+44RMEySoMa6N91ZI1/iJwu55Wiul+EJqEXAA14KC95Fh6Nu/dNKWUPNa8fcB6nAmTpS4+Gv9WmepHK7KxE/YXPf+ahw0k320FLazJB+OqnrOm/omKjc8yqjkjuxavRNzzSK1QKeC721MPQwUEQSN8oFAUbK30B70WrZVeNGjL2iojO02dVUGTwGp6JzdCHQWVf8BN3IIs6VgfPZrk8pRNLK0oSgZXZ+0kRNvnX3czvfoVG4P3KY9DouZEpPgPacsJFsKRbZDx7VYR6pzBAgMBAAECggEABeobJmHblfwrHFTarZTX7jj5R9TtVg4SewJsHL27C++0SMdWF241AhOxKDwIfxvPQG99HvNbwEPmVOdh6NYNXjdnG4vIe+Rc9JDSDss8/mrexbckuOzDRkr946ZS+wvqRFpTOQRUfXga7LBiMLm5DhQM9f3jwv3ve3iaQxR6QNs9gQREsLGNleOQvneWAz/RF38X3zJaJaYmn1tUnIKR4drblNcTOEJEImHCp95HfxgW5EvnDhQCzS3cy5M4A+q1F5qi/bxuzM5mzRk/W/gBItL1izscmPxWf6Zjsk1NXP6c7OvD4lZ9Oj5cyExL5SImt83peP6eSYGpSYbK/w/D+QKBgQD0eXqdkxr51l2k8/7d27OEEe5Q1VUPFpqRukcOT9wONywVp+RhUspHXDEE13TzUCEWFfsLEu0su6Cl0pP6U51ZNG4JZzZUTa26MZNrM6NKfxL1O/fH4MvxxGhRLDzYdkxUhYVIwLcCSpvOC8E6huUqQ2sXRtbnFbtoArp49X8/9wKBgQDJiOA/I56h0w6HB/hTCkaTdu1hXwDrww4hTx9UyKiAyfACbfIS8LsCmiPe74gH0qP58D3GFseItODYlbM6HIg2vJ8l01len64yDiRLVfKkcInm/MkgwSUrM42MHou+AHVGVKXnuED4jlqGqeOuGgZ+/oE1jNoBmbNUgmHT+kHLBwKBgQCK0gg8PfINeBpItnw8wqqXDg+DOwBHxxASO/JpQBO2xsFzsphOY1KVXtY+55FzMEb5YZXh3edrhUddocuBU8V/h/QLxt3k1Q8XMVuCfhn1DV9YfarfhtiH+n05yqLqT5JcVE0w2K2S0YbTY31fnIW4j/vZhdI1+fMmxpXYZZ7SVQKBgQCF5Oc/a5XX45YSRkxIN1p+XJQHjgvZlwTLQnsJZCr3LbLEmnEq/j7w0qGkWrWgpOSGiIMyGS5F+A4uNv8VS6F/5D9D7S/B+wrLfLPGTXrgn1gvW6DwnwHnut9+yUiEWl+P82sqETMxL7qbH0ICCI5qTwJfJo4sZAZNzjXalLijfwKBgQDjgw8UHb1KadCW5jPg89G1SyiGqEOXGNbBcPdQBUQDUMdeFPcM7Z19/mxSQDMnG3uzWI2ufKR+hkoB2sJe3uV9PcGMsx+S1w1hU+gbzSMxTbalXfmz5LeIw+GqBTlldo3QKE6BlgjE4qIWjIlRoOt2jtZU9eGQnwdomvhLRHE67A==");
		//公钥解密
		PublicKey publicKey = loadPublicKeyByStr("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwHYX9XNpe0QEIY15kp/XtuZCtUkcFs38lBIxNmIaJHLviXwrkbTOdo5EHyqMOHnRQSyvuOETBMkqDGujfdWSNf4icLueVorpfhCahFwANeCgveRYejbv3TSllDzWvH3AepwJk6UuPhr/VpnqRyuysRP2Fz3/mocNJN9tBS2syQfjqp6zpv6Jio3PMqo5I7sWr0Tc80itUCngu9tTD0MFBEEjfKBQFGyt9Ae9Fq2VXjRoy9oqIztNnVVBk8Bqeic3Qh0FlX/ATdyCLOlYHz2a5PKUTSytKEoGV2ftJETb5193M736FRuD9ymPQ6LmRKT4D2nLCRbCkW2Q8e1WEeqcwQIDAQAB");
		JSONObject object =new JSONObject();
		object.put("ca",null);
		object.put("response_biz_content","dlEsQ8SnCifsNKyHe01ts1QeCxZURhnEAotiinFfVioWwAr4N52HB9Ylm6bR43LeQ9h6rZTSXPwBCoWyyqCHb31pg1F6GjaZzElFMuNphax5P0Gh5YuwLErds7SNZRDTDaor6FVEd8NINtzUM2WD2w==");
		object.put("sign_type","RSA");
		String a =signRSA(object.toJSONString(),privateKey);
		System.out.println(a);
		boolean result =verifyRSA(object.toJSONString(),publicKey,"M2o2ycjb4SNQx45Du7nFdsU0Nvt7jtvOCDdy5JYg19eMwPmHEFnHyWSHviRPgjFoCbsTlr6QF4k2RoB5NgeTb8iQDmETGdQj8em5r9YtQ6as2RRedwRMM33nCXyriNcNtoTxVPUAxqeslPUmBhPF88czDonisl96HkP6Wv23txaXf1YWTLXJvBHLLpITBhs/CVHw1FDq0WbSN2jq1z8dTR0mDOOPdap2X3Vyl3TfxHOT2mN7gbkE7yxpI3gf4IbKEq4YWgiz7xQhmIa/JR9cu3iswNDWXi9bvQWD1ev7TLjxa6IEqWmJ2rQjnTFNIm3BSyQG3bKApcCN6/k2r1eVWw==");
		System.out.println(result);

		boolean f = verifyRSA("/icbc/v1/transaction/broadcast?api=/push/audiomessage/push/V1&app_id=10000000000002103001&biz_content=DSqS+t8QNfa9l9bC0B/75ZoxDQyBu5QPLXeUhiwI6xCtBZYJMYrIbTOBsBhOGCkmiISPlWweuOFNakyvBSX2jypGP+EiDbs82AosD3///C7bBfVDWDnG9akWmoG2coEa+zs52rAG8Tl6quzc2mS1S8eVLYAW/PZ6nLku+Dn8YiTjmurU9WB6Q/Dee0ona5hyRAjoULg8Np6NziQAA3BHDIsbLZuEtzxYVHa8qdRQAGrKJKeW5slW9WL66sPPWE2p&charset=utf-8&check_flag=0&encrypt_type=AES&format=json&from=icbc-api&msg_id=8e6c6aa33a70ba9f7a2a7cb462cf7fee&sign_type=RSA&timestamp=2019-12-13 11:54:50",publicKey,"HArkpZQ78czQqUoNTWB4PC/tm+ygg16QApkyB+L2cmju+0aVJlwNK1CA6JFRy5mpvQEAbWkFyb4IgcFxR0rkBnmvXkbPgAHH6R5C2D0W4+4nH/+iKFIjY6uo/m7n8tVOaM8zKNXlUXasskwGisZuwdfQuh4bdLtMh1167U8tg5k=");
		PublicKey publicKey = loadPublicKeyByStr("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCMpjaWjngB4E3ATh+G1DVAmQnIpiPEFAEDqRfNGAVvvH35yDetqewKi0l7OEceTMN1C6NPym3zStvSoQayjYV+eIcZERkx31KhtFu9clZKgRTyPjdKMIth/wBtPKjL/5+PYalLdomM4ONthrPgnkN4x4R0+D4+EBpXo8gNiAFsNwIDAQAB");
		System.out.println(f);
		System.out.println(encryptAES("1","5Wa3pFJsPmjdjxfOsdfZKQ=="));
		System.out.println(decryptAES("hKxzQ+8HpZqIco4e234LRA==","5Wa3pFJsPmjdjxfOsdfZKQ=="));

		IcbcBroadcastResponse response = new IcbcBroadcastResponse();
		response.setResponse_biz_content("dlEsQ8SnCifsNKyHe01ts1QeCxZURhnEAotiinFfVioWwAr4N52HB9Ylm6bR43LeQ9h6rZTSXPwBCoWyyqCHb31pg1F6GjaZzElFMuNphax5P0Gh5YuwLErds7SNZRDTDaor6FVEd8NINtzUM2WD2w==");
		response.setSign_type("RSA");
		String params = JSONObject.toJSONString(response);
		JSONObject jsonObject = JSON.parseObject(params);
		Map map = JSON.toJavaObject(jsonObject, Map.class);
		SortedMap<Object, Object> parameters = new TreeMap<Object, Object>(map);
		String signData = JSON.toJSONString(parameters);
		String resign = signRSA(signData,privateKey);
		System.out.println(resign);*/
		/*JSONObject object =new JSONObject();
		object.put("access_key_id","PSDC_IOT_ACCOUNT_TEST_DEV_XXX");
		object.put("request_id","12jh7yuhtyr54edsr4jkloqih");
		object.put("device_id","**********");
		object.put("request_data","123");
		object.put("push_template","01");
		object.put("nonce","12378987jhgfdriotnmcfjhsdfwlmnja");
		System.out.println(Sign.encryptSign(object));


		//ICBC AES解密
		String content ="DSqS+t8QNfa9l9bC0B/75ZoxDQyBu5QPLXeUhiwI6xDEfWlkLr1nT0cmjegGpSPX+NdPIhH4yjxCEU7t9hI21pBwcGVK2zvFAH9dJv2yKdQK1+SimHVClpJVkt3jdXPXp/nGf1/iiP1wzztIOFbexxvNln28fszDovAoClawRc+88GaXSp72PAvfxxe6NVxgOKgGPewGOPFx20mWqxzRNer4aoWqOLycpLQqrRl4O7T6BJMAJHmLmzLIiIyrmGkD";
		String a =IcbcEncrypt.decryptContent(content,"AES","5Wa3pFJsPmjdjxfOsdfZKQ==","UTF-8");
		System.out.println(a);

		String mac ="QG70apEJ3HkeJSsNRR4vRm8KRQz%2FSt8JdQa%2C6vUFtT7aEMYjTN%2FJIo74F0zbj8mJ7XLPn2Q6paCB%0AeVkA36gwxJEIdfw4crK6mnNkm7rwmQ5ZuXw%2C2yE9WsoG6rcCprdDfIajzK9bAVftcs%2CfZDqloEKS%0AKahqol3Q3YDC8dizpd%2Cy29xYXnWXPnfPMtFlztYhNQwdfFmYVdlLfdMKslkuxVK5sKW8vCypYkW%2C%0AKtfzqIK6XdHWrPh1WxT%2F3e%2FPPo9kuFV%2CPTF2SgLv87j0SVvjugE%2FmyTjLqGg2ovWlhuh88lQOB8x%0AgkriJleUkSwJqdkS72Sy9%2CPuJC6r%2Float0sFGLRb8dkFruety3G7lE2oIqY8zG0QWWXmBGoBXM0%2C%0AlbZA4CmNzcEQ8J4OhS0jQzBkIeZsGCB6cQcSCpFBbaKi5LjQhdnFPqtQiOadNzzSwqLqghN3sSDl%0ABndDuWSZNqPDN44i4aGcxLAHsSu6RtuzqQJY4sX%2FX5V6fA%3D%3D 00000000000011";
		String prikey ="30820272020100300d06092a864886f70d01010105000482025c3082025802010002818053a4797f2fc85b1b72306f327153a5dd21c40ef8d61eaead93e4b1c4492025458cb62d7f7deb6ccdec2ee3540c34a089e227cc8a6949f710d8297edd82d6c2c72a153a1a3204d60c181be94cad12df17d1f42ccdb16cb38ea66781ba42b2d9df113cf4a18fa0c3e7af886db877ad0372227a07893a82f3cb972ae2ed73ffd91d020111028180275c7569076d580cea712544ea093efea67a61660a68caac095c8fe3e62d3eb75146abffc2c92424ab615beb5109b4f5979a4223045f0adac01386fed428d420f2c794a044fe005702ed5ae69976274eb75ee528f9ea948f28fb011f569446a58dc9b919db7ba611bbe8d35ec2f53dadd03f8fd51b68b9b1b1a1c8b7d1cc3ff1024100a200a8bd3c7f5aec25e24fb6ff87a89bfaff7a3b0d0c716b3f58889b41c28f9fd05523501e58c4e4b7a4687d2bf5556455482cbbf59fc22392010855cf55e7c5024100842c758862e9ba670c01386ba77022f4914b0b9b914dc6732ff996bc68f5341f739b27fa7ee13e1d48954411ed6e8afc72aac9286aa4a70e6bb2101106d7e97902405f4bae8d6ee180c72557f2a7e19b17e3485a0baa43e933a87f9d7d8881090930d4e6c97a6c343795993388c219db9ba46e66b0e708f4905128b5503279f64c1902402ea647998c527e064078e6bc9572df2924389aaf6075cd9210eeadabe8cf0356650995a3b44f7f55a1259f8ddb54310dce1e28e1169477324420ba605cc4acc10240525f2236beae06460277af1676a16d9da78fa86ec5c8ef1bba309eb158b6bd5d3f83b86d9d89c92826379261de009c4e6be00c068cd364393ea93288c0f63074";
		String pubkey ="30819c300d06092a864886f70d010101050003818a0030818602818053a4797f2fc85b1b72306f327153a5dd21c40ef8d61eaead93e4b1c4492025458cb62d7f7deb6ccdec2ee3540c34a089e227cc8a6949f710d8297edd82d6c2c72a153a1a3204d60c181be94cad12df17d1f42ccdb16cb38ea66781ba42b2d9df113cf4a18fa0c3e7af886db877ad0372227a07893a82f3cb972ae2ed73ffd91d020111";
		String text = ECipher_Decode_Prim.decode(mac,pubkey);
		System.out.println("text:"+text);*/


        //System.out.println(Sign.encryptSign("UGF5TG9hZD0wLjAxJkRldmljZUNvZGU9U1Q5OTkyMDAyTDNNMDAwMDAxJk1zZ051bT0yMDIwMDQwMzEwMTAyNDQ1NSZQYXlUeXBlPTAwJlNZU19USU1FPTIwMjAwNDAzMTAxMTU2JlNZU19JRD0wMDAwMDAwMDAwMDAwMTE=","F59104DD50C17CF95864A004180209B6"));
        String a = encryptSign("channel=2&cid=commontest&money=0.01&nonce=312345678909876&paidTime=2021-04-23 14:44:20&sn=CTS010000011395&ts=202104231414520", "commontest");
        System.out.println(a);
        System.out.println("y7lOrWjOwD75Y4+Ek8QAy/c9669fnifFoW2OlLk6jAY=");
    }
}
