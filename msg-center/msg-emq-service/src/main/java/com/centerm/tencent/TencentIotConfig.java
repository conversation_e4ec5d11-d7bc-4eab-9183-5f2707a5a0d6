package com.centerm.tencent;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


@Configuration
public class TencentIotConfig {

    public static String accessKeyId ; //密钥ID

    public static String accessKeySecret ; //密钥

    public static String  endpointExplorer;

    public static String regionId ; //区域ID

    @Autowired
    public TencentIotConfig(@Value("${tencent.client.accessKeyId}") String accessKeyId,
                            @Value("${tencent.client.accessKeySecret}") String accessKeySecret,
                            @Value("${tencent.client.regionId}") String regionId,
                            @Value("${tencent.client.endpointExplorer}") String endpointExplorer) {
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.endpointExplorer = endpointExplorer;
        this.regionId = regionId;

    }

}
