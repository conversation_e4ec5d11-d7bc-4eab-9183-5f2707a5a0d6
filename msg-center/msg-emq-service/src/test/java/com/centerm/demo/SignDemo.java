package com.centerm.demo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.centerm.common.config.Global;
import com.centerm.common.json.JsonUtils;
import com.centerm.common.utils.Base64Util;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.MyRSAUtils;
import com.centerm.framework.manager.RedisCacheManager;
import com.centerm.framework.web.domain.server.Sys;
import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

import static com.centerm.mqtt.utils.Sign.base642Byte;
import static com.centerm.mqtt.utils.Sign.byte2Base64;

/**
 * @program: msg-center
 * @description:
 * @author: Zhang Chong
 * @create: 2019/5/23 15:43
 **/
@Slf4j
public class SignDemo {

	public static void main(String[] args) {

		test();

	}


	public static void test() {
        try{
            String appsecert = "88345E92CE454711E8DDF60FB4F49858";          		 					//应用appsecret：commontest
            Map<String,Object> data = new HashMap<String,Object>();					//待签名数据，请参照签名生成流程生成
            data.put("sn","ST9992207L4N251019");
            data.put("money", 0.01);
            //data.put("paidTime","2021-05-28 11:11:11");
            data.put("channel",1);
            data.put("cid","jsnx");
            //data.put("content","这是一笔tts播报");
            data.put("nonce","20230220120602388000023Z");
            data.put("ts","20230220120602");
			//data = JsonUtils.jsonToMap("{\"money\":10.50,\"channel\":2,\"sign\":\"HLtrBXrpcts7i1o+yi8avhaXpDFz/23cdDZiD/2M/iU=\",\"sn\":\"SDYDS019000000001\",\"paidTime\":\"2022-02-19 17:02:34\",\"nonce\":\"123123\",\"content\":\"这是一笔tts播报\",\"cid\":\"commontest\",\"ts\":\"20220219170234\"}");
			//data = JsonUtils.jsonToMap("{\"money\":0.01,\"channel\":1,\"sign\":\"HLtrBXrpcts7i1o+yi8avhaXpDFz/23cdDZiD/2M/iU=\",\"sn\":\"ST9992207L4N251019\",\"nonce\":\"20230220120602388000023Z\",\"cid\":\"jsnx\",\"ts\":\"20230220120602\"}");

			System.out.println("data:"+data.toString());
            String sign = generateSignature(data, appsecert, "HMAC-SHA256");        //生成数据签名
            System.out.println("sign:"+sign);
            data.put("sign",sign);
            System.out.println(JsonUtils.objectToJson(data));
            System.out.println(JSON.toJSONString(data));

        }
        catch(Exception e){
            e.printStackTrace();
        }
    }

	public static String generateSignature(final Map<String,Object> data, String appsecret, String signType) // 签名生成方法
			throws Exception {
		Set keySet = data.keySet();
		String[] keyArray = (String[]) keySet.toArray(new String[keySet.size()]);
		Arrays.sort(keyArray);
		StringBuilder sb = new StringBuilder();
		for (String k : keyArray) {
			if (k.equals("sign")) {
				continue;
			}

			if (data.get(k) == null || "null".equals(data.get(k))) { 				// 参数值为空，则不参与签名
				data.remove(k);
				continue;
			}

			Object value = data.get(k);
			if (value instanceof Integer) {
				value = sb.append(k).append("=").append(value).append("&");
			} else {
				if (String.valueOf(value).trim().length() > 0) {
					sb.append(k).append("=").append(String.valueOf(value).trim()).append("&");
				}
			}
		}
		String sbr = sb.substring(0, sb.length() - 1);
		System.out.println("signStr:"+sbr);
		if ("MD5".equals(signType)) {
			java.security.MessageDigest md = MessageDigest.getInstance("MD5");
			byte[] array = md.digest(sbr.getBytes("UTF-8"));
			return Base64.getUrlEncoder().encodeToString(array);					// 16进制base64形式
		} else if ("HMAC-SHA256".equals(signType)) {
			Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
			SecretKeySpec secret_key = new SecretKeySpec(appsecret.getBytes("UTF-8"), "HmacSHA256");
			sha256_HMAC.init(secret_key);
			byte[] array = sha256_HMAC.doFinal(sbr.getBytes("UTF-8"));
			return Base64.getEncoder().encodeToString(array); 					// 16进制base64形式
		}
		return null;
	}


}
