package com.centerm.mqtt;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/4/9 10:20
 **/
public class Test {
    public static void main(String[] args) {
        String startTime = "1554776818830";
        String endTime = "1554776818888";
        String tempTime = "1554776818869";
        System.out.println(Long.parseLong(endTime)-Long.parseLong(startTime));
    }
}
