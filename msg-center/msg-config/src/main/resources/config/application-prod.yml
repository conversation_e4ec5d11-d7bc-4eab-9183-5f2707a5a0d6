#开发配置
server:
  tomcat:
    basedir: /data/iot/common/files/
spring:
  datasource:
    druid:
      #主数据库配置
      master:
        url: *****************************************************************************************************************************************************************************************************
        username: root
        password: Cpay4@msg123


      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
  redis:
    host: redis
    port: 6379
    password: Cpay4@123
    database: 1

gateway:
  url: http://localhost:18081

gate:
  #接口公网地址
  url: http://localhost:18081
  #文件服务地址（终端）
  fileUrl: https://*************/file/
  #文件服务地址（平台）
  fileUrlPlatform: https://*************/file/
  #fastDfs文件路径
  fastDfsUrl: /download/
  rmi:
    host: 127.0.0.1
    port: 1099
fdfs:
  so-timeout: 1500
  connect-timeout: 600
  thumb-image:
    width: 150
    height: 150
  tracker-list:
    - ************:22122
    #- *************:22122

env:
  profile: test

monitor:
  ynnxURL: http://***************:7074/ynnx/v1/transaction/pushTTS
  ynnxsbCid: ynnxGuard
  zjnxURL: http://***************:7074/push
  zjnxsbCid: zjnxGuard


emq:
  enable: false
  api:
    url: http://node1.emqx.io:8081/
    username: 5ce04687c6a6c
    password: MzIyNTM5MTg1MDAyODc5MzAzMzI2NjE5MDYwNzQzNzAwNDI
    timeout: 3000
    pool:
      # 最大连接数
      maxTotal: 200
      # 设置每个连接的路由数
      defaultMaxPerRoute: 20
  client:
    url: tcp://node1.emqx.io:1883
    username: java_platform
    password: a290aeda1a
    timeout: 10
    keepAliveInterval: 20
    pool:
      # 最大客户端数
      maxClient: 20
  passwordHash: sha256,salt


aliyun:
  client:
    accessKeyId: LTAI5tRzdYGyahBurdjNpSxD
    accessKeySecret: 000
    iotInstanceId: iot-310aaam2
    regionId: ap-southeast-1
  amqp:
    accessKey: LTAI5tRzdYGyahBurdjNpSxD
    accessSecret: 000
    consumerGroupId: DEFAULT_GROUP
    iotInstanceId: iot-310aaam2
    host: iot-310aaam2.amqp.iothub.aliyuncs.com
huaweiiot:
  client:
    regionId: cn-east-3
    endpoint: 2d247cf850.st1.iotda-app.cn-east-3.myhuaweicloud.com
    ak: 000
    sk: 000
    projectId: f8619b9c0c4143c599d2678d80cb2fdc
  amqp:
    accessKey: DVHLjHN5
    accessCode: 0000
    host: 2d247cf850.st1.iotda-app.cn-east-3.myhuaweicloud.com
    port: 5671
    queuePrefech: 1000
    queueName: CPAYIOT-DEVICE-STATUS


tencent:
  client:
    accessKeyId: AKIDOgopdCYhcIlBTvBE7nFTTFnGfvxmQyIO
    accessKeySecret: uc6MVUFm6C6qNgmhQvCH8nERSHsSt9Bz
    endpointExplorer: iotexplorer.ap-bangkok.tencentcloudapi.com
    regionId: ap-bangkok
