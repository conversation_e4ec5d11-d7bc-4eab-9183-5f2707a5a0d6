######### User Permissions ############
user.has.offline=User has been logged out
user.self.not.force=The current logged-in user cannot be forced to log out

######### Business Exceptions ############
dept.has.children=Sub-departments exist, deletion not allowed
dept.has.user=Department has associated users, deletion not allowed
menu.has.children=Sub-menu items exist, deletion not allowed
menu.has.used=Menu is assigned to a role, deletion not allowed
password.old.wrong=Password change failed, old password is incorrect

not.null=*Field must not be empty
user.token.error=User token error
user.jcaptcha.error=Incorrect verification code
user.not.exists=User does not exist or password is incorrect
user.password.not.match=User does not exist or password is incorrect
user.password.retry.limit.count=Invalid password attempt {0} times
user.password.retry.limit.exceed=Invalid password attempt {0} times, account locked
user.password.delete=Apologies, your account has been deleted
user.blocked=User has been blocked, please contact the administrator
role.blocked=Role has been blocked, reason: {0}
user.logout.success=Logged out successfully
user.login.success=Logged in successfully
user.notfound=Please log in again
user.forcelogout=Administrative forced logout, please log in again
user.unknown.error=Unknown error, please log in again
user.unauthorized=Unauthorized
no.permission=You do not have permission for this data, please contact the administrator to add permission [{0}]
no.create.permission=You do not have permission to create data, please contact the administrator to add permission [{0}]
no.update.permission=You do not have permission to modify data, please contact the administrator to add permission [{0}]
no.delete.permission=You do not have permission to delete data, please contact the administrator to add permission [{0}]
no.export.permission=You do not have permission to export data, please contact the administrator to add permission [{0}]
no.view.permission=You do not have permission to view data, please contact the administrator to add permission [{0}]
user.username.not.valid=*Must be 2 to 20 characters in Chinese, letters, digits, or underscores, and must not start with a digit
user.password.not.valid=*5-50 characters
user.email.not.valid=Invalid email format
user.mobile.phone.number.not.valid=Invalid mobile phone number format
admin.not.update=Not allowed to modify super administrator user
admin.not.delete=Not allowed to delete super administrator user

