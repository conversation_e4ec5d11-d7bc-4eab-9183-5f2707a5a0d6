#系统通用错误码
##########系统通用错误码#############
response.success=Operation successful
response.fail=Operation failed
unauthorized =Authorization failed
edit.fail=Data operation failed, possibly data does not exist
response.login=You are not logged in yet or the session has timed out. Please log in again
response.runException=Runtime exception, please contact the administrator
response.exception=Server error, please contact the administrator
response.affected.rows.zero=Operation failed, no rows affected
database.exception=Database operation error! Please contact the administrator
request.not.support=Unsupported request: {0}
request.valid.fail=Invalid parameter: {0}
param.not.null=Parameter {0} cannot be empty
bind.not.delete={0} is assigned and cannot be deleted
param.exist={0} already exists
param.error=Parameter {0} is incorrect, please enter the correct value
param.not.except=Parameter {0} is not valid
json.parse.exception={0} is not in the correct format, JSON parse failed
error={0}
length.not.valid=Length must be between {min} and {max} characters
##File upload messages
upload.exceed.maxSize=The size of the uploaded file exceeds the allowed size! <br/> The maximum allowed file size is: {0}MB!
upload.filename.exceed.length=The uploaded file name can be at most {0} characters long