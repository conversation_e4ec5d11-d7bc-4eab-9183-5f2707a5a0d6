package com.centerm.gateway.config;

import com.centerm.common.constant.EmqConstants;
import com.centerm.mqtt.manager.mqtt.ClientFactory;
import com.centerm.mqtt.manager.mqtt.MqttClientComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.centerm.mqtt.manager.message.MessagePubLishManager;
import javax.annotation.PostConstruct;

@Component
public class EmqCustomInitializer {
    @Autowired
    public EmqCustomInitializer(@Value("${emq.client.url}") String url,
                                @Value("${emq.client.username}") String username,
                                @Value("${emq.client.password}") String password,
                                @Value("${emq.client.timeout}") Integer timeout,
                                @Value("${emq.client.keepAliveInterval}")Integer keepAliveInterval) {
        MqttClientComponent.url = url;
        MqttClientComponent.username = username;
        MqttClientComponent.password = password;
        MqttClientComponent.clientIdPrefix = EmqConstants.EMQ_SUBSCRIBE_CLIENTID_PREFIX;
        MqttClientComponent.timeout = timeout;
        MqttClientComponent.keepAliveInterval = keepAliveInterval;
    }
    @PostConstruct
    public void init() {
        ClientFactory.createDefaultClients();
        MessagePubLishManager.createClients();
    }
}
