<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.centerm</groupId>
    <artifactId>msg-center</artifactId>
    <version>2.0.0</version>

    <name>msg-center</name>
    <description>管理系统框架</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <spring.boot.version>2.1.1.RELEASE</spring.boot.version>
        <maven.war.version>3.0.0</maven.war.version>
        <spring.io.version>Cairo-SR4</spring.io.version>
        <shiro.version>1.7.0</shiro.version>
        <thymeleaf.extras.shiro.version>2.0.0</thymeleaf.extras.shiro.version>
        <pagehelper.boot.version>1.2.5</pagehelper.boot.version>
        <druid.version>1.1.10</druid.version>
        <bitwalker.version>1.19</bitwalker.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <swagger.version>2.9.2</swagger.version>
        <swagger.ui.version>1.8.4</swagger.ui.version>
        <swagger-bootstrap-ui.version>1.8.9</swagger-bootstrap-ui.version>
        <jsoup.version>1.11.3</jsoup.version>
        <fastjson.version>1.2.7</fastjson.version>
        <velocity.version>1.7</velocity.version>
        <jwt.version>3.4.1</jwt.version>
        <mojo.version>2.3</mojo.version>
        <oshi.version>3.9.1</oshi.version>
        <!--TODO mybatis plus 最新版3.1.0引入后运行报错-->
        <mybatis-plus.version>3.0.7.1</mybatis-plus.version>
        <mysql-connector-java.version>6.0.6</mysql-connector-java.version>
        <paho-client-mqttv3.version>1.2.0</paho-client-mqttv3.version>
        <commons-pool2.version>2.4.2</commons-pool2.version>
    </properties>

    <dependencyManagement>

        <dependencies>
            <!-- spring-boot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- spring-io -->
            <dependency>
                <groupId>io.spring.platform</groupId>
                <artifactId>platform-bom</artifactId>
                <version>${spring.io.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 核心框架 -->
            <dependency>
                <groupId>com.centerm</groupId>
                <artifactId>msg-framework</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 通用模块 -->
            <dependency>
                <groupId>com.centerm</groupId>
                <artifactId>msg-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 定时任务 -->
            <dependency>
                <groupId>com.centerm</groupId>
                <artifactId>msg-quartz</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 代码生成 -->
            <dependency>
                <groupId>com.centerm</groupId>
                <artifactId>msg-generator</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 公共模块 -->
            <dependency>
                <groupId>com.centerm</groupId>
                <artifactId>msg-config</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 管理服务 -->
            <dependency>
                <groupId>com.centerm</groupId>
                <artifactId>msg-admin</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 系统模块 -->
            <dependency>
                <groupId>com.centerm</groupId>
                <artifactId>msg-system</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <modules>
        <module>msg-admin</module>
        <module>msg-framework</module>
        <module>msg-system</module>
        <module>msg-quartz</module>
        <module>msg-generator</module>
        <module>msg-common</module>
        <module>msg-config</module>
        <module>msg-gateway</module>
        <module>msg-admin-service</module>
        <module>msg-emq-service</module>
<!--        <module>msg-wx-gate</module>-->
<!--        <module>msg-wx-gate-less</module>-->
        <module>msg-service-sms</module>
<!--        <module>msg-cms</module>-->
        <module>msg-emq-manager</module>
<!--        <module>msg-huaweidev-online</module>-->
        <module>msg-receive</module>
        <module>msg-monitor</module>
<!--        <module>msg-offline</module>-->
<!--        <module>msg-online</module>-->

    </modules>
    <packaging>pom</packaging>

    <dependencies>

        <!-- Spring框架基本的核心工具 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <!-- SpringWeb模块 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.baomidou/mybatis-plus-boot-starter -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>${commons-pool2.version}</version>
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 日志工具类 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>


        <!--对象辅助类 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- swagger2-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger.version}</version>
        </dependency>

        <!-- swagger2-UI-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.version}</version>
        </dependency>
        <!--美化swagger-UI-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <version>${swagger-bootstrap-ui.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/mysql/mysql-connector-java -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${druid.version}</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>
    <profiles>
        <!--开发环境-->
        <profile>
            <id>dev</id>
            <properties>
                <profileActive>dev</profileActive>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!--测试环境-->
        <profile>
            <id>test</id>
            <properties>
                <profileActive>test</profileActive>
            </properties>
        </profile>


        <profile>
            <id>prod</id>
            <properties>
                <profileActive>prod</profileActive>
            </properties>
        </profile>

    </profiles>

    <build>
        <!-- profile对资源的操作 -->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>config/*</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources/config</directory>
                <!-- 是否替换@xx@表示的maven properties属性值 -->
                <filtering>true</filtering>
                <includes>
                    <include>application.yml</include>
                    <include>application-app.yml</include>
                    <include>xmlsign.properties</include>
                    <include>application-${profileActive}.yml</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>${mojo.version}</version>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>


    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>http://192.168.48.66:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>http://192.168.48.66:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>