package com.centerm.gateway.controller.custom;

import com.centerm.common.dto.CustomCommands;
import com.centerm.common.dto.common.GateResponse;
import com.centerm.gateway.controller.custom.aspectj.CustomRequestSecurity;
import com.centerm.gateway.service.CommandDispatcher;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 提供api实现外部对设备的指令操作
 */
@RestController
@RequestMapping("/customer/commands")
public class CommandsController extends BaseController {


    @Resource
    private CommandDispatcher commandDispatcher;

    @CustomRequestSecurity
    @PostMapping()
    @ApiOperation(value = "指令操作下发")
    @ResponseBody
    public GateResponse commands(@RequestBody CustomCommands customCommands) {
        return commandDispatcher.dispatch(customCommands);
    }
}