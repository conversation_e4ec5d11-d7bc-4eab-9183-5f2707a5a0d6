package com.centerm.gateway.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @program: msg-center
 * @description:
 * @author: <PERSON>
 * @create: 2019/5/27 9:34
 **/
@Configuration
public class GateConfig implements WebMvcConfigurer {
//    @Bean
//    public FilterRegistrationBean testFilterRegistration() {
//        FilterRegistrationBean registration = new FilterRegistrationBean(new ParameterFilter());
//        registration.addUrlPatterns("/scrcu/v1/transaction/broadcast",
//                "/scrcu/v1/transaction/broadcast/",
//                "/v1/transaction/dadAlarm",
//                "/v1/transaction/dadAlarm/");
//
//        registration.setName("parameterFilter");
//        return registration;
//    }
//    /**
//     * 通用签名拦截器
//     * @return
//     */
//    @Bean
//    public FilterRegistrationBean FilterRegistrationCommon() {
//        FilterRegistrationBean registration = new FilterRegistrationBean(new CommonParameterFilter());
//        registration.addUrlPatterns("/common/v1/transaction/broadcast", "/common/v1/transaction/broadcast/");
//        registration.addUrlPatterns("/common/v1/transaction/pushTTS", "/common/v1/transaction/pushTTS/");
//        registration.addUrlPatterns("/common/v1/transaction/broadcastWith", "/common/v1/transaction/broadcastWith/");
//        registration.addUrlPatterns("/common/v1/transaction/broadcastStatus", "/common/v1/transaction/broadcastStatus/");
//        registration.addUrlPatterns("/common/v1/print/push", "/common/v1/print/push/");
//        registration.setName("commonParameterFilter");
//        return registration;
//    }
//
//
//    @Override
//    public void addInterceptors(InterceptorRegistry registry) {
//        //registry.addInterceptor(new ParamTestInterceptor());
//        //registry.addInterceptor(new ParameterInterceptor())
//        //       .addPathPatterns("/**")
//        //       .excludePathPatterns("/v1/transaction/received","/v1/command/upgrade","/v1/command/result");
//    }
}
