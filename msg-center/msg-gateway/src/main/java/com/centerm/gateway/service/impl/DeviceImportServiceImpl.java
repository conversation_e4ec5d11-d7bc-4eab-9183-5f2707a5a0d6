package com.centerm.gateway.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.centerm.common.dto.CustomCommands;
import com.centerm.common.dto.common.CommonResponseEnums;
import com.centerm.common.dto.common.GateResponse;
import com.centerm.common.exception.BusinessException;
import com.centerm.common.utils.CommonUtils;
import com.centerm.common.utils.DateUtils;
import com.centerm.gateway.service.CommandsService;
import com.centerm.gateway.service.IDeviceImportService;
import com.centerm.mqtt.enums.CustomCommandEnum;
import com.centerm.mqtt.msg.device.domain.Device;
import com.centerm.mqtt.msg.device.mapper.DeviceMapper;
import com.centerm.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DeviceImportServiceImpl implements IDeviceImportService, CommandsService {
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private DeviceMapper deviceMapper;
    @Override
    public Integer getCommand() {
        return CustomCommandEnum.DEVICE_IMPORT.getCommand();
    }

    @Override
    public GateResponse execute(CustomCommands commands) {
        if(!JSONUtil.isJsonArray(commands.getContent())){
            throw new BusinessException("instruction.format.error" , commands.getCommand());
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String deptId = configService.selectConfigByKey("device_import_default_deptid");
        if (StrUtil.isBlank(deptId)){
            throw new BusinessException("instruction.format.error" , commands.getCommand());
        }
        JSONArray snsJsonArray = JSONUtil.parseArray(commands.getContent());
        if (snsJsonArray.size() > 100){
            throw new BusinessException("limit.exceeded" , commands.getCommand());
        }
        for (int i = 0; i < snsJsonArray.size(); i++) {
            JSONObject json = (JSONObject)snsJsonArray.get(i);
            Device query = deviceMapper.selectBySn(json.getStr("sn"));
            if (query == null) {
                Device device = new Device();
                String id = CommonUtils.createUUID();
                device.setId(id);
                device.setCreateTime(DateUtils.getNowDate());
                device.setSn(json.getStr("sn"));
                device.setDeptId(Integer.parseInt(deptId));
                device.setType("1");
                device.setProducer("centerm");
                device.setModel(json.getStr("model"));
                deviceMapper.insert(device);
                successNum++;
                successMsg.append("<br/>" + successNum + ". Device " + device.getSn() + " imported successfully.");
            }else {
                failureNum++;
                failureMsg.append("<br/>" + failureNum + ". Device " + json.getStr("sn") + " already existed");
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "Sorry, import failed! There are " + failureNum + " records with incorrect format. Details as follows:");
            return GateResponse.success(CommonResponseEnums.UPDATE_SUCCESS.getCode(), failureMsg.toString());
        } else {
            successMsg.insert(0, "Congrats，all import successfully！With " + successNum + " records. Details as follows：");
            return GateResponse.success(CommonResponseEnums.UPDATE_SUCCESS.getCode(), successMsg.toString());
        }

    }
}
