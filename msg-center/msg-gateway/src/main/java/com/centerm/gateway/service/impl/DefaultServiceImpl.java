package com.centerm.gateway.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.centerm.common.dto.CustomCommands;
import com.centerm.common.dto.common.GateResponse;
import com.centerm.common.enums.CommonTopicEnums;
import com.centerm.common.utils.DescConstants;
import com.centerm.common.utils.emq.CommonTopicUtils;
import com.centerm.common.utils.emq.MqttUtils;
import com.centerm.gateway.service.CommandsService;
import com.centerm.gateway.service.IDefaultService;
import com.centerm.mqtt.dto.ContentPayloadRequest;
import com.centerm.mqtt.dto.PublishRequest;
import com.centerm.mqtt.msg.device.domain.Device;
import com.centerm.mqtt.msg.logMsgPush.domain.LogMsgPush;
import com.centerm.mqtt.msg.logMsgPush.mapper.LogMsgPushMapper;
import com.centerm.mqtt.service.IDeviceService;
import com.centerm.mqtt.service.IMessageManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class DefaultServiceImpl implements IDefaultService, CommandsService {
    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private LogMsgPushMapper logMsgPushMapper;

    @Autowired
    private IMessageManagerService messageManagerService;

    private static final Integer QOS_DEFAULT = 1;

    @Override
    public Integer getCommand() {
        return 99_99;
    }

    @Override
    public GateResponse execute(CustomCommands commands) {
        String sn = JSONUtil.parseObj(commands.getContent()).getStr("sn");
        //【验证】终端信息
        Device device = deviceService.validateDevice(commands, sn);

        //【请求】参数构造
        ContentPayloadRequest request = new ContentPayloadRequest();
        String msgId = MqttUtils.createMsgId(CommonTopicEnums.API_COMMAND);
        request.setMsgId(msgId);
        request.setType(commands.getCommand());
        request.setContent(commands.getContent());

        //【构造】消息发送实体类
        PublishRequest publish = new PublishRequest();
        String messageContent = JSON.toJSONString(request);
        publish.setPayload(messageContent);
        publish.setSn(sn);
        publish.setIotType(device.getIotType());
        publish.setProductKey(device.getProductKey());
        publish.setQos(QOS_DEFAULT);
        publish.setRetain(Boolean.FALSE);
        String topicName = CommonTopicUtils.topicConvert(CommonTopicEnums.API_COMMAND, sn);
        publish.setTopic(topicName);
        //【记录】日志
        LogMsgPush logMsgPush = new LogMsgPush();
        logMsgPush.setFunc(DescConstants.CUSTOM_TOPIC);
        logMsgPush.setTarget(device.getSn());
        logMsgPush.setContent(messageContent);
        logMsgPush.setId(msgId);
        logMsgPush.setCreateTime(new Date());
        logMsgPushMapper.insert(logMsgPush);

        messageManagerService.publishMessage(publish);

        return GateResponse.success();
    }
}
