package com.centerm.gateway.controller.custom.aspectj;

public class RequestContext {
    private static InheritableThreadLocal<RequestContexWrapper> protocolThreadLocal = new InheritableThreadLocal<>();

    public static RequestContexWrapper getRequestContextProtocol(){
        return protocolThreadLocal.get();
    }

    public static void setRequestContextProtocol(RequestContexWrapper requestContexWrapper){
        protocolThreadLocal.set(requestContexWrapper);
    }

    public static void clearRequestContextProtocol(){
        protocolThreadLocal.remove();
    }
}
