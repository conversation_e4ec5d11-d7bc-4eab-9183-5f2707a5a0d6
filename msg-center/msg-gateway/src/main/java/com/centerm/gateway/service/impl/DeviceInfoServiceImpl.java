package com.centerm.gateway.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.centerm.common.dto.CustomCommands;
import com.centerm.common.dto.common.GateResponse;
import com.centerm.common.exception.BusinessException;
import com.centerm.gateway.service.CommandsService;
import com.centerm.gateway.service.IDeviceInfoService;
import com.centerm.mqtt.enums.CustomCommandEnum;
import com.centerm.mqtt.msg.device.domain.Device;
import com.centerm.mqtt.msg.device.mapper.DeviceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DeviceInfoServiceImpl implements IDeviceInfoService, CommandsService {
    @Autowired
    private DeviceMapper deviceMapper;
    @Override
    public Integer getCommand() {
        return CustomCommandEnum.DEVICE_INFO.getCommand();
    }

    @Override
    public GateResponse execute(CustomCommands commands) {
        if(!JSONUtil.isJsonArray(commands.getContent())){
            throw new BusinessException("instruction.format.error" , commands.getCommand());
        }
        JSONObject jsonObject = JSONUtil.parseObj(commands.getContent());
        Device device = deviceMapper.selectBySn(jsonObject.getStr("sn"));
        if (device == null){
            throw new BusinessException("validate.device.exception");
        }
        JSONObject respJson = new JSONObject();
        respJson.set("sn", device.getSn());
        respJson.set("networkStatus", device.getNetworkStatus());
        respJson.set("lastLoginTime", device.getLastLoginTime());
        respJson.set("lastLogoutTime", device.getLastLogoutTime());
        respJson.set("activeTime", device.getActiveTime());
        return GateResponse.success(respJson.toString());
    }
}
