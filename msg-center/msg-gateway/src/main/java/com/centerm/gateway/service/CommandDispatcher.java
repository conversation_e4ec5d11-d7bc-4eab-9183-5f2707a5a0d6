package com.centerm.gateway.service;


import com.centerm.common.dto.CustomCommands;
import com.centerm.common.dto.common.GateResponse;
import com.centerm.common.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CommandDispatcher {
    private final Map<Integer, CommandsService> commandServiceMap = new HashMap<>();

    @Autowired
    public CommandDispatcher(List<CommandsService> commandsServices) {
        for (CommandsService service : commandsServices) {
            commandServiceMap.put(service.getCommand(), service);
        }
    }

    public GateResponse dispatch(CustomCommands commands) {
        CommandsService service = commandServiceMap.get(commands.getCommand());
        if (service == null) {
            service = commandServiceMap.get(99_99);
            //throw new BusinessException("unsupported.command" , commands.getCommand());
        }
        return service.execute(commands);
    }
}
