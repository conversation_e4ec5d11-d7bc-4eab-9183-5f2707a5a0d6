<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.generator.mapper.GenMapper">

    <resultMap type="TableInfo" id="TableInfoResult">
        <id property="tableName" column="table_name"/>
        <result property="tableComment" column="table_comment"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="ColumnInfo" id="ColumnInfoResult">
        <id property="columnName" column="column_name"/>
        <result property="dataType" column="data_type"/>
        <result property="columnComment" column="column_comment"/>
    </resultMap>

    <sql id="selectGenVo">
        select table_name, table_comment, create_time, update_time from information_schema.tables
    </sql>

    <select id="selectTableList" parameterType="TableInfo" resultMap="TableInfoResult">
        <include refid="selectGenVo"/>
        where table_comment <![CDATA[ <> ]]> '' and table_schema = (select database())
        <if test="tableName != null and tableName != ''">
            AND table_name like concat('%', #{tableName}, '%')
        </if>
        <if test="tableComment != null and tableComment != ''">
            AND table_comment like concat('%', #{tableComment}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
    </select>

    <select id="selectTableByName" parameterType="String" resultMap="TableInfoResult">
        <include refid="selectGenVo"/>
        where table_comment <![CDATA[ <> ]]> '' and table_schema = (select database())
        and table_name = #{tableName}
    </select>

    <select id="selectTableColumnsByName" parameterType="String" resultMap="ColumnInfoResult">
		select column_name, data_type, column_comment from information_schema.columns
        where table_name = #{tableName} and table_schema = (select database()) order by ordinal_position
	</select>

</mapper> 