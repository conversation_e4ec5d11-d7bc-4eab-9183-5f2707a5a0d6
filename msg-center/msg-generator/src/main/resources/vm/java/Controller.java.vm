package ${package}.controller;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import ${package}.domain.${className};
import ${package}.service.I${className}Service;
import com.centerm.common.annotation.Log;
import com.centerm.common.enums.BusinessType;
import com.centerm.common.page.TableDataInfo;
import com.centerm.common.support.Convert;
import com.centerm.common.utils.poi.ExcelUtil;
import com.centerm.framework.web.base.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * ${tableComment} 信息操作处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Controller
@RequestMapping("/${moduleName}/${classname}")
public class ${className}Controller extends BaseController {
    private String prefix = "${moduleName}/${classname}";

    @Autowired
    private I${className}Service ${classname}Service;

    @RequiresPermissions("${moduleName}:${classname}:view")
    @GetMapping()
    public String ${classname}() {
        return prefix + "/${classname}";
    }

    /**
     * 查询${tableComment}列表
     */
    @RequiresPermissions("${moduleName}:${classname}:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(${className} ${classname}) {
        startPage();
        QueryWrapper<${className}> queryWrapper = new QueryWrapper<>(${classname});
        List<${className}> list = ${classname}Service.list(queryWrapper);
        return getDataTable(list);
    }


    /**
     * 导出${tableComment}列表
     */
    @RequiresPermissions("${moduleName}:${classname}:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(${className} ${classname}) {
        QueryWrapper<${className}> queryWrapper = new QueryWrapper<${className}>(${classname});
        List<${className}> list = ${classname}Service.list(queryWrapper);
        ExcelUtil<${className}> util = new ExcelUtil<${className}>(${className}. class);
        return util.exportExcel(list, "${classname}");
    }

    /**
     * 新增${tableComment}
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存${tableComment}
     */
    @RequiresPermissions("${moduleName}:${classname}:add")
    @Log(title = "${tableComment}", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(${className} ${classname}) {
        return ${classname}Service.save(${classname});
    }

    /**
     * 修改${tableComment}
     */
    @GetMapping("/edit/{${primaryKey.attrname}}")
    public String edit(@PathVariable("${primaryKey.attrname}") ${primaryKey.attrType} ${primaryKey.attrname}, ModelMap mmap) {
        ${className} ${classname} =${classname}Service.getById(${primaryKey.attrname});
        mmap.put("${classname}", ${classname});
        return prefix + "/edit";
    }

    /**
     * 修改保存${tableComment}
     */
    @RequiresPermissions("${moduleName}:${classname}:edit")
    @Log(title = "${tableComment}", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(${className} ${classname}) {
        return ${classname}Service.updateById(${classname});
    }

    /**
     * 删除${tableComment}
     */
    @RequiresPermissions("${moduleName}:${classname}:remove")
    @Log(title = "${tableComment}", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<Integer> lst = Arrays.asList(Convert.toIntArray(ids));
        return ${classname}Service.removeByIds(lst);
    }

}
