# Compiled class files
*.class

# Log files
*.log
/logs/

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# VS Code
.vscode/
.settings/
.classpath
.project

# Mac OS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build output
dist/

# Eclipse
.classpath
.project
.settings/

# NetBeans
nbproject/private/
build/*
nbbuild/
nbdist/
nbactions.xml
nb-configuration.xml